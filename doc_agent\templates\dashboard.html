<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Dashboard - TeamsysV0.1</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .status-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }
        .status-unprocessed { background-color: #6c757d; }
        .status-processed { background-color: #198754; }
        .status-failed { background-color: #dc3545; }
        .status-review { background-color: #fd7e14; }
        
        .email-row {
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .email-row:hover {
            background-color: #f8f9fa;
        }
        
        .sidebar {
            min-height: 100vh;
            background-color: #f8f9fa;
        }
        
        .stats-card {
            border-left: 4px solid;
        }
        .stats-card.total { border-color: #6c757d; }
        .stats-card.processed { border-color: #198754; }
        .stats-card.failed { border-color: #dc3545; }
        .stats-card.review { border-color: #fd7e14; }
        .stats-card.unprocessed { border-color: #0d6efd; }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 sidebar p-4">
                <h4><i class="fas fa-envelope"></i> Email Dashboard</h4>
                <hr>
                
                <!-- Statistics Cards -->
                <div id="stats-container">
                    <div class="card stats-card total mb-2">
                        <div class="card-body p-3">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title mb-0">Total Emails</h6>
                                    <h4 class="mb-0" id="stat-total">0</h4>
                                </div>
                                <i class="fas fa-envelope fa-2x text-muted"></i>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card stats-card processed mb-2">
                        <div class="card-body p-3">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title mb-0">Processed</h6>
                                    <h4 class="mb-0" id="stat-processed">0</h4>
                                </div>
                                <i class="fas fa-check-circle fa-2x text-success"></i>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card stats-card review mb-2">
                        <div class="card-body p-3">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title mb-0">Review</h6>
                                    <h4 class="mb-0" id="stat-review">0</h4>
                                </div>
                                <i class="fas fa-exclamation-triangle fa-2x text-warning"></i>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card stats-card failed mb-2">
                        <div class="card-body p-3">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title mb-0">Failed</h6>
                                    <h4 class="mb-0" id="stat-failed">0</h4>
                                </div>
                                <i class="fas fa-times-circle fa-2x text-danger"></i>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card stats-card unprocessed mb-3">
                        <div class="card-body p-3">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title mb-0">Unprocessed</h6>
                                    <h4 class="mb-0" id="stat-unprocessed">0</h4>
                                </div>
                                <i class="fas fa-clock fa-2x text-primary"></i>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Filters -->
                <div class="mb-3">
                    <label class="form-label">Status Filter</label>
                    <select class="form-select" id="status-filter">
                        <option value="all">All Statuses</option>
                        <option value="unprocessed">Unprocessed</option>
                        <option value="processed">Processed</option>
                        <option value="review">Review</option>
                        <option value="failed">Failed</option>
                    </select>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">Label Filter</label>
                    <select class="form-select" id="label-filter">
                        <option value="all">All Labels</option>
                        <option value="Brady">Brady</option>
                        <option value="RSEA">RSEA</option>
                        <option value="Woolworths">Woolworths</option>
                        <option value="Brierley">Brierley</option>
                        <option value="Gateway">Gateway</option>
                        <option value="Highgate">Highgate</option>
                        <option value="Sitecraft">Sitecraft</option>
                    </select>
                </div>
                
                <!-- Actions -->
                <div class="d-grid gap-2">
                    <button class="btn btn-primary" id="refresh-btn">
                        <i class="fas fa-sync"></i> Refresh Emails
                    </button>
                    <button class="btn btn-success" id="process-selected-btn" disabled>
                        <i class="fas fa-cog"></i> Process Selected
                    </button>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 p-4">
                <!-- Search and Tools -->
                <div class="row mb-4">
                    <div class="col-md-8">
                        <div class="input-group">
                            <input type="text" class="form-control" id="search-input" 
                                   placeholder="Search emails by subject or sender...">
                            <button class="btn btn-outline-secondary" id="search-btn">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-4 text-end">
                        <button class="btn btn-outline-primary" id="select-all-btn">
                            <i class="fas fa-check-square"></i> Select All
                        </button>
                        <button class="btn btn-outline-secondary" id="clear-selection-btn">
                            <i class="fas fa-square"></i> Clear
                        </button>
                    </div>
                </div>
                
                <!-- Email List -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-list"></i> Email List
                            <span class="badge bg-secondary ms-2" id="email-count">0</span>
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th width="40">
                                            <input type="checkbox" id="select-all-checkbox">
                                        </th>
                                        <th>Subject</th>
                                        <th>Sender</th>
                                        <th>Label</th>
                                        <th>Status</th>
                                        <th>Attachments</th>
                                        <th>Date</th>
                                        <th width="100">Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="emails-table-body">
                                    <!-- Emails will be loaded here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <!-- Pagination -->
                <nav class="mt-4">
                    <ul class="pagination justify-content-center" id="pagination">
                        <!-- Pagination will be generated here -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>

    <!-- Email Detail Modal -->
    <div class="modal fade" id="emailDetailModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Email Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="email-detail-content">
                    <!-- Email details will be loaded here -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentPage = 1;
        let selectedEmails = new Set();
        
        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            loadStats();
            loadEmails();
            setupEventListeners();
        });
        
        function setupEventListeners() {
            // Filter changes
            document.getElementById('status-filter').addEventListener('change', () => {
                currentPage = 1;
                loadEmails();
            });
            
            document.getElementById('label-filter').addEventListener('change', () => {
                currentPage = 1;
                loadEmails();
            });
            
            // Search
            document.getElementById('search-btn').addEventListener('click', () => {
                currentPage = 1;
                loadEmails();
            });
            
            document.getElementById('search-input').addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    currentPage = 1;
                    loadEmails();
                }
            });
            
            // Refresh button
            document.getElementById('refresh-btn').addEventListener('click', refreshEmails);
            
            // Process selected button
            document.getElementById('process-selected-btn').addEventListener('click', processSelectedEmails);
            
            // Select all buttons
            document.getElementById('select-all-btn').addEventListener('click', selectAllEmails);
            document.getElementById('clear-selection-btn').addEventListener('click', clearSelection);
            document.getElementById('select-all-checkbox').addEventListener('change', toggleSelectAll);
        }
        
        async function loadStats() {
            try {
                const response = await fetch('/api/stats');
                const stats = await response.json();
                
                document.getElementById('stat-total').textContent = stats.overall.total;
                document.getElementById('stat-processed').textContent = stats.overall.processed;
                document.getElementById('stat-review').textContent = stats.overall.review;
                document.getElementById('stat-failed').textContent = stats.overall.failed;
                document.getElementById('stat-unprocessed').textContent = stats.overall.unprocessed;
            } catch (error) {
                console.error('Error loading stats:', error);
            }
        }
        
        async function loadEmails() {
            try {
                const params = new URLSearchParams({
                    page: currentPage,
                    per_page: 20,
                    status: document.getElementById('status-filter').value,
                    label: document.getElementById('label-filter').value,
                    search: document.getElementById('search-input').value
                });
                
                const response = await fetch(`/api/emails?${params}`);
                const data = await response.json();
                
                displayEmails(data.emails);
                updatePagination(data.page, data.total_pages);
                document.getElementById('email-count').textContent = data.total_count;
            } catch (error) {
                console.error('Error loading emails:', error);
            }
        }
        
        function displayEmails(emails) {
            const tbody = document.getElementById('emails-table-body');
            tbody.innerHTML = '';
            
            emails.forEach(email => {
                const row = document.createElement('tr');
                row.className = 'email-row';
                row.innerHTML = `
                    <td>
                        <input type="checkbox" class="email-checkbox" value="${email.id}">
                    </td>
                    <td>
                        <a href="#" onclick="showEmailDetails('${email.id}')" class="text-decoration-none">
                            ${email.subject}
                        </a>
                    </td>
                    <td>${email.sender || 'Unknown'}</td>
                    <td><span class="badge bg-info">${email.source_label}</span></td>
                    <td>
                        <span class="badge status-badge status-${email.status}">
                            ${email.status.charAt(0).toUpperCase() + email.status.slice(1)}
                        </span>
                    </td>
                    <td>
                        <i class="fas fa-paperclip"></i> ${email.attachment_count}
                    </td>
                    <td>${formatDate(email.timestamp)}</td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="showEmailDetails('${email.id}')">
                                <i class="fas fa-eye"></i>
                            </button>
                            <div class="dropdown">
                                <button class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                    <i class="fas fa-ellipsis-h"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" onclick="markEmailStatus('${email.id}', 'processed')">
                                        <i class="fas fa-check text-success"></i> Mark Processed
                                    </a></li>
                                    <li><a class="dropdown-item" onclick="markEmailStatus('${email.id}', 'review')">
                                        <i class="fas fa-exclamation-triangle text-warning"></i> Mark Review
                                    </a></li>
                                    <li><a class="dropdown-item" onclick="markEmailStatus('${email.id}', 'failed')">
                                        <i class="fas fa-times text-danger"></i> Mark Failed
                                    </a></li>
                                </ul>
                            </div>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });
            
            // Add event listeners to checkboxes
            document.querySelectorAll('.email-checkbox').forEach(checkbox => {
                checkbox.addEventListener('change', updateSelectedEmails);
            });
        }
        
        function updateSelectedEmails() {
            selectedEmails.clear();
            document.querySelectorAll('.email-checkbox:checked').forEach(checkbox => {
                selectedEmails.add(checkbox.value);
            });
            
            document.getElementById('process-selected-btn').disabled = selectedEmails.size === 0;
        }
        
        function updatePagination(currentPage, totalPages) {
            const pagination = document.getElementById('pagination');
            pagination.innerHTML = '';
            
            // Previous button
            const prevBtn = document.createElement('li');
            prevBtn.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
            prevBtn.innerHTML = `<a class="page-link" href="#" onclick="changePage(${currentPage - 1})">Previous</a>`;
            pagination.appendChild(prevBtn);
            
            // Page numbers
            for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) {
                const pageBtn = document.createElement('li');
                pageBtn.className = `page-item ${i === currentPage ? 'active' : ''}`;
                pageBtn.innerHTML = `<a class="page-link" href="#" onclick="changePage(${i})">${i}</a>`;
                pagination.appendChild(pageBtn);
            }
            
            // Next button
            const nextBtn = document.createElement('li');
            nextBtn.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
            nextBtn.innerHTML = `<a class="page-link" href="#" onclick="changePage(${currentPage + 1})">Next</a>`;
            pagination.appendChild(nextBtn);
        }
        
        function changePage(page) {
            currentPage = page;
            loadEmails();
        }
        
        async function refreshEmails() {
            const btn = document.getElementById('refresh-btn');
            btn.disabled = true;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Refreshing...';
            
            try {
                const response = await fetch('/api/refresh-emails', { method: 'POST' });
                const result = await response.json();
                
                if (result.status === 'success') {
                    loadStats();
                    loadEmails();
                    showToast('Emails refreshed successfully', 'success');
                } else {
                    showToast('Error refreshing emails: ' + result.message, 'error');
                }
            } catch (error) {
                showToast('Error refreshing emails: ' + error.message, 'error');
            } finally {
                btn.disabled = false;
                btn.innerHTML = '<i class="fas fa-sync"></i> Refresh Emails';
            }
        }
        
        async function processSelectedEmails() {
            if (selectedEmails.size === 0) return;
            
            const btn = document.getElementById('process-selected-btn');
            btn.disabled = true;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
            
            try {
                const response = await fetch('/api/process-emails', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ email_ids: Array.from(selectedEmails) })
                });
                
                const result = await response.json();
                
                if (result.status === 'success') {
                    showToast(`Processed ${result.results.length} emails`, 'success');
                    loadStats();
                    loadEmails();
                    clearSelection();
                } else {
                    showToast('Error processing emails: ' + result.message, 'error');
                }
            } catch (error) {
                showToast('Error processing emails: ' + error.message, 'error');
            } finally {
                btn.disabled = false;
                btn.innerHTML = '<i class="fas fa-cog"></i> Process Selected';
            }
        }
        
        async function showEmailDetails(emailId) {
            try {
                const response = await fetch(`/api/email/${emailId}`);
                const email = await response.json();
                
                const content = document.getElementById('email-detail-content');
                content.innerHTML = `
                    <div class="row">
                        <div class="col-md-6">
                            <strong>Subject:</strong> ${email.subject}<br>
                            <strong>Sender:</strong> ${email.sender}<br>
                            <strong>Label:</strong> <span class="badge bg-info">${email.source_label}</span><br>
                            <strong>Status:</strong> <span class="badge status-badge status-${email.status}">${email.status}</span><br>
                            <strong>Timestamp:</strong> ${formatDate(email.timestamp)}
                        </div>
                        <div class="col-md-6">
                            <strong>Attachments:</strong> ${email.attachment_count}<br>
                            ${email.attachments ? email.attachments.map(att => `<span class="badge bg-secondary me-1">${att}</span>`).join('') : ''}
                        </div>
                    </div>
                    <hr>
                    <div>
                        <strong>Email Body:</strong>
                        <div class="border p-3 mt-2" style="max-height: 300px; overflow-y: auto; background-color: #f8f9fa;">
                            ${email.body ? email.body.replace(/
/g, '<br>') : 'No body content'}
                        </div>
                    </div>
                    ${email.markdown_file || email.myob_file ? `
                        <hr>
                        <div>
                            <strong>Generated Files:</strong><br>
                            ${email.markdown_file ? `<a href="/api/download/markdown/${email.markdown_file.split('/').pop()}" class="btn btn-sm btn-outline-primary me-2"><i class="fas fa-download"></i> Markdown</a>` : ''}
                            ${email.myob_file ? `<a href="/api/download/myob/${email.myob_file.split('/').pop()}" class="btn btn-sm btn-outline-success"><i class="fas fa-download"></i> MYOB JSON</a>` : ''}
                        </div>
                    ` : ''}
                    ${email.error_message ? `
                        <hr>
                        <div class="alert alert-danger">
                            <strong>Error:</strong> ${email.error_message}
                        </div>
                    ` : ''}
                `;
                
                new bootstrap.Modal(document.getElementById('emailDetailModal')).show();
            } catch (error) {
                showToast('Error loading email details: ' + error.message, 'error');
            }
        }
        
        async function markEmailStatus(emailId, status) {
            try {
                const response = await fetch(`/api/email/${emailId}/mark-status`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ status })
                });
                
                const result = await response.json();
                
                if (result.status === 'success') {
                    showToast(result.message, 'success');
                    loadStats();
                    loadEmails();
                } else {
                    showToast('Error updating status: ' + result.message, 'error');
                }
            } catch (error) {
                showToast('Error updating status: ' + error.message, 'error');
            }
        }
        
        function selectAllEmails() {
            document.querySelectorAll('.email-checkbox').forEach(checkbox => {
                checkbox.checked = true;
            });
            updateSelectedEmails();
        }
        
        function clearSelection() {
            document.querySelectorAll('.email-checkbox').forEach(checkbox => {
                checkbox.checked = false;
            });
            document.getElementById('select-all-checkbox').checked = false;
            updateSelectedEmails();
        }
        
        function toggleSelectAll() {
            const selectAll = document.getElementById('select-all-checkbox').checked;
            document.querySelectorAll('.email-checkbox').forEach(checkbox => {
                checkbox.checked = selectAll;
            });
            updateSelectedEmails();
        }
        
        function formatDate(dateString) {
            try {
                const date = new Date(dateString);
                return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
            } catch {
                return dateString;
            }
        }
        
        function showToast(message, type) {
            // Simple toast notification
            const toast = document.createElement('div');
            toast.className = `alert alert-${type === 'success' ? 'success' : 'danger'} position-fixed`;
            toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            toast.innerHTML = `
                ${message}
                <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
            `;
            document.body.appendChild(toast);
            
            setTimeout(() => {
                if (toast.parentElement) {
                    toast.remove();
                }
            }, 5000);
        }
    </script>
</body>
</html>