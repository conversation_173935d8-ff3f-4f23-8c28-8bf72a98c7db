#!/usr/bin/env python3
"""
Test script for the debtor lookup service
"""

import logging
from debtor_lookup_service import DebtorLookupService

def test_debtor_lookup():
    """Test the debtor lookup service with various email examples."""
    
    # Setup logging
    logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
    
    try:
        # Initialize service
        service = DebtorLookupService(csv_path="docs/active_debtors.csv", threshold=80)
        
        # Test cases based on the CSV data
        test_cases = [
            # Exact domain matches (if we had them)
            ("<EMAIL>", "Woolworths Ordering", "Should match WOOLWORTHS LIMITED"),
            ("<EMAIL>", "Coles Purchasing", "Should match COLES"),
            ("<EMAIL>", "Bunnings Orders", "Should match BUNNINGS - HEAD OFFICE"),
            
            # Fuzzy domain matches
            ("<EMAIL>", "<PERSON>", "Should match BRADY AUSTRALIA PTY LTD"),
            ("<EMAIL>", "Rip Curl Orders", "Should match RIP CURL"),
            ("<EMAIL>", "Myer Purchasing", "Should match MYER LIMITED"),
            
            # Company name in sender
            ("<EMAIL>", "Woolworths Limited", "Should match via sender name"),
            ("<EMAIL>", "Brady Australia", "Should match via sender name"),
            
            # Keyword matching
            ("<EMAIL>", "Storage Systems", "Should match storage companies"),
            ("<EMAIL>", "Bearings Supply", "Should match bearing companies"),
            
            # Should not match
            ("<EMAIL>", "John Smith", "Should not match"),
            ("<EMAIL>", "Unknown Company", "Should not match"),
        ]
        
        print("=== Debtor Lookup Service Test Results ===\n")
        
        # Show service stats first
        stats = service.get_stats()
        print("Service Statistics:")
        for key, value in stats.items():
            print(f"  {key}: {value}")
        print()
        
        # Test each case
        for i, (email, sender, expected) in enumerate(test_cases, 1):
            print(f"Test {i}: {email} (sender: {sender})")
            print(f"Expected: {expected}")
            
            result = service.find_debtor_by_email(email, sender)
            
            if result:
                print(f"✓ Match found:")
                print(f"  Customer: {result['customer_name']}")
                print(f"  Debtor ID: {result['debtor_id']}")
                print(f"  Confidence: {result['confidence_score']}%")
                print(f"  Method: {result['match_method']}")
                print(f"  Matched text: {result['matched_text']}")
                if result['instructions_path']:
                    print(f"  Instructions: {result['instructions_path']}")
            else:
                print("✗ No match found")
            
            print("-" * 60)
        
        # Test search functionality
        print("\n=== Search Functionality Test ===")
        search_queries = ["woolworths", "brady", "storage", "bearings", "coles"]
        
        for query in search_queries:
            print(f"\nSearching for: '{query}'")
            results = service.search_customers(query, limit=5)
            
            if results:
                for j, result in enumerate(results, 1):
                    print(f"  {j}. {result['customer_name']} (ID: {result['debtor_id']}, Score: {result['confidence_score']}%)")
            else:
                print("  No matches found")
        
        print("\n=== Test Complete ===")
        
    except Exception as e:
        print(f"Error during testing: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(test_debtor_lookup())