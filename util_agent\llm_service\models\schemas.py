"""
Data models for the email order processor - Consolidated and corrected to be the single source of truth.
This file merges the contents of the original models.py and llm_service/models/schemas.py to resolve circular dependencies.
"""
from pydantic import BaseModel, Field, validator
from typing import List, Optional, Dict, Any
from datetime import datetime

# --- Core Data Structures ---

class DeliveryAddress(BaseModel):
    """Structured delivery address model for MYOB payloads and internal use."""
    line1: Optional[str] = None
    line2: Optional[str] = None
    line3: Optional[str] = None
    line4: Optional[str] = None
    line5: Optional[str] = None
    line6: Optional[str] = None

class CustomerDetails(BaseModel):
    """Complete customer details model, containing the structured address."""
    debtor_id: Optional[int] = None
    customer_order_number: Optional[str] = None
    customer_name: str
    delivery_address: Optional[DeliveryAddress] = None # CORRECTLY NESTED

class OrderLine(BaseModel):
    """A single line item from an extracted order."""
    stockcode: str
    orderquantity: float

class ExtractedOrder(BaseModel):
    """The primary Pydantic model for validating the output of the LLM's order extraction."""
    customer_details: CustomerDetails
    order_lines: List[OrderLine]
    X_SHIPVIA: Optional[str] = Field(None, description="The shipping method")
    order_status: int = Field(default=0)

    @validator('order_lines')
    def validate_order_lines_not_empty(cls, v):
        if not v:
            raise ValueError("order_lines cannot be empty")
        return v

# --- Models Primarily from the LLM Service Schema ---

class EmailSummary(BaseModel):
    """Model for a brief, actionable summary of an email."""
    summary: str = Field(..., description="Brief summary of email")
    action_required: str = Field(..., description="Required action")

class MYOBPayload(BaseModel):
    """A Pydantic representation of the JSON payload required by the MYOB API."""
    debtorid: int
    customerordernumber: Optional[str] = None
    status: int = 0
    defaultlocationid: int = 1
    lines: List[Dict[str, Any]]
    deliveryaddress: Optional[Dict[str, Optional[str]]] = None
    extrafields: Optional[List[Dict[str, str]]] = None

    @validator('lines')
    def validate_lines(cls, v):
        if not v:
            raise ValueError("lines cannot be empty")
        for line in v:
            if 'stockcode' not in line or 'orderquantity' not in line:
                raise ValueError("Each line must have stockcode and orderquantity")
        return v

class EmailAnalysis(BaseModel):
    """Model for the result of an email analysis/categorization task."""
    is_order: bool = Field(..., description="Whether the email contains an order")
    confidence: float = Field(..., ge=0, le=1, description="Confidence score")
    order_type: Optional[str] = Field(None, description="Type of order identified")
    summary: str = Field(..., description="Summary of the email content")

# --- Models Aligned with Supabase Schema ---

class EmailAttachment(BaseModel):
    """Model for email attachments, matching the Supabase table."""
    id: Optional[str] = None
    email_id: Optional[str] = None
    filename: str
    content_type: Optional[str] = None
    size_bytes: Optional[int] = None
    attachment_id: Optional[str] = None
    processed: bool = False
    extracted_text: Optional[str] = None
    data: Optional[bytes] = None  # Not stored in DB

class EmailData(BaseModel):
    """Model for email data, matching the Supabase 'emails' table schema."""
    id: Optional[str] = None
    gmail_id: str
    subject: str
    sender: str
    recipient: Optional[str] = None
    body: str
    html_body: Optional[str] = None

    thread_id: Optional[str] = None
    label_ids: List[str] = []
    received_date: Optional[datetime] = None
    processed_date: Optional[datetime] = None
    category: Optional[str] = None
    debtor_id: Optional[int] = None
    confidence_score: Optional[float] = None
    has_attachments: bool = False
    attachment_count: int = 0
    attachments: List[EmailAttachment] = []
    source_label: Optional[str] = None

class OrderLineItem(BaseModel):
    """Model for order line items, matching the Supabase 'order_line_items' table."""
    id: Optional[str] = None
    email_id: Optional[str] = None
    erp_payload_id: Optional[str] = None
    line_number: Optional[int] = None
    stock_code: Optional[str] = None
    description: Optional[str] = None
    quantity: Optional[float] = None
    unit_price: Optional[float] = None
    line_total: Optional[float] = None
    unit_of_measure: Optional[str] = None

class EmailCategorization(BaseModel):
    """Model for categorizations, matching the Supabase 'email_categorizations' table."""
    id: Optional[str] = None
    email_id: Optional[str] = None
    category: str
    subcategory: Optional[str] = None
    confidence_score: Optional[float] = None
    extracted_data: Optional[Dict[str, Any]] = None
    model_used: Optional[str] = None
    processing_time_ms: Optional[int] = None

class ERPPayload(BaseModel):
    """Model for ERP payloads, matching the Supabase 'erp_payloads' table."""
    id: Optional[str] = None
    email_id: Optional[str] = None
    debtor_id: int
    payload_type: str
    payload_data: Dict[str, Any]
    status: str = "draft"
    approval_required: bool = True
    approved_by: Optional[str] = None
    approved_at: Optional[datetime] = None
    submitted_at: Optional[datetime] = None
    erp_response: Optional[Dict[str, Any]] = None
    customer_order_number: Optional[str] = None
    po_number: Optional[str] = None
    total_amount: Optional[float] = None

class ProcessedOrder(BaseModel):
    """The final, complete model representing a fully processed email order."""
    email_id: str
    email_subject: str
    email_data: EmailData
    extracted_data: Optional[ExtractedOrder] = None
    markdown_summary: str
    myob_payload: Optional[Dict[str, Any]] = None
    markdown_filepath: Optional[str] = None
    myob_filepath: Optional[str] = None
    erp_payload_id: Optional[str] = None
    categorization_id: Optional[str] = None
    order_line_items: List[OrderLineItem] = []

    class Config:
        arbitrary_types_allowed = True