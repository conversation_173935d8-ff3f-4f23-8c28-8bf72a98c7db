"""
Data models for the email order processor.

This module defines Pydantic models for data validation and structure throughout
the email processing pipeline. All models include comprehensive validation and
type checking to ensure data integrity.

Classes:
    CustomerDetails: Customer and debtor information for MYOB integration
    DeliveryAddress: Structured delivery address information
    OrderLine: Individual order line items with stock codes and quantities
    ExtractedOrder: Complete order structure extracted from emails
    EmailData: Email content and metadata from Gmail
    ProcessedOrder: Final processed order ready for MYOB submission

Example:
    >>> from models import ExtractedOrder, CustomerDetails, OrderLine
    >>> customer = CustomerDetails(debtor_id=6207, customer_order_number="PO123")
    >>> line = OrderLine(stockcode="ABC123", orderquantity=10.0)
    >>> order = ExtractedOrder(
    ...     customer_details=customer,
    ...     order_lines=[line],
    ...     order_status=3
    ... )
"""
from pydantic import BaseModel, Field, validator
from typing import List, Optional
from enum import IntEnum

class OrderStatus(IntEnum):
    """MYOB order status codes."""
    NOT_PROCESSED = 0
    QUOTATION = 3
    STANDING_ORDER = 4
    LAYBY = 5

class CustomerDetails(BaseModel):
    """
    Customer information for MYOB integration.
    
    Attributes:
        debtor_id (int): MYOB debtor ID for the customer
        customer_order_number (Optional[str]): Customer's purchase order number
    
    Example:
        >>> customer = CustomerDetails(debtor_id=6207, customer_order_number="PO-12345")
    """
    debtor_id: int = Field(..., gt=0, description="MYOB debtor ID (must be positive)")
    customer_order_number: Optional[str] = Field(None, description="Customer's order reference number")
    
    @validator('debtor_id')
    def validate_debtor_id(cls, v):
        """Ensure debtor_id is a positive integer."""
        if v <= 0:
            raise ValueError('debtor_id must be a positive integer')
        return v

class DeliveryAddress(BaseModel):
    """
    Structured delivery address information.
    
    Supports up to 6 address lines for comprehensive address formatting.
    Typically used as: line1=street, line2=suburb, line3=state, line4=postcode.
    
    Attributes:
        line1-line6 (Optional[str]): Address lines in order of specificity
    
    Example:
        >>> address = DeliveryAddress(
        ...     line1="123 Main Street",
        ...     line2="Suburb",
        ...     line3="State",
        ...     line4="12345"
        ... )
    """
    line1: Optional[str] = Field(None, max_length=100, description="Primary address line (street)")
    line2: Optional[str] = Field(None, max_length=100, description="Secondary address line (suburb)")
    line3: Optional[str] = Field(None, max_length=50, description="State/region")
    line4: Optional[str] = Field(None, max_length=20, description="Postal/ZIP code")
    line5: Optional[str] = Field(None, max_length=100, description="Additional address information")
    line6: Optional[str] = Field(None, max_length=100, description="Country (if international)")

class OrderLine(BaseModel):
    """
    Individual order line item.
    
    Represents a single product line in an order with stock code and quantity.
    
    Attributes:
        stockcode (str): Product stock code from MYOB inventory
        orderquantity (float): Quantity ordered (supports decimal quantities)
    
    Example:
        >>> line = OrderLine(stockcode="ABC-123", orderquantity=5.5)
    """
    stockcode: str = Field(..., min_length=1, max_length=50, description="Product stock code")
    orderquantity: float = Field(..., gt=0, description="Quantity ordered (must be positive)")
    
    @validator('orderquantity')
    def validate_quantity(cls, v):
        """Ensure quantity is positive."""
        if v <= 0:
            raise ValueError('orderquantity must be positive')
        return v

class ExtractedOrder(BaseModel):
    """
    Complete order structure extracted from emails.
    
    This is the main order model containing all information needed to create
    a sales order in MYOB EXO. Includes customer details, order lines, and
    optional delivery information.
    
    Attributes:
        customer_details (CustomerDetails): Customer and debtor information
        defaultlocationid (int): MYOB location ID for inventory allocation
        order_status (int): Order status code (see OrderStatus enum)
        delivery_address (Optional[DeliveryAddress]): Shipping address if provided
        order_lines (List[OrderLine]): List of order line items
        X_SHIPVIA (Optional[str]): Shipping method or carrier information
    
    Example:
        >>> order = ExtractedOrder(
        ...     customer_details=CustomerDetails(debtor_id=6207),
        ...     order_lines=[OrderLine(stockcode="ABC123", orderquantity=10)],
        ...     order_status=OrderStatus.QUOTATION
        ... )
    """
    customer_details: CustomerDetails = Field(..., description="Customer and debtor information")
    defaultlocationid: int = Field(default=1, ge=1, description="MYOB location ID for inventory")
    order_status: int = Field(
        default=OrderStatus.NOT_PROCESSED, 
        description="Order status: 0=Not Processed, 3=Quotation, 4=Standing Order, 5=Layby"
    )
    delivery_address: Optional[DeliveryAddress] = Field(None, description="Delivery address if specified")
    order_lines: List[OrderLine] = Field(..., min_items=1, description="Order line items (at least one required)")
    X_SHIPVIA: Optional[str] = Field(None, max_length=100, description="Shipping method or carrier")
    
    @validator('order_lines')
    def validate_order_lines(cls, v):
        """Ensure at least one order line exists."""
        if not v:
            raise ValueError('At least one order line is required')
        return v

class EmailData(BaseModel):
    """
    Email data with extracted content from Gmail.
    
    Contains all relevant email information including content, metadata,
    and attachment data for processing.
    
    Attributes:
        id (str): Gmail message ID
        subject (str): Email subject line
        sender (str): Sender email address
        timestamp (str): Email timestamp in ISO format
        body (str): Email body content (HTML or plain text)
        attachments (List[dict]): List of attachment metadata and content
        source_label (str): Gmail label where email was found
    
    Example:
        >>> email = EmailData(
        ...     id="msg123",
        ...     subject="Purchase Order PO-12345",
        ...     sender="<EMAIL>",
        ...     timestamp="2024-01-01T10:00:00Z",
        ...     body="Please find attached purchase order...",
        ...     source_label="Brady"
        ... )
    """
    id: str = Field(..., description="Gmail message ID")
    subject: str = Field(..., description="Email subject line")
    sender: str = Field(..., description="Sender email address")
    timestamp: str = Field(..., description="Email timestamp in ISO format")
    body: str = Field(..., description="Email body content")
    attachments: List[dict] = Field(default_factory=list, description="Attachment metadata and content")
    source_label: str = Field(..., description="Gmail label source")

class ProcessedOrder(BaseModel):
    """
    Complete processed order ready for MYOB submission.
    
    Contains the full processing pipeline results including extracted data,
    generated summaries, MYOB payload, and file paths.
    
    Attributes:
        email_id (str): Source Gmail message ID
        email_subject (str): Original email subject
        extracted_data (ExtractedOrder): Structured order data
        markdown_summary (str): Generated markdown summary
        myob_payload (dict): MYOB-ready JSON payload
        markdown_filepath (Optional[str]): Path to saved markdown file
        myob_filepath (Optional[str]): Path to saved MYOB JSON file
    
    Example:
        >>> processed = ProcessedOrder(
        ...     email_id="msg123",
        ...     email_subject="Purchase Order PO-12345",
        ...     extracted_data=order,
        ...     markdown_summary="# Order Summary...",
        ...     myob_payload={"debtorid": 6207, ...}
        ... )
    """
    email_id: str = Field(..., description="Source Gmail message ID")
    email_subject: str = Field(..., description="Original email subject")
    extracted_data: ExtractedOrder = Field(..., description="Structured order data")
    markdown_summary: str = Field(..., description="Generated markdown summary")
    myob_payload: dict = Field(..., description="MYOB-ready JSON payload")
    markdown_filepath: Optional[str] = Field(None, description="Path to saved markdown file")
    myob_filepath: Optional[str] = Field(None, description="Path to saved MYOB JSON file")
