#!/usr/bin/env python3
"""Test the active customer lookup functionality."""

from supabase_database_service import SupabaseService

def test_active_customers():
    db = SupabaseService()
    
    print("Testing active customer lookup...")
    
    try:
        # Test the active customers method
        active_customers = db.get_active_customers(months_back=24)
        print(f"Found {len(active_customers)} active customers")
        
        # Show first 10 active customers
        print("\nFirst 10 active customers:")
        for i, customer in enumerate(active_customers[:10]):
            print(f"{i+1:2d}. ID: {customer['debtor_id']:>6} | Name: {customer['customer_name']}")
        
        # Test the optimized search for Woolworths
        print("\n" + "="*60)
        print("Testing optimized Woolworths lookup:")
        
        test_names = ["Woolworths", "ALH GROUP", "woolworths"]
        
        for name in test_names:
            print(f"\nSearching for: '{name}'")
            customer = db.find_customer_by_name_optimized(name, use_active_only=True)
            if customer:
                print(f"  ✅ Found: ID {customer['debtor_id']} - {customer['customer_name']}")
            else:
                print(f"  ❌ Not found in active customers")
                
                # Try all customers
                customer = db.find_customer_by_name_optimized(name, use_active_only=False)
                if customer:
                    print(f"  ⚠️  Found in all customers: ID {customer['debtor_id']} - {customer['customer_name']}")
                else:
                    print(f"  ❌ Not found anywhere")
        
    except Exception as e:
        print(f"Error testing active customers: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_active_customers()