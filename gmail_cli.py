#!/usr/bin/env python3
"""
Enhanced Gmail CLI Tool with Rich Logging and Multiple Query Options

A comprehensive command-line interface for Gmail operations with advanced querying,
rich console output, and detailed logging capabilities.
"""

import argparse
import sys
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from pathlib import Path

# Rich imports for enhanced CLI experience
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.prompt import Prompt, Confirm
from rich.logging import RichHandler
from rich.text import Text
from rich.tree import Tree
import logging

# Project imports
from services.gmail_service import GmailService
from config import config

# Setup rich console and logging
console = Console()

# Configure rich logging
logging.basicConfig(
    level=logging.INFO,
    format="%(message)s",
    datefmt="[%X]",
    handlers=[RichHandler(console=console, rich_tracebacks=True)]
)
logger = logging.getLogger(__name__)


class GmailCLI:
    """Enhanced Gmail CLI with rich interface and multiple query options."""
    
    def __init__(self):
        self.gmail_service = None
        self.console = console
        
    def initialize_service(self) -> bool:
        """Initialize Gmail service with rich progress indicator."""
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=self.console
        ) as progress:
            task = progress.add_task("Initializing Gmail service...", total=None)
            try:
                self.gmail_service = GmailService()
                progress.update(task, description="✅ Gmail service initialized")
                return True
            except Exception as e:
                progress.update(task, description=f"❌ Failed to initialize: {e}")
                logger.error(f"Gmail service initialization failed: {e}")
                return False
    
    def build_query(self, args) -> str:
        """Build Gmail search query from CLI arguments."""
        query_parts = []
        
        # Date range
        if args.days:
            after_date = (datetime.now() - timedelta(days=args.days)).strftime('%Y/%m/%d')
            query_parts.append(f"after:{after_date}")
        elif args.after:
            query_parts.append(f"after:{args.after}")
        
        if args.before:
            query_parts.append(f"before:{args.before}")
        
        # Sender/recipient filters
        if args.from_sender:
            query_parts.append(f"from:{args.from_sender}")
        if args.to_recipient:
            query_parts.append(f"to:{args.to_recipient}")
        
        # Subject and content
        if args.subject:
            query_parts.append(f'subject:"{args.subject}"')
        if args.contains:
            query_parts.append(f'"{args.contains}"')
        
        # Attachment filters
        if args.has_attachment:
            query_parts.append("has:attachment")
        if args.attachment_type:
            query_parts.append(f"filename:{args.attachment_type}")
        
        # Read status
        if args.unread_only:
            query_parts.append("is:unread")
        elif args.read_only:
            query_parts.append("-is:unread")
        
        # Labels
        if args.label:
            for label in args.label:
                query_parts.append(f"label:{label}")
        
        # Size filters
        if args.larger_than:
            query_parts.append(f"larger:{args.larger_than}")
        if args.smaller_than:
            query_parts.append(f"smaller:{args.smaller_than}")
        
        # Custom query
        if args.custom_query:
            query_parts.append(args.custom_query)
        
        return " ".join(query_parts) if query_parts else ""
    
    def search_emails(self, query: str, max_results: int = 50) -> List[Dict[str, Any]]:
        """Search emails with the given query."""
        if not self.gmail_service:
            logger.error("Gmail service not initialized")
            return []
        
        try:
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=self.console
            ) as progress:
                task = progress.add_task(f"Searching emails with query: {query}", total=None)
                
                response = self.gmail_service.service.users().messages().list(
                    userId='me',
                    q=query,
                    maxResults=max_results
                ).execute()
                
                messages = response.get('messages', [])
                progress.update(task, description=f"✅ Found {len(messages)} emails")
                
                return messages
                
        except Exception as e:
            logger.error(f"Error searching emails: {e}")
            return []
    
    def get_email_details(self, message_id: str) -> Optional[Dict[str, Any]]:
        """Get detailed email information."""
        if not self.gmail_service:
            return None
        
        try:
            msg = self.gmail_service.service.users().messages().get(
                userId='me',
                id=message_id,
                format='full'
            ).execute()
            
            headers = msg['payload']['headers']
            subject = next((h['value'] for h in headers if h['name'].lower() == 'subject'), 'No Subject')
            sender = next((h['value'] for h in headers if h['name'].lower() == 'from'), 'Unknown')
            date_str = next((h['value'] for h in headers if h['name'].lower() == 'date'), 'Unknown')
            
            # Get labels
            label_ids = msg.get('labelIds', [])
            labels = []
            if label_ids:
                try:
                    all_labels = self.gmail_service.service.users().labels().list(userId='me').execute()
                    label_map = {label['id']: label['name'] for label in all_labels.get('labels', [])}
                    labels = [label_map.get(lid, lid) for lid in label_ids]
                except:
                    labels = label_ids
            
            # Count attachments
            attachment_count = 0
            def count_attachments(parts):
                nonlocal attachment_count
                for part in parts:
                    if part.get('filename'):
                        attachment_count += 1
                    if 'parts' in part:
                        count_attachments(part['parts'])
            
            if 'parts' in msg['payload']:
                count_attachments(msg['payload']['parts'])
            
            return {
                'id': message_id,
                'subject': subject,
                'sender': sender,
                'date': date_str,
                'labels': labels,
                'attachment_count': attachment_count,
                'snippet': msg.get('snippet', ''),
                'size': msg.get('sizeEstimate', 0)
            }
            
        except Exception as e:
            logger.error(f"Error getting email details for {message_id}: {e}")
            return None
    
    def display_emails_table(self, emails: List[Dict[str, Any]], show_details: bool = False):
        """Display emails in a rich table format."""
        if not emails:
            self.console.print("📭 No emails found matching the criteria", style="yellow")
            return
        
        table = Table(title=f"📧 Found {len(emails)} emails")
        table.add_column("ID", style="dim", width=12)
        table.add_column("Subject", style="bold blue", min_width=30)
        table.add_column("From", style="green", width=25)
        table.add_column("Date", style="cyan", width=20)
        
        if show_details:
            table.add_column("Labels", style="magenta", width=20)
            table.add_column("Attachments", style="yellow", width=12)
            table.add_column("Size", style="dim", width=10)
        
        for email in emails:
            row = [
                email['id'][:12] + "...",
                email['subject'][:50] + ("..." if len(email['subject']) > 50 else ""),
                email['sender'][:25] + ("..." if len(email['sender']) > 25 else ""),
                email['date'][:20]
            ]
            
            if show_details:
                labels_str = ", ".join(email.get('labels', [])[:3])
                if len(email.get('labels', [])) > 3:
                    labels_str += "..."
                
                row.extend([
                    labels_str,
                    str(email.get('attachment_count', 0)),
                    f"{email.get('size', 0) // 1024}KB"
                ])
            
            table.add_row(*row)
        
        self.console.print(table)
    
    def display_email_tree(self, emails: List[Dict[str, Any]]):
        """Display emails in a tree format grouped by sender."""
        if not emails:
            self.console.print("📭 No emails found", style="yellow")
            return
        
        # Group by sender
        sender_groups = {}
        for email in emails:
            sender = email['sender']
            if sender not in sender_groups:
                sender_groups[sender] = []
            sender_groups[sender].append(email)
        
        tree = Tree("📧 Emails by Sender")
        
        for sender, sender_emails in sender_groups.items():
            sender_node = tree.add(f"👤 {sender} ({len(sender_emails)} emails)")
            
            for email in sender_emails[:10]:  # Limit to 10 per sender
                subject = email['subject'][:50] + ("..." if len(email['subject']) > 50 else "")
                email_node = sender_node.add(f"📄 {subject}")
                email_node.add(f"🕒 {email['date']}")
                if email.get('attachment_count', 0) > 0:
                    email_node.add(f"📎 {email['attachment_count']} attachments")
        
        self.console.print(tree)
    
    def interactive_email_browser(self, emails: List[Dict[str, Any]]):
        """Interactive email browser with rich interface."""
        if not emails:
            self.console.print("📭 No emails to browse", style="yellow")
            return
        
        current_index = 0
        
        while True:
            email = emails[current_index]
            
            # Create email panel
            email_info = f"""
📧 Subject: {email['subject']}
👤 From: {email['sender']}
🕒 Date: {email['date']}
📎 Attachments: {email.get('attachment_count', 0)}
📏 Size: {email.get('size', 0) // 1024}KB
🏷️  Labels: {', '.join(email.get('labels', [])[:5])}

📝 Snippet: {email.get('snippet', 'No preview available')}
            """.strip()
            
            panel = Panel(
                email_info,
                title=f"Email {current_index + 1} of {len(emails)}",
                border_style="blue"
            )
            
            self.console.clear()
            self.console.print(panel)
            
            # Navigation options
            options = [
                "[n] Next email",
                "[p] Previous email",
                "[d] Show details",
                "[l] List all labels",
                "[m] Mark as read/unread",
                "[q] Quit browser"
            ]
            
            self.console.print("\n" + " | ".join(options), style="dim")
            
            choice = Prompt.ask("Choose action", choices=["n", "p", "d", "l", "m", "q"], default="n")
            
            if choice == "n":
                current_index = (current_index + 1) % len(emails)
            elif choice == "p":
                current_index = (current_index - 1) % len(emails)
            elif choice == "d":
                self.show_detailed_email(email['id'])
                Prompt.ask("Press Enter to continue")
            elif choice == "l":
                self.show_email_labels(email['id'])
                Prompt.ask("Press Enter to continue")
            elif choice == "m":
                self.toggle_read_status(email['id'])
            elif choice == "q":
                break
    
    def show_detailed_email(self, message_id: str):
        """Show detailed email information."""
        email_data = self.gmail_service._get_email_details(message_id, "CLI")
        if not email_data:
            self.console.print("❌ Failed to get email details", style="red")
            return
        
        details = f"""
📧 Subject: {email_data.subject}
👤 From: {email_data.sender}
🕒 Timestamp: {email_data.timestamp}
📎 Attachments: {len(email_data.attachments)}

📝 Body Preview:
{email_data.body[:500]}{'...' if len(email_data.body) > 500 else ''}
        """.strip()
        
        if email_data.attachments:
            details += "\n\n📎 Attachments:\n"
            for att in email_data.attachments:
                details += f"  • {att['filename']} ({att['mime_type']})\n"
        
        panel = Panel(details, title="Email Details", border_style="green")
        self.console.print(panel)
    
    def show_email_labels(self, message_id: str):
        """Show all labels for an email."""
        try:
            msg = self.gmail_service.service.users().messages().get(
                userId='me',
                id=message_id,
                format='metadata'
            ).execute()
            
            label_ids = msg.get('labelIds', [])
            if not label_ids:
                self.console.print("🏷️  No labels found", style="yellow")
                return
            
            # Get label names
            all_labels = self.gmail_service.service.users().labels().list(userId='me').execute()
            label_map = {label['id']: label['name'] for label in all_labels.get('labels', [])}
            
            labels_table = Table(title="🏷️  Email Labels")
            labels_table.add_column("Label ID", style="dim")
            labels_table.add_column("Label Name", style="bold")
            
            for label_id in label_ids:
                label_name = label_map.get(label_id, "Unknown")
                labels_table.add_row(label_id, label_name)
            
            self.console.print(labels_table)
            
        except Exception as e:
            logger.error(f"Error getting labels: {e}")
    
    def toggle_read_status(self, message_id: str):
        """Toggle read/unread status of an email."""
        try:
            # Check current status
            msg = self.gmail_service.service.users().messages().get(
                userId='me',
                id=message_id,
                format='metadata'
            ).execute()
            
            label_ids = msg.get('labelIds', [])
            is_unread = 'UNREAD' in label_ids
            
            if is_unread:
                # Mark as read
                self.gmail_service.service.users().messages().modify(
                    userId='me',
                    id=message_id,
                    body={'removeLabelIds': ['UNREAD']}
                ).execute()
                self.console.print("✅ Marked as read", style="green")
            else:
                # Mark as unread
                self.gmail_service.service.users().messages().modify(
                    userId='me',
                    id=message_id,
                    body={'addLabelIds': ['UNREAD']}
                ).execute()
                self.console.print("📬 Marked as unread", style="blue")
                
        except Exception as e:
            logger.error(f"Error toggling read status: {e}")
    
    def run_query_command(self, args):
        """Execute the query command."""
        if not self.initialize_service():
            return
        
        # Build and display query
        query = self.build_query(args)
        if not query:
            query = "in:inbox"  # Default query
        
        query_panel = Panel(
            f"🔍 Search Query: {query}",
            title="Gmail Search",
            border_style="blue"
        )
        self.console.print(query_panel)
        
        # Search emails
        messages = self.search_emails(query, args.max_results)
        if not messages:
            return
        
        # Get detailed information
        emails = []
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=self.console
        ) as progress:
            task = progress.add_task("Fetching email details...", total=len(messages))
            
            for msg in messages:
                email_details = self.get_email_details(msg['id'])
                if email_details:
                    emails.append(email_details)
                progress.advance(task)
        
        # Display results
        if args.format == 'table':
            self.display_emails_table(emails, args.detailed)
        elif args.format == 'tree':
            self.display_email_tree(emails)
        elif args.format == 'interactive':
            self.interactive_email_browser(emails)
    
    def run_labels_command(self, args):
        """Execute the labels command."""
        if not self.initialize_service():
            return
        
        try:
            response = self.gmail_service.service.users().labels().list(userId='me').execute()
            labels = response.get('labels', [])
            
            if args.create:
                # Create new label
                label_id = self.gmail_service.create_label(args.create)
                if label_id:
                    self.console.print(f"✅ Created label '{args.create}' with ID: {label_id}", style="green")
                else:
                    self.console.print(f"❌ Failed to create label '{args.create}'", style="red")
                return
            
            # Display labels
            labels_table = Table(title="📋 Gmail Labels")
            labels_table.add_column("Name", style="bold")
            labels_table.add_column("ID", style="dim")
            labels_table.add_column("Type", style="cyan")
            labels_table.add_column("Messages", style="yellow")
            
            for label in sorted(labels, key=lambda x: x['name']):
                label_type = label.get('type', 'user')
                message_count = label.get('messagesTotal', 'N/A')
                
                labels_table.add_row(
                    label['name'],
                    label['id'],
                    label_type,
                    str(message_count)
                )
            
            self.console.print(labels_table)
            
        except Exception as e:
            logger.error(f"Error listing labels: {e}")


def create_parser() -> argparse.ArgumentParser:
    """Create the argument parser with all CLI options."""
    parser = argparse.ArgumentParser(
        description="Enhanced Gmail CLI with Rich Interface",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Search emails from last 7 days with attachments
  python gmail_cli.py query --days 7 --has-attachment
  
  # Search unread emails from specific sender
  python gmail_cli.py query --from-sender "<EMAIL>" --unread-only
  
  # Interactive browser for emails with PDFs
  python gmail_cli.py query --attachment-type pdf --format interactive
  
  # Search emails with custom query
  python gmail_cli.py query --custom-query "has:attachment larger:1M"
  
  # List all labels
  python gmail_cli.py labels
  
  # Create a new label
  python gmail_cli.py labels --create "New Orders"
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Query command
    query_parser = subparsers.add_parser('query', help='Search and display emails')
    
    # Date filters
    date_group = query_parser.add_argument_group('Date Filters')
    date_group.add_argument('--days', type=int, help='Search emails from last N days')
    date_group.add_argument('--after', help='Search emails after date (YYYY/MM/DD)')
    date_group.add_argument('--before', help='Search emails before date (YYYY/MM/DD)')
    
    # Sender/recipient filters
    people_group = query_parser.add_argument_group('People Filters')
    people_group.add_argument('--from-sender', help='Filter by sender email/domain')
    people_group.add_argument('--to-recipient', help='Filter by recipient email/domain')
    
    # Content filters
    content_group = query_parser.add_argument_group('Content Filters')
    content_group.add_argument('--subject', help='Filter by subject line')
    content_group.add_argument('--contains', help='Filter by email content')
    
    # Attachment filters
    attachment_group = query_parser.add_argument_group('Attachment Filters')
    attachment_group.add_argument('--has-attachment', action='store_true', help='Only emails with attachments')
    attachment_group.add_argument('--attachment-type', help='Filter by attachment type (pdf, doc, xlsx, etc.)')
    
    # Status filters
    status_group = query_parser.add_argument_group('Status Filters')
    status_group.add_argument('--unread-only', action='store_true', help='Only unread emails')
    status_group.add_argument('--read-only', action='store_true', help='Only read emails')
    
    # Label filters
    label_group = query_parser.add_argument_group('Label Filters')
    label_group.add_argument('--label', action='append', help='Filter by label (can be used multiple times)')
    
    # Size filters
    size_group = query_parser.add_argument_group('Size Filters')
    size_group.add_argument('--larger-than', help='Emails larger than size (e.g., 1M, 500K)')
    size_group.add_argument('--smaller-than', help='Emails smaller than size (e.g., 1M, 500K)')
    
    # Advanced options
    advanced_group = query_parser.add_argument_group('Advanced Options')
    advanced_group.add_argument('--custom-query', help='Custom Gmail search query')
    advanced_group.add_argument('--max-results', type=int, default=50, help='Maximum number of results (default: 50)')
    
    # Display options
    display_group = query_parser.add_argument_group('Display Options')
    display_group.add_argument('--format', choices=['table', 'tree', 'interactive'], default='table',
                              help='Output format (default: table)')
    display_group.add_argument('--detailed', action='store_true', help='Show detailed information in table')
    
    # Labels command
    labels_parser = subparsers.add_parser('labels', help='Manage Gmail labels')
    labels_parser.add_argument('--create', help='Create a new label')
    
    return parser


def main():
    """Main CLI entry point."""
    parser = create_parser()
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # Create CLI instance
    cli = GmailCLI()
    
    # Welcome message
    console.print(Panel.fit(
        "📧 Enhanced Gmail CLI Tool\n"
        "Rich interface for Gmail operations",
        style="bold blue"
    ))
    
    try:
        if args.command == 'query':
            cli.run_query_command(args)
        elif args.command == 'labels':
            cli.run_labels_command(args)
    except KeyboardInterrupt:
        console.print("\n👋 Goodbye!", style="yellow")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        console.print(f"❌ An error occurred: {e}", style="red")


if __name__ == "__main__":
    main()