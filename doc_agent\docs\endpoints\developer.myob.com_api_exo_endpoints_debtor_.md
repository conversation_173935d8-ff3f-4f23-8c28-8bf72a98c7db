---
url: "https://developer.myob.com/api/exo/endpoints/debtor/"
title: "Debtor"
---

[![](https://developer.myob.com/media/cms_page_media/23/myob_logo_small.png)](http://www.myob.com/)

# Debtor

Return, update and create a debtor.

**Date Released:** Nov 1st 2013 **Date Updated:** November 14th 2014

| URL | Supports |
| --- | --- |
| {URI}/debtor/<br>{URI}/debtor/search?q={query}<br>{URI}/debtor/{debtorid}/contact<br>{URI}/debtor/convertprospect | [GET](https://developer.myob.com/api/exo/endpoints/debtor/#GET) \| <br>[PUT](https://developer.myob.com/api/exo/endpoints/debtor/#PUT) \| <br>[POST](https://developer.myob.com/api/exo/endpoints/debtor/#POST)<br>[GET](https://developer.myob.com/api/exo/endpoints/debtor/#GET) \| <br>[PUT](https://developer.myob.com/api/exo/endpoints/debtor/#PUT) \| <br>[POST](https://developer.myob.com/api/exo/endpoints/debtor/#POST)<br>[GET](https://developer.myob.com/api/exo/endpoints/debtor/#GET) \| <br>[PUT](https://developer.myob.com/api/exo/endpoints/debtor/#PUT) \| <br>[POST](https://developer.myob.com/api/exo/endpoints/debtor/#POST)<br>[GET](https://developer.myob.com/api/exo/endpoints/debtor/#GET) \| <br>[PUT](https://developer.myob.com/api/exo/endpoints/debtor/#PUT) \| <br>[POST](https://developer.myob.com/api/exo/endpoints/debtor/#POST) |
| {URI} is exo.api.myob.com when connecting to the cloud or the address of the machine hosting the API when connecting on premise. |

**Note:** If a custom filter has been defined using the **Debtors custom filter SQL** profile setting, this will be applied when returning Debtor records via a GET operation.

`search` returns debtors that match the specified search string.

`contact` returns a list of contacts associated with the specified debtor account. You can also remove the association between a Debtor and a contact with a DELETE call to this endpoint, e.g. a DELETE call to `{URI}/debtor/21/contact/3` will remove the association between Debtor 21 and contact 3 (but will not delete the contact itself).

**Note:** The **debtor/contact** endpoint cannot be used to GET, PUT or POST to individual contacts; it can only return the full list of contacts for, e.g. a GET or PUT call to **{URI}/debtor/21/contact/3** will not work.

`convertprospect` converts the specified prospect (non account) to a debtor account. The ID of the prospect to be converted should be passed in in the body of the request.

ID fields are required when updating a debtor (PUT). Account Name is the only field required when creating a new debtor (POST). The API will retrieve the information relating to a specific Id so you have all the information available.

**Note:** Actual fields returned may differ slightly depending on local settings and configuration.

The elements list below details information for Debtor. To view the descriptions for the elements you can either hover any attribute to reveal details [or click here to show all details inline.](https://developer.myob.com/api/exo/endpoints/debtor/#reveal)

#### Attribute Details

- basepriceid integer,null
- Required on PUT
- baseprice object,null
- Type: object,null
  - name string,null
  - Type: string,null
  - rel string,null
  - Type: string,null
  - title string,null
  - Type: string,null
  - id integer
  - Required on PUT
  - href string,null
  - Type: string,null
- needorderno boolean
- Type: boolean
- allowrestrictedstock boolean,null
- Type: boolean,null
- active boolean,null
- Type: boolean,null
- privateaccount boolean,null
- Type: boolean,null
- alphacode string,null
- Type: string,null
- alert string,null
- Type: string,null
- creditlimit number
- Type: number
- currencyid integer,null
- Required on PUT
- currency object,null
- Type: object,null
  - code string,null
  - Type: string,null
  - name string,null
  - Type: string,null
  - symbol string,null
  - Type: string,null
  - buyrate number
  - Type: number
  - sellrate number
  - Type: number
  - rel string,null
  - Type: string,null
  - title string,null
  - Type: string,null
  - id integer
  - Required on PUT
  - href string,null
  - Type: string,null
- credittermid integer,null
- Required on PUT
- credittermwarningtext string,null
- Type: string,null
- creditterm object,null
- Type: object,null
  - description string,null
  - Type: string,null
  - activefordebtors boolean
  - Type: boolean
  - activeforcreditors boolean
  - Type: boolean
  - rel string,null
  - Type: string,null
  - title string,null
  - Type: string,null
  - id integer
  - Required on PUT
  - href string,null
  - Type: string,null
- primarygroupid integer,null
- Required on PUT
- primarygroup object,null
- Type: object,null
  - companytype integer
  - Type: integer
  - accountgrouptype integer
  - Type: integer
  - name string,null
  - Type: string,null
  - reportcode string,null
  - Type: string,null
  - rel string,null
  - Type: string,null
  - title string,null
  - Type: string,null
  - id integer
  - Required on PUT
  - href string,null
  - Type: string,null
- secondarygroupid integer,null
- Required on PUT
- secondarygroup object,null
- Type: object,null
  - companytype integer
  - Type: integer
  - accountgrouptype integer
  - Type: integer
  - name string,null
  - Type: string,null
  - reportcode string,null
  - Type: string,null
  - rel string,null
  - Type: string,null
  - title string,null
  - Type: string,null
  - id integer
  - Required on PUT
  - href string,null
  - Type: string,null
- stopcredit boolean,null
- Type: boolean,null
- agedbal0 number
- Type: number
- agedbal1 number
- Type: number
- agedbal2 number
- Type: number
- agedbal3 number
- Type: number
- monthval number
- Type: number
- lastmonth number
- Type: number
- yearval number
- Type: number
- lastyear number
- Type: number
- headaccno integer
- Type: integer
- companytype integer
- Type: integer
- accountname string
- Type: string
- email string,null
- Type: string,null
- phone string,null
- Type: string,null
- postaladdress object
- Type: object
  - line1 string,null
  - Type: string,null
  - line2 string,null
  - Type: string,null
  - line3 string,null
  - Type: string,null
  - line4 string,null
  - Type: string,null
  - line5 string,null
  - Type: string,null
- postalcode string,null
- Type: string,null
- deliveryaddress object
- Type: object
  - line1 string,null
  - Type: string,null
  - line2 string,null
  - Type: string,null
  - line3 string,null
  - Type: string,null
  - line4 string,null
  - Type: string,null
  - line5 string,null
  - Type: string,null
  - line6 string,null
  - Type: string,null
- defaultcontactid integer,null
- Required on PUT
- defaultcontact object,null
- EXO Business Contact

Title: Contact
  - jobtitle string,null
  - Type: string,null
  - firstname string,null
  - Type: string,null
  - lastname string,null
  - Type: string,null
  - fullname string,null
  - Type: string,null
  - directphonenumber string,null
  - Type: string,null
  - mobilephonenumber string,null
  - Type: string,null
  - email string,null
  - Type: string,null
  - postaladdress object
  - Type: object
    - line1 string,null
    - Type: string,null
    - line2 string,null
    - Type: string,null
    - line3 string,null
    - Type: string,null
    - line4 string,null
    - Type: string,null
    - line5 string,null
    - Type: string,null
  - postalcode string,null
  - Type: string,null
  - deliveryaddress object
  - Type: object
    - line1 string,null
    - Type: string,null
    - line2 string,null
    - Type: string,null
    - line3 string,null
    - Type: string,null
    - line4 string,null
    - Type: string,null
    - line5 string,null
    - Type: string,null
    - line6 string,null
    - Type: string,null
  - advertsourceid integer,null
  - Required on PUT
  - advertsource object,null
  - Type: object,null
    - description string,null
    - Type: string,null
    - processid integer
    - Required on PUT
    - communicationprocess object,null
    - Type: object,null
      - description string,null
      - Type: string,null
      - rel string,null
      - Type: string,null
      - title string,null
      - Type: string,null
      - id integer
      - Required on PUT
      - href string,null
      - Type: string,null
    - rel string,null
    - Type: string,null
    - title string,null
    - Type: string,null
    - id integer
    - Required on PUT
    - href string,null
    - Type: string,null
  - active boolean,null
  - Type: boolean,null
  - optoutemarketing boolean,null
  - Type: boolean,null
  - salespersonid integer,null
  - Required on PUT
  - salesperson object,null
  - Type: object,null
    - name string,null
    - Type: string,null
    - jobtitle string,null
    - Type: string,null
    - rel string,null
    - Type: string,null
    - title string,null
    - Type: string,null
    - id integer
    - Required on PUT
    - href string,null
    - Type: string,null
  - defaultcompany object,null
  - EXO Business Company

    Title: BaseCompany
    - companytype integer
    - Type: integer
    - accountname string
    - Type: string
    - email string,null
    - Type: string,null
    - phone string,null
    - Type: string,null
    - postaladdress object
    - Type: object
      - line1 string,null
      - Type: string,null
      - line2 string,null
      - Type: string,null
      - line3 string,null
      - Type: string,null
      - line4 string,null
      - Type: string,null
      - line5 string,null
      - Type: string,null
    - postalcode string,null
    - Type: string,null
    - deliveryaddress object
    - Type: object
      - line1 string,null
      - Type: string,null
      - line2 string,null
      - Type: string,null
      - line3 string,null
      - Type: string,null
      - line4 string,null
      - Type: string,null
      - line5 string,null
      - Type: string,null
      - line6 string,null
      - Type: string,null
    - defaultcontactid integer,null
    - Required on PUT
    - defaultcontact undefined
    - Type: undefined
    - contacts object,null
    - Type: object,null
      - rel string,null
      - Type: string,null
      - title string,null
      - Type: string,null
      - href string,null
      - Type: string,null
    - website string,null
    - Type: string,null
    - salespersonid integer,null
    - Required on PUT
    - salesperson object,null
    - Type: object,null
      - name string,null
      - Type: string,null
      - jobtitle string,null
      - Type: string,null
      - rel string,null
      - Type: string,null
      - title string,null
      - Type: string,null
      - id integer
      - Required on PUT
      - href string,null
      - Type: string,null
    - balance number
    - Type: number
    - contactname string,null
    - Type: string,null
    - latitude number,null
    - Type: number,null
    - longitude number,null
    - Type: number,null
    - geocodestatus integer,null
    - Type: integer,null
    - lastupdated string,null
    - Type: string,null
    - extrafields array,null
    - Type: array,null
    - rel string,null
    - Type: string,null
    - title string,null
    - Type: string,null
    - id integer
    - Required on PUT
    - href string,null
    - Type: string,null
  - defaultcompanyid object,null
  - EXO Business Company Link

    Title: CompanyId

    Required on PUT
    - companytype integer
    - Type: integer
    - accno integer
    - Type: integer
  - defaultcompanyname string,null
  - Type: string,null
  - latitude number,null
  - Type: number,null
  - longitude number,null
  - Type: number,null
  - geocodestatus integer,null
  - Type: integer,null
  - extrafields array,null
  - Type: array,null
  - rel string,null
  - Type: string,null
  - title string,null
  - Type: string,null
  - id integer
  - Required on PUT
  - href string,null
  - Type: string,null
- contacts object,null
- Type: object,null
  - rel string,null
  - Type: string,null
  - title string,null
  - Type: string,null
  - href string,null
  - Type: string,null
- website string,null
- Type: string,null
- salespersonid integer,null
- Required on PUT
- salesperson object,null
- Type: object,null
  - name string,null
  - Type: string,null
  - jobtitle string,null
  - Type: string,null
  - rel string,null
  - Type: string,null
  - title string,null
  - Type: string,null
  - id integer
  - Required on PUT
  - href string,null
  - Type: string,null
- balance number
- Type: number
- contactname string,null
- Type: string,null
- latitude number,null
- Type: number,null
- longitude number,null
- Type: number,null
- geocodestatus integer,null
- Type: integer,null
- lastupdated string,null
- Type: string,null
- extrafields array,null
- Type: array,null
- rel string,null
- Type: string,null
- title string,null
- Type: string,null
- id integer
- Required on PUT
- href string,null
- Type: string,null

#### Example json GET response

- {
  - basepriceid : 7
  - needorderno : false
  - allowrestrictedstock : true
  - active : true
  - privateaccount : false
  - alphacode : KNINIC01
  - alert
  - creditlimit : 0
  - currencyid : 1
  - credittermid : 2
  - credittermwarningtext
  - primarygroupid : 3
  - secondarygroupid : 1
  - stopcredit : false
  - agedbal0 : 0
  - agedbal1 : 0
  - agedbal2 : 149.58
  - agedbal3 : 0
  - monthval : 0
  - lastmonth : 234.17
  - yearval : 5087.33
  - lastyear : 0
  - headaccno : -1
  - companytype : 1
  - accountname : Knight Nicol Autos
  - email : <EMAIL>
  - phone : 02-9827 1099
  - postaladdress
    - {
      - line1 : P.O Box 374-350
      - line2 : Penrith
      - line3 : NSW
      - line4 : Australia
      - line5 :
    - }
  - postalcode :
  - deliveryaddress
    - {
      - line1 : 370 High St
      - line2 : Penrith
      - line3 : NSW
      - line4 : Australia
      - line5 :
      - line6 :
    - }
  - defaultcontactid : 1
  - contacts
    - {
      - rel : collection/contact
      - href : {URI}https://exo-stage.api.myob.com/debtor/1/contact
    - }
  - website
  - salespersonid : 4
  - balance : 149.58
  - contactname
  - latitude : -33.75396
  - longitude : 150.698669
  - geocodestatus : 0
  - lastupdated : 2014-10-01T11:43:59.36+10:00
  - extrafields
    - {
      - 0
        - {
          - key : PASS\_WORD
          - value : xMpCOKC5I4INzFCab3WEmw==
        - }
      - 1
        - {
          - key : AUTOBILLCODE
          - value
        - }
      - 2
        - {
          - key : PROMPTPAY\_AMT
          - value : 0
        - }
      - 3
        - {
          - key : PROMPTPAY\_PC
          - value : 0
        - }
    - }
  - id : 1
  - href : {URI}https://exo-stage.api.myob.com/debtor/1
- }

{URI} is defined as: http://exo.api.myob.com/

|     |     |
| --- | --- |
|  |  |