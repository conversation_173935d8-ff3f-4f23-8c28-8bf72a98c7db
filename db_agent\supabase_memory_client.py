"""
Supabase Memory Client for LLM Agent
Replaces ChromaDB with Supabase database for persistent memory storage
Uses the defined schema for storing and retrieving LLM context and memory data
"""

import os
import json
import logging
from typing import List, Dict, Any, Optional, Union
from datetime import datetime, timedelta
from dataclasses import dataclass

# Supabase imports
try:
    from supabase import create_client, Client
    SUPABASE_AVAILABLE = True
except ImportError:
    SUPABASE_AVAILABLE = False
    create_client = None
    Client = None
    print("Supabase not installed. Run 'pip install supabase' to enable database features.")

from config import config

logger = logging.getLogger(__name__)

@dataclass
class MemoryRecord:
    """Represents a memory record stored in Supabase."""
    id: str
    content: str
    metadata: Dict[str, Any]
    created_at: datetime
    email_id: Optional[str] = None
    debtor_id: Optional[int] = None

class SupabaseMemoryClient:
    """
    Supabase-based memory client for LLM agent context storage.
    Integrates with the email processing schema for unified data management.
    """
    
    def __init__(self):
        """Initialize the Supabase memory client."""
        if not SUPABASE_AVAILABLE:
            raise ImportError("Supabase is not available. Install with 'pip install supabase'")
        
        # Get credentials from config
        self.url = config.SUPABASE_URL
        self.key = config.SUPABASE_KEY
        
        if not self.url or not self.key:
            raise ValueError("SUPABASE_URL and SUPABASE_KEY must be set in environment variables")
        
        # Initialize Supabase client
        if create_client is None:
            raise ImportError("Supabase create_client function not available")
        
        try:
            self.supabase = create_client(self.url, self.key)
            # Test connection with retry logic
            self.connection_available = self._test_connection()
            
            if self.connection_available:
                logger.info("Supabase memory client initialized successfully")
            else:
                logger.warning("Supabase memory client initialized in fallback mode (no connection)")
                
        except Exception as e:
            logger.error(f"Failed to initialize Supabase client: {e}")
            self.connection_available = False
            self.supabase = None
            logger.warning("Supabase memory client will operate in offline mode")
    
    def _test_connection(self):
        """Test the Supabase connection with retry and fallback."""
        import time
        max_retries = 3
        retry_delay = 2
        
        for attempt in range(max_retries):
            try:
                # Simple query to test connection with timeout
                result = self.supabase.table("customers").select("debtor_id").limit(1).execute()
                logger.info("Supabase connection test successful")
                return True
            except Exception as e:
                if attempt < max_retries - 1:
                    logger.warning(f"Supabase connection attempt {attempt + 1} failed: {e}")
                    logger.info(f"Retrying in {retry_delay} seconds...")
                    time.sleep(retry_delay)
                    retry_delay *= 2  # Exponential backoff
                else:
                    logger.error(f"Supabase connection test failed after {max_retries} attempts: {e}")
                    logger.warning("Supabase memory client will operate in fallback mode")
                    return False
        return False
    
    def _check_connection(self) -> bool:
        """Check if Supabase connection is available."""
        return hasattr(self, 'connection_available') and self.connection_available and self.supabase is not None
    
    def _execute_with_fallback(self, operation_name: str, operation_func, fallback_result=None):
        """Execute database operation with fallback handling."""
        if not self._check_connection():
            logger.warning(f"Supabase not connected, {operation_name} operation skipped")
            return fallback_result
        
        try:
            return operation_func()
        except Exception as e:
            logger.error(f"Supabase {operation_name} operation failed: {e}")
            return fallback_result
    
    def add_memory(self, documents: List[str], metadatas: Optional[List[Dict[str, Any]]] = None, 
                   ids: Optional[List[str]] = None, email_id: Optional[str] = None, 
                   debtor_id: Optional[int] = None) -> bool:
        """
        Add memory documents to Supabase system_logs table for LLM context storage.
        
        Args:
            documents: List of text documents to store
            metadatas: Optional metadata for each document
            ids: Optional unique IDs for each document
            email_id: Optional email ID to link memory to
            debtor_id: Optional customer debtor ID to link memory to
            
        Returns:
            bool: Success status
        """
        if not documents:
            return False
            
        def _add_operation():
            # Prepare records for insertion
            records = []
            for i, document in enumerate(documents):
                metadata = metadatas[i] if metadatas and i < len(metadatas) else {}
                record_id = ids[i] if ids and i < len(ids) else None
                
                # Prepare system log record for memory storage
                log_record = {
                    "level": "INFO",
                    "message": document,
                    "module": "llm_memory",
                    "function_name": "add_memory",
                    "email_id": email_id,
                    "debtor_id": debtor_id,
                    "additional_data": {
                        "memory_metadata": metadata,
                        "memory_id": record_id,
                        "memory_type": "llm_context",
                        "indexed_at": datetime.now().isoformat()
                    }
                }
                records.append(log_record)
            
            # Insert into system_logs table
            result = self.supabase.table("system_logs").insert(records).execute()
            
            if result.data:
                logger.info(f"Successfully added {len(documents)} memory documents")
                return True
            else:
                logger.error("Failed to add memory documents - no data returned")
                return False
        
        return self._execute_with_fallback("add_memory", _add_operation, False)
    
    def query_memory(self, query_texts: List[str], n_results: int = 10, 
                     where: Optional[Dict[str, Any]] = None, 
                     email_id: Optional[str] = None,
                     debtor_id: Optional[int] = None) -> Dict[str, Any]:
        """
        Query memory documents from Supabase.
        
        Args:
            query_texts: List of query strings
            n_results: Number of results to return
            where: Optional metadata filtering
            email_id: Filter by email ID
            debtor_id: Filter by debtor ID
            
        Returns:
            dict: Query results in ChromaDB-compatible format
        """
        try:
            if not query_texts:
                return {"documents": [], "metadatas": [], "ids": []}
            
            # Build query
            query = self.supabase.table("system_logs").select("*")
            
            # Filter by module and function for memory records
            query = query.eq("module", "llm_memory").eq("function_name", "add_memory")
            
            # Apply filters
            if email_id:
                query = query.eq("email_id", email_id)
            if debtor_id:
                query = query.eq("debtor_id", debtor_id)
            
            # Execute query with limit
            query = query.order("created_at", desc=True).limit(n_results)
            result = query.execute()
            
            if not result.data:
                return {"documents": [], "metadatas": [], "ids": []}
            
            # Format results to match ChromaDB format
            documents = []
            metadatas = []
            ids = []
            
            for record in result.data:
                documents.append(record["message"])
                
                # Extract metadata from additional_data
                metadata = record.get("additional_data", {}).get("memory_metadata", {})
                metadata.update({
                    "log_id": record["id"],
                    "created_at": record["created_at"],
                    "email_id": record.get("email_id"),
                    "debtor_id": record.get("debtor_id")
                })
                metadatas.append(metadata)
                
                # Use memory_id if available, otherwise log_id
                record_id = record.get("additional_data", {}).get("memory_id", record["id"])
                ids.append(record_id)
            
            # Simple text similarity scoring (basic implementation)
            # In a production system, you might want to implement vector similarity
            scored_results = self._score_results(query_texts[0], documents, metadatas, ids)
            
            logger.info(f"Retrieved {len(scored_results['documents'])} memory documents")
            return scored_results
            
        except Exception as e:
            logger.error(f"Error querying memory: {e}")
            return {"documents": [], "metadatas": [], "ids": []}
    
    def _score_results(self, query: str, documents: List[str], metadatas: List[Dict], 
                       ids: List[str]) -> Dict[str, Any]:
        """
        Simple text similarity scoring for results.
        In production, this would use vector embeddings.
        """
        query_lower = query.lower()
        scored_items = []
        
        for doc, meta, doc_id in zip(documents, metadatas, ids):
            # Simple keyword matching score
            score = 0
            doc_lower = doc.lower()
            
            # Count keyword matches
            query_words = query_lower.split()
            for word in query_words:
                if word in doc_lower:
                    score += 1
            
            # Normalize score
            score = score / len(query_words) if query_words else 0
            
            scored_items.append((score, doc, meta, doc_id))
        
        # Sort by score (descending)
        scored_items.sort(key=lambda x: x[0], reverse=True)
        
        # Extract sorted results
        return {
            "documents": [item[1] for item in scored_items],
            "metadatas": [item[2] for item in scored_items],
            "ids": [item[3] for item in scored_items],
            "distances": [1.0 - item[0] for item in scored_items]  # Convert score to distance
        }
    
    def delete_memory(self, ids: Optional[List[str]] = None, 
                      where: Optional[Dict[str, Any]] = None,
                      email_id: Optional[str] = None,
                      debtor_id: Optional[int] = None) -> bool:
        """
        Delete memory documents from Supabase.
        
        Args:
            ids: List of IDs to delete
            where: Optional metadata filtering
            email_id: Delete by email ID
            debtor_id: Delete by debtor ID
            
        Returns:
            bool: Success status
        """
        try:
            if not any([ids, email_id, debtor_id]):
                logger.warning("No deletion criteria provided")
                return False
            
            # Build delete query
            if ids:
                # Delete by specific memory IDs stored in additional_data
                for memory_id in ids:
                    result = self.supabase.table("system_logs")\
                        .delete()\
                        .eq("module", "llm_memory")\
                        .eq("function_name", "add_memory")\
                        .contains("additional_data", {"memory_id": memory_id})\
                        .execute()
            else:
                # Delete by email_id or debtor_id
                query = self.supabase.table("system_logs").delete()
                query = query.eq("module", "llm_memory").eq("function_name", "add_memory")
                
                if email_id:
                    query = query.eq("email_id", email_id)
                if debtor_id:
                    query = query.eq("debtor_id", debtor_id)
                
                result = query.execute()
            
            logger.info("Memory deletion completed")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting memory: {e}")
            return False
    
    def store_order_in_memory(self, order_data: Dict[str, Any], email_id: Optional[str] = None) -> bool:
        """
        Store order processing context in memory for LLM reference.
        
        Args:
            order_data: Order information to store
            email_id: Associated email ID
            
        Returns:
            bool: Success status
        """
        try:
            # Format order context for memory storage
            order_context = f"""
Order Processing Context:
Customer: {order_data.get('customer_name', 'Unknown')}
Debtor ID: {order_data.get('debtor_id', 'Unknown')}
Order Number: {order_data.get('customer_order_number', 'Unknown')}
Total Amount: ${order_data.get('total_amount', 0):.2f}
Line Items: {len(order_data.get('line_items', []))}
Status: {order_data.get('status', 'Unknown')}
Processed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
            
            metadata = {
                "type": "order_context",
                "customer_name": order_data.get('customer_name'),
                "debtor_id": order_data.get('debtor_id'),
                "order_number": order_data.get('customer_order_number'),
                "total_amount": order_data.get('total_amount'),
                "status": order_data.get('status')
            }
            
            return self.add_memory(
                documents=[order_context],
                metadatas=[metadata],
                ids=[f"order_{order_data.get('customer_order_number', datetime.now().isoformat())}"],
                email_id=email_id,
                debtor_id=order_data.get('debtor_id')
            )
            
        except Exception as e:
            logger.error(f"Error storing order in memory: {e}")
            return False
    
    def get_relevant_context(self, query: str, customer_name: Optional[str] = None, 
                           debtor_id: Optional[int] = None, limit: int = 5) -> str:
        """
        Get relevant context for LLM processing.
        
        Args:
            query: Context query
            customer_name: Optional customer filter
            debtor_id: Optional debtor ID filter
            limit: Maximum number of context items
            
        Returns:
            str: Formatted context string
        """
        try:
            # Query memory for relevant context
            where_filter = {}
            if customer_name:
                where_filter["customer_name"] = customer_name
            
            results = self.query_memory(
                query_texts=[query],
                n_results=limit,
                where=where_filter if where_filter else None,
                debtor_id=debtor_id
            )
            
            if not results["documents"]:
                return "No relevant context found."
            
            # Format context for LLM
            context_parts = ["=== RELEVANT CONTEXT ==="]
            
            for i, (doc, metadata) in enumerate(zip(results["documents"], results["metadatas"])):
                context_parts.append(f"\nContext {i+1}:")
                context_parts.append(f"Created: {metadata.get('created_at', 'Unknown')}")
                if metadata.get('customer_name'):
                    context_parts.append(f"Customer: {metadata['customer_name']}")
                context_parts.append(f"Content: {doc[:500]}...")  # Truncate long content
                context_parts.append("-" * 50)
            
            return "\n".join(context_parts)
            
        except Exception as e:
            logger.error(f"Error getting relevant context: {e}")
            return "Error retrieving context."
    
    def get_customer_context(self, debtor_id: int, limit: int = 10) -> Dict[str, Any]:
        """
        Get comprehensive customer context from database.
        
        Args:
            debtor_id: Customer debtor ID
            limit: Maximum number of records
            
        Returns:
            dict: Customer context data
        """
        try:
            context = {"customer_info": None, "recent_emails": [], "order_history": []}
            
            # Get customer information
            customer_result = self.supabase.table("customers")\
                .select("*")\
                .eq("debtor_id", debtor_id)\
                .execute()
            
            if customer_result.data:
                context["customer_info"] = customer_result.data[0]
            
            # Get recent emails
            emails_result = self.supabase.table("emails")\
                .select("*")\
                .eq("debtor_id", debtor_id)\
                .order("received_date", desc=True)\
                .limit(limit)\
                .execute()
            
            if emails_result.data:
                context["recent_emails"] = emails_result.data
            
            # Get ERP payloads/orders
            orders_result = self.supabase.table("erp_payloads")\
                .select("*")\
                .eq("debtor_id", debtor_id)\
                .order("created_at", desc=True)\
                .limit(limit)\
                .execute()
            
            if orders_result.data:
                context["order_history"] = orders_result.data
            
            return context
            
        except Exception as e:
            logger.error(f"Error getting customer context: {e}")
            return {"customer_info": None, "recent_emails": [], "order_history": []}
    
    def cleanup_old_memory(self, days_old: int = 30) -> bool:
        """
        Clean up old memory records.
        
        Args:
            days_old: Delete records older than this many days
            
        Returns:
            bool: Success status
        """
        try:
            cutoff_date = (datetime.now() - timedelta(days=days_old)).isoformat()
            
            result = self.supabase.table("system_logs")\
                .delete()\
                .eq("module", "llm_memory")\
                .eq("function_name", "add_memory")\
                .lt("created_at", cutoff_date)\
                .execute()
            
            logger.info(f"Cleaned up memory records older than {days_old} days")
            return True
            
        except Exception as e:
            logger.error(f"Error cleaning up old memory: {e}")
            return False


# Singleton instance for global use
supabase_memory_client = None

def get_memory_client() -> SupabaseMemoryClient:
    """Get or create the global Supabase memory client instance."""
    global supabase_memory_client
    if supabase_memory_client is None:
        supabase_memory_client = SupabaseMemoryClient()
    return supabase_memory_client


if __name__ == "__main__":
    # Example usage and testing
    try:
        # Initialize memory client
        memory_client = SupabaseMemoryClient()
        
        # Test adding memory
        test_documents = [
            "Customer Brady Australia placed order #12345 for safety equipment",
            "RSEA Safety requested quote for protective gear",
            "Woolworths approved purchase order for team systems equipment"
        ]
        
        test_metadata = [
            {"customer": "Brady Australia", "type": "order", "order_number": "12345"},
            {"customer": "RSEA Safety", "type": "quote_request"},
            {"customer": "Woolworths", "type": "purchase_order"}
        ]
        
        # Add memories
        success = memory_client.add_memory(
            documents=test_documents,
            metadatas=test_metadata,
            ids=["test1", "test2", "test3"]
        )
        print(f"Add memory success: {success}")
        
        # Query memories
        results = memory_client.query_memory(
            query_texts=["Brady Australia order"],
            n_results=5
        )
        print(f"Query results: {len(results['documents'])} documents found")
        
        # Get relevant context
        context = memory_client.get_relevant_context(
            query="recent customer orders",
            limit=3
        )
        print(f"Relevant context: {context[:200]}...")
        
        # Test customer context
        customer_context = memory_client.get_customer_context(debtor_id=5760)
        print(f"Customer context: {customer_context}")
        
        print("Supabase memory client test completed successfully!")
        
    except Exception as e:
        print(f"Test failed: {e}")
