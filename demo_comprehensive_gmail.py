#!/usr/bin/env python3
"""
Demo script for Enhanced Gmail Service with Comprehensive Processing Workflow.

This script demonstrates the enhanced Gmail service following the exact workflow
pattern from demo_enhanced_llm.py with CRUD operations, PDF parsing, and MYOB integration.
"""

import sys
from pathlib import Path

# Add current directory to path for imports
sys.path.insert(0, str(Path(__file__).parent))

from services.enhanced_gmail_service import EnhancedGmailService
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn

console = Console()

def demo_comprehensive_workflow():
    """Demonstrate the comprehensive email processing workflow."""
    console.print(Panel.fit(
        "🚀 Enhanced Gmail Service - Comprehensive Workflow Demo\n"
        "Following the Enhanced LLM Demo Pattern",
        style="bold yellow"
    ))
    
    try:
        # Initialize enhanced Gmail service
        enhanced_gmail = EnhancedGmailService()
        
        console.print("🔄 Fetching Brady emails for comprehensive processing...")
        
        # Fetch Brady emails (we know these exist)
        emails = enhanced_gmail.fetch_emails_with_ai_filtering(
            labels=["Brady"],
            days_back=7,
            ai_filter=True,
            min_confidence=0.7
        )
        
        if not emails:
            console.print("⚠️  No emails found for processing", style="yellow")
            return
        
        console.print(f"✅ Found {len(emails)} emails for comprehensive processing")
        
        # Process first email with comprehensive workflow
        sample_email = emails[0]
        
        console.print(f"\n📧 Processing email: {sample_email.subject}")
        console.print(f"📤 From: {sample_email.sender}")
        console.print(f"📎 Attachments: {len(sample_email.attachments)}")
        
        # Run comprehensive processing workflow
        result = enhanced_gmail.comprehensive_email_processing(
            sample_email, 
            display_results=True
        )
        
        # Display final workflow results
        console.print("\n" + "="*60)
        console.print("📋 Final Workflow Results")
        console.print("="*60)
        
        # Results summary table
        results_table = Table(title="Comprehensive Processing Results")
        results_table.add_column("Metric", style="bold")
        results_table.add_column("Value", style="cyan")
        results_table.add_column("Status", style="green")
        
        results_table.add_row("Processing Status", result.processing_status, "✅" if result.processing_status == "success" else "❌")
        results_table.add_row("Processing Time", f"{result.processing_time:.2f}s", "⏱️")
        results_table.add_row("Email Record Created", "Yes" if result.email_record_id else "No", "📝")
        results_table.add_row("PDFs Analyzed", str(len(result.pdf_analyses)), "📄")
        results_table.add_row("Orders Extracted", str(len(result.extracted_orders)), "📦")
        results_table.add_row("MYOB Payloads Generated", str(len(result.myob_payloads or [])), "💼")
        
        console.print(results_table)
        
        # Show service statistics
        stats = enhanced_gmail.processing_stats
        stats_table = Table(title="Service Statistics")
        stats_table.add_column("Metric", style="bold")
        stats_table.add_column("Value", style="yellow")
        
        stats_table.add_row("Total Emails Processed", str(stats["emails_processed"]))
        stats_table.add_row("Total Orders Extracted", str(stats["orders_extracted"]))
        stats_table.add_row("Processing Errors", str(stats["processing_errors"]))
        stats_table.add_row("Last Processing Time", stats["last_processing_time"] or "Never")
        
        console.print(stats_table)
        
    except Exception as e:
        console.print(f"❌ Error in comprehensive workflow demo: {e}", style="red")

def demo_batch_comprehensive_processing():
    """Demonstrate batch processing with comprehensive workflow."""
    console.print(Panel.fit(
        "📊 Batch Comprehensive Processing Demo",
        style="bold blue"
    ))
    
    try:
        enhanced_gmail = EnhancedGmailService()
        
        console.print("🔄 Fetching multiple Brady emails for batch processing...")
        
        # Fetch multiple emails
        emails = enhanced_gmail.fetch_emails_with_ai_filtering(
            labels=["Brady"],
            days_back=14,
            ai_filter=True,
            min_confidence=0.6
        )
        
        if not emails:
            console.print("⚠️  No emails found for batch processing", style="yellow")
            return
        
        # Limit to first 3 emails for demo
        batch_emails = emails[:3]
        console.print(f"✅ Processing batch of {len(batch_emails)} emails")
        
        batch_results = []
        total_orders = 0
        total_payloads = 0
        
        with Progress(SpinnerColumn(), TextColumn("[progress.description]{task.description}")) as progress:
            task = progress.add_task("Batch processing...", total=len(batch_emails))
            
            for i, email in enumerate(batch_emails):
                progress.update(task, description=f"Processing email {i+1}/{len(batch_emails)}: {email.subject[:30]}...")
                
                # Process with comprehensive workflow (no display for batch)
                result = enhanced_gmail.comprehensive_email_processing(
                    email, 
                    display_results=False
                )
                
                batch_results.append(result)
                total_orders += len(result.extracted_orders)
                total_payloads += len(result.myob_payloads or [])
                
                progress.advance(task)
        
        # Display batch results
        console.print("✅ Batch processing completed")
        
        batch_table = Table(title="Batch Processing Results")
        batch_table.add_column("Email", style="bold", width=30)
        batch_table.add_column("Status", style="green", width=10)
        batch_table.add_column("Orders", style="yellow", width=8)
        batch_table.add_column("MYOB", style="cyan", width=8)
        batch_table.add_column("Time", style="dim", width=8)
        
        for result in batch_results:
            subject = result.email_data.subject[:25] + ("..." if len(result.email_data.subject) > 25 else "")
            status = "✅" if result.processing_status == "success" else "❌"
            orders = str(len(result.extracted_orders))
            myob = str(len(result.myob_payloads or []))
            time_str = f"{result.processing_time:.1f}s" if result.processing_time else "N/A"
            
            batch_table.add_row(subject, status, orders, myob, time_str)
        
        console.print(batch_table)
        
        # Batch summary
        summary_table = Table(title="Batch Summary")
        summary_table.add_column("Metric", style="bold")
        summary_table.add_column("Value", style="cyan")
        
        total_time = sum(r.processing_time or 0 for r in batch_results)
        success_count = sum(1 for r in batch_results if r.processing_status == "success")
        
        summary_table.add_row("Emails Processed", str(len(batch_results)))
        summary_table.add_row("Successful Processing", str(success_count))
        summary_table.add_row("Total Orders Extracted", str(total_orders))
        summary_table.add_row("Total MYOB Payloads", str(total_payloads))
        summary_table.add_row("Total Processing Time", f"{total_time:.2f}s")
        summary_table.add_row("Average Time per Email", f"{total_time / len(batch_results):.2f}s")
        
        console.print(summary_table)
        
    except Exception as e:
        console.print(f"❌ Error in batch processing demo: {e}", style="red")

def demo_email_crud_workflow():
    """Demonstrate the email CRUD workflow integration."""
    console.print(Panel.fit(
        "📝 Email CRUD Workflow Integration Demo",
        style="bold green"
    ))
    
    try:
        enhanced_gmail = EnhancedGmailService()
        
        console.print("🔄 Demonstrating CRUD operations integration...")
        
        # Fetch an email
        emails = enhanced_gmail.fetch_emails_with_ai_filtering(
            labels=["Brady"],
            days_back=7,
            ai_filter=True,
            min_confidence=0.7
        )
        
        if not emails:
            console.print("⚠️  No emails found for CRUD demo", style="yellow")
            return
        
        email = emails[0]
        
        # Process email (this creates the email record)
        console.print("📧 Processing email with comprehensive workflow...")
        result = enhanced_gmail.comprehensive_email_processing(email, display_results=False)
        
        if result.email_record_id:
            console.print(f"✅ Email record created: {result.email_record_id}")
            
            # READ - Demonstrate reading the email record
            console.print("\n📖 Reading email record from memory...")
            email_record = enhanced_gmail.llm_service.read_email_record(result.email_record_id)
            
            if email_record:
                console.print("✅ Email record retrieved successfully")
                
                # Display some metadata
                metadata_table = Table(title="Stored Email Metadata")
                metadata_table.add_column("Field", style="bold")
                metadata_table.add_column("Value", style="cyan")
                
                metadata = email_record["metadata"]
                for key, value in list(metadata.items())[:8]:  # Show first 8 fields
                    metadata_table.add_row(key, str(value))
                
                console.print(metadata_table)
            
            # SEARCH - Demonstrate searching for similar emails
            console.print("\n🔍 Searching for similar emails...")
            search_results = enhanced_gmail.llm_service.search_emails(
                query=f"Brady purchase order {email.subject[:20]}",
                filters={"category": "purchase_order"},
                limit=3
            )
            
            console.print(f"✅ Found {len(search_results)} similar emails in memory")
            
            if search_results:
                search_table = Table(title="Similar Emails Found")
                search_table.add_column("Email ID", style="dim")
                search_table.add_column("Subject", style="bold")
                search_table.add_column("Relevance", style="green")
                
                for result_item in search_results[:3]:
                    metadata = result_item["metadata"]
                    relevance = f"{result_item['relevance_score']:.2f}"
                    
                    search_table.add_row(
                        metadata.get("email_id", "Unknown")[:12] + "...",
                        metadata.get("subject", "No subject")[:30] + "...",
                        relevance
                    )
                
                console.print(search_table)
        
        else:
            console.print("⚠️  No email record was created", style="yellow")
        
    except Exception as e:
        console.print(f"❌ Error in CRUD workflow demo: {e}", style="red")

def main():
    """Run all comprehensive workflow demonstrations."""
    console.print(Panel.fit(
        "🚀 Enhanced Gmail Service - Comprehensive Workflow Demonstrations\n"
        "Replicating the Enhanced LLM Demo Pattern with Real Gmail Data",
        style="bold magenta"
    ))
    
    try:
        # Run demonstrations
        demo_comprehensive_workflow()
        console.print("\n" + "="*80 + "\n")
        
        demo_batch_comprehensive_processing()
        console.print("\n" + "="*80 + "\n")
        
        demo_email_crud_workflow()
        
        console.print(Panel.fit(
            "✅ All comprehensive workflow demonstrations completed!\n\n"
            "The Enhanced Gmail Service now provides:\n"
            "• Complete CRUD operations for emails with AI analysis\n"
            "• PDF parsing and structure analysis for attachments\n"
            "• Order extraction with business rule application\n"
            "• MYOB payload generation ready for ERP integration\n"
            "• Rich visual feedback and comprehensive reporting\n"
            "• Memory-based context storage and semantic search\n"
            "• Batch processing capabilities for high-volume scenarios\n\n"
            "This workflow exactly matches the enhanced LLM demo pattern\n"
            "but operates on real Gmail data with full integration!",
            style="bold green"
        ))
        
    except Exception as e:
        console.print(f"❌ Comprehensive workflow demo failed: {e}", style="red")

if __name__ == "__main__":
    main()