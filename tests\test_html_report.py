#!/usr/bin/env python3
"""
Test HTML Report Generation
Generate a sample HTML report based on the recent processing run.
"""

import os
import sys
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from email_report_service import EmailReportService, EmailClassification, ProcessedOrder, SystemMetrics

def create_sample_data():
    """Create sample data based on the recent processing run."""
    
    # Email classifications from the recent run
    classifications = [
        EmailClassification(
            subject="Purchase Order 4506048706",
            sender="<EMAIL>",
            category="ORDER",
            priority="HIGH",
            confidence=0.90,
            response_required=True,
            summary="Purchase order email from <PERSON> at BradyCorp with an order for safety equipment",
            gmail_id="1981ae6b3133d595"
        ),
        EmailClassification(
            subject="PURCHASE ORDER - 1889386 for TEASYS from RSEA Safety Cairns",
            sender="<EMAIL>",
            category="ORDER",
            priority="HIGH",
            confidence=0.95,
            response_required=False,
            summary="Purchase order for TEASYS from RSEA Safety Cairns with order details",
            gmail_id="19810778cacb98ac"
        ),
        EmailClassification(
            subject="Purchase Order 4401204387",
            sender="<EMAIL>",
            category="ORDER",
            priority="HIGH",
            confidence=0.90,
            response_required=True,
            summary="Purchase Order #4401204387 from Woolworths",
            gmail_id="1981bcd5ff1d840f"
        ),
        EmailClassification(
            subject="[Brierley Purchase#606529 3] Purchase Order (with Pricing) v2 #606529",
            sender="<EMAIL>",
            category="ORDER",
            priority="HIGH",
            confidence=0.90,
            response_required=True,
            summary="Purchase order with pricing details from Brierley Hose",
            gmail_id="1981aebe9dd6c57b"
        ),
        EmailClassification(
            subject="New Purchase Order # PO36002",
            sender="<EMAIL>",
            category="ORDER",
            priority="HIGH",
            confidence=0.95,
            response_required=True,
            summary="Purchase order #PO36002 from Gateway Packaging requesting confirmation",
            gmail_id="1981118e2f9a18e6"
        ),
        EmailClassification(
            subject="ORDER CONFIRMATION - 916750",
            sender="<EMAIL>",
            category="ORDER",
            priority="MEDIUM",
            confidence=0.90,
            response_required=False,
            summary="Order confirmation email from Fallshaw Group with details about order 916750",
            gmail_id="1981bed7807da3b4"
        ),
        EmailClassification(
            subject="Sitecraft Purchase Order #116201",
            sender="<EMAIL>",
            category="ORDER",
            priority="HIGH",
            confidence=0.95,
            response_required=True,
            summary="Purchase order #116201 from Sitecraft requesting confirmation",
            gmail_id="1981be46bf4772a2"
        )
    ]
    
    # Processed orders from the recent run
    processed_orders = [
        ProcessedOrder(
            subject="Purchase Order 4506048706",
            customer_name="BASKETBALL RINGLEADER",
            debtor_id=248,
            order_total=1250.00,
            line_items=3,
            status="FAILED",
            processing_time=4.2,
            error_message="Debtor ID 248 not found in customers table"
        ),
        ProcessedOrder(
            subject="PURCHASE ORDER - 1889386 for TEASYS from RSEA Safety Cairns",
            customer_name="ROAD SAFETY EQUIPMENT (RSEA)",
            debtor_id=6207,
            order_total=2850.75,
            line_items=2,
            status="SUCCESS",
            processing_time=5.4
        ),
        ProcessedOrder(
            subject="Purchase Order 4401204387",
            customer_name="WOOLWORTHS LIMITED",
            debtor_id=10981,
            order_total=15420.50,
            line_items=1,
            status="SUCCESS",
            processing_time=3.8
        ),
        ProcessedOrder(
            subject="[Brierley Purchase#606529 3] Purchase Order (with Pricing) v2 #606529",
            customer_name="BASKETBALL RINGLEADER",
            debtor_id=248,
            order_total=890.25,
            line_items=2,
            status="FAILED",
            processing_time=2.9,
            error_message="Debtor ID 248 not found in customers table"
        ),
        ProcessedOrder(
            subject="New Purchase Order # PO36002",
            customer_name="WURTH AUSTRALIA",
            debtor_id=1663,
            order_total=3200.00,
            line_items=1,
            status="SUCCESS",
            processing_time=3.1
        ),
        ProcessedOrder(
            subject="ORDER CONFIRMATION - 916750",
            customer_name="FALLSHAW HOLDINGS",
            debtor_id=7001,
            order_total=4750.80,
            line_items=2,
            status="SUCCESS",
            processing_time=2.7
        ),
        ProcessedOrder(
            subject="Sitecraft Purchase Order #116201",
            customer_name="ROLLS AUSTRALIA PTY LTD",
            debtor_id=1302,
            order_total=1890.45,
            line_items=1,
            status="SUCCESS",
            processing_time=2.5
        )
    ]
    
    return classifications, processed_orders

def main():
    """Generate and save the HTML report."""
    
    print("🧠 GENERATING HTML EMAIL REPORT")
    print("=" * 50)
    
    # Create sample data
    classifications, processed_orders = create_sample_data()
    
    # Initialize the email report service
    report_service = EmailReportService()
    
    # Generate system metrics
    metrics = report_service.generate_summary_metrics(classifications, processed_orders)
    
    print(f"📊 System Metrics Generated:")
    print(f"   Total Emails: {metrics.total_emails_processed}")
    print(f"   Success Rate: {metrics.success_rate:.1f}%")
    print(f"   Successful Orders: {metrics.successful_orders}")
    print(f"   Failed Orders: {metrics.failed_orders}")
    print(f"   JSON Parse Rate: {metrics.json_parse_success_rate:.1f}%")
    print(f"   Customer Match Rate: {metrics.customer_match_rate:.1f}%")
    
    # Generate HTML report
    print(f"\n📧 Generating HTML Report...")
    html_report = report_service.generate_html_report(
        classifications=classifications,
        processed_orders=processed_orders,
        system_metrics=metrics,
        report_period="Last Processing Run (7 emails)"
    )
    
    # Save to file
    report_filename = f"team_systems_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
    with open(report_filename, "w", encoding="utf-8") as f:
        f.write(html_report)
    
    print(f"✅ HTML Report Generated: {report_filename}")
    print(f"📁 File size: {len(html_report):,} characters")
    
    # Display key highlights
    total_order_value = sum(order.order_total for order in processed_orders if order.status == "SUCCESS")
    high_priority_count = len([c for c in classifications if c.priority == "HIGH"])
    response_required_count = len([c for c in classifications if c.response_required])
    
    print(f"\n🎯 Report Highlights:")
    print(f"   💰 Total Order Value: ${total_order_value:,.2f}")
    print(f"   🚨 High Priority Emails: {high_priority_count}")
    print(f"   📋 Response Required: {response_required_count}")
    print(f"   ⚡ Average Processing Time: {metrics.avg_processing_time:.2f}s")
    
    # Test email sending (if configured)
    if os.getenv('SMTP_USERNAME') and os.getenv('SMTP_PASSWORD') and os.getenv('REPORT_RECIPIENTS'):
        print(f"\n📤 Testing Email Send...")
        try:
            success = report_service.send_html_report(
                classifications=classifications,
                processed_orders=processed_orders,
                system_metrics=metrics,
                subject_prefix="Team Systems Test Report",
                report_period="Test Run - 7 Emails Processed"
            )
            if success:
                print(f"✅ Email sent successfully!")
            else:
                print(f"❌ Email sending failed - check logs")
        except Exception as e:
            print(f"❌ Email sending error: {e}")
    else:
        print(f"\n📤 Email sending not configured (missing SMTP credentials)")
        print(f"   To enable email reports, set:")
        print(f"   - SMTP_USERNAME")
        print(f"   - SMTP_PASSWORD") 
        print(f"   - REPORT_RECIPIENTS")
    
    print(f"\n🎉 Report generation complete!")
    print(f"📂 Open {report_filename} in your browser to view the report")

if __name__ == "__main__":
    main()