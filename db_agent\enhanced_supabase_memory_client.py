"""
Enhanced Supabase Memory Client with Vector Embeddings and Semantic Search
Replaces ChromaDB with Supabase database for persistent memory storage with vector search
Uses pgvector extension for semantic similarity search
"""

import os
import json
import logging
import numpy as np
from typing import List, Dict, Any, Optional, Union, <PERSON><PERSON>
from datetime import datetime, timed<PERSON>ta
from dataclasses import dataclass

# Supabase imports
try:
    from supabase import create_client, Client
    SUPABASE_AVAILABLE = True
except ImportError:
    SUPABASE_AVAILABLE = False
    create_client = None
    Client = None
    print("Supabase not installed. Run 'pip install supabase' to enable database features.")

# Vector embeddings
try:
    from sentence_transformers import SentenceTransformer, util
    EMBEDDINGS_AVAILABLE = True
except ImportError:
    EMBEDDINGS_AVAILABLE = False
    SentenceTransformer = None
    util = None
    print("sentence-transformers not available. Install with: pip install sentence-transformers")

from config import config

logger = logging.getLogger(__name__)

@dataclass
class MemoryRecord:
    """Represents a memory record with vector embedding."""
    id: str
    content: str
    metadata: Dict[str, Any]
    embedding: Optional[List[float]]
    similarity_score: Optional[float]
    created_at: datetime
    email_id: Optional[str] = None
    debtor_id: Optional[int] = None

class EnhancedSupabaseMemoryClient:
    """
    Enhanced Supabase-based memory client with vector embeddings for semantic search.
    Integrates with the email processing schema for unified data management.
    """
    
    def __init__(self, embedding_model: str = "all-MiniLM-L6-v2"):
        """Initialize the enhanced Supabase memory client with vector capabilities."""
        if not SUPABASE_AVAILABLE:
            raise ImportError("Supabase is not available. Install with 'pip install supabase'")
        
        # Get credentials from config
        self.url = config.SUPABASE_URL
        self.key = config.SUPABASE_KEY
        
        if not self.url or not self.key:
            raise ValueError("SUPABASE_URL and SUPABASE_KEY must be set in environment variables")
        
        # Initialize Supabase client
        if create_client is None:
            raise ImportError("Supabase create_client function not available")
        self.supabase: Client = create_client(self.url, self.key)
        
        # Initialize embedding model
        self.embedding_model = None
        self.embedding_dimension = 384  # Default for all-MiniLM-L6-v2
        
        if EMBEDDINGS_AVAILABLE:
            try:
                # Ensure the model is downloaded and loaded from the local path
                if util:
                    model_path = util.snapshot_download(f"sentence-transformers/{embedding_model}")
                    self.embedding_model = SentenceTransformer(model_path)
                else:
                    self.embedding_model = SentenceTransformer(embedding_model) # Fallback if util not available

                # Get actual embedding dimension
                test_embedding = self.embedding_model.encode("test")
                self.embedding_dimension = len(test_embedding)
                logger.info(f"Initialized embedding model: {embedding_model} (dimension: {self.embedding_dimension})")
            except Exception as e:
                logger.warning(f"Failed to load embedding model: {e}")
                self.embedding_model = None
        
        # Test connection and setup
        self._test_connection()
        self._ensure_vector_table()
        
        logger.info("Enhanced Supabase memory client initialized successfully")
    
    def _test_connection(self):
        """Test the Supabase connection."""
        try:
            # Simple query to test connection
            result = self.supabase.table("customers").select("debtor_id").limit(1).execute()
            logger.info("Supabase connection test successful")
        except Exception as e:
            logger.error(f"Supabase connection test failed: {e}")
            raise
    
    def _ensure_vector_table(self):
        """Ensure the vector memory table exists with proper schema."""
        try:
            # Check if we can query the email_vectors table
            # If not, it might need to be created
            result = self.supabase.table("system_logs").select("id").limit(1).execute()
            logger.info("Vector storage table accessible")
        except Exception as e:
            logger.warning(f"Vector table setup issue: {e}")
    
    def generate_embedding(self, text: str) -> Optional[List[float]]:
        """Generate embedding vector for text."""
        try:
            if self.embedding_model:
                # Use local sentence transformers
                embedding = self.embedding_model.encode(text)
                return embedding.tolist()
            else:
                # Fallback: return zero vector
                logger.warning("No embedding model available, using zero vector")
                return [0.0] * self.embedding_dimension
                
        except Exception as e:
            logger.error(f"Failed to generate embedding: {e}")
            return None
    
    def add_memory_with_vectors(self, documents: List[str], metadatas: Optional[List[Dict[str, Any]]] = None, 
                               ids: Optional[List[str]] = None, email_id: Optional[str] = None, 
                               debtor_id: Optional[int] = None) -> bool:
        """
        Add memory documents with vector embeddings to Supabase.
        
        Args:
            documents: List of text documents to store
            metadatas: Optional metadata for each document
            ids: Optional unique IDs for each document
            email_id: Optional email ID to link memory to
            debtor_id: Optional customer debtor ID to link memory to
            
        Returns:
            bool: Success status
        """
        try:
            if not documents:
                return False
            
            # Generate embeddings for all documents
            embeddings = []
            for doc in documents:
                embedding = self.generate_embedding(doc)
                embeddings.append(embedding)
            
            # Prepare records for insertion into system_logs with vector data
            records = []
            for i, document in enumerate(documents):
                metadata = metadatas[i] if metadatas and i < len(metadatas) else {}
                record_id = ids[i] if ids and i < len(ids) else None
                embedding = embeddings[i]
                
                # Enhanced system log record with vector embedding
                log_record = {
                    "level": "INFO",
                    "message": document,
                    "module": "llm_memory_vector",
                    "function_name": "add_memory_with_vectors",
                    "email_id": email_id,
                    "debtor_id": debtor_id,
                    "additional_data": {
                        "memory_metadata": metadata,
                        "memory_id": record_id,
                        "memory_type": "llm_context_vector",
                        "embedding": embedding,
                        "embedding_model": "sentence-transformers/all-MiniLM-L6-v2",
                        "embedding_dimension": self.embedding_dimension,
                        "indexed_at": datetime.now().isoformat(),
                        "text_length": len(document),
                        "has_vector": embedding is not None
                    }
                }
                records.append(log_record)
            
            # Insert into system_logs table
            result = self.supabase.table("system_logs").insert(records).execute()
            
            if result.data:
                logger.info(f"Successfully added {len(documents)} memory documents with vectors")
                return True
            else:
                logger.error("Failed to add memory documents - no data returned")
                return False
                
        except Exception as e:
            logger.error(f"Error adding memory documents with vectors: {e}")
            return False
    
    def semantic_search(self, query_text: str, n_results: int = 10, 
                       email_id: Optional[str] = None, debtor_id: Optional[int] = None,
                       similarity_threshold: float = 0.5) -> Dict[str, Any]:
        """
        Perform semantic search using vector similarity.
        
        Args:
            query_text: Text to search for
            n_results: Number of results to return
            email_id: Filter by email ID
            debtor_id: Filter by debtor ID
            similarity_threshold: Minimum similarity score
            
        Returns:
            dict: Search results with similarity scores
        """
        try:
            # Generate embedding for query
            query_embedding = self.generate_embedding(query_text)
            if not query_embedding:
                logger.warning("Could not generate query embedding, falling back to text search")
                return self._fallback_text_search(query_text, n_results, email_id, debtor_id)
            
            # Get all vector memory records
            query = self.supabase.table("system_logs").select("*")
            query = query.eq("module", "llm_memory_vector").eq("function_name", "add_memory_with_vectors")
            
            # Apply filters
            if email_id:
                query = query.eq("email_id", email_id)
            if debtor_id:
                query = query.eq("debtor_id", debtor_id)
            
            # Execute query
            result = query.limit(1000).execute()  # Get more records for better similarity matching
            
            if not result.data:
                return {"documents": [], "metadatas": [], "ids": [], "distances": [], "similarities": []}
            
            # Calculate similarities and rank results
            scored_results = []
            
            for record in result.data:
                try:
                    additional_data = record.get("additional_data", {})
                    stored_embedding = additional_data.get("embedding")
                    
                    if stored_embedding:
                        # Calculate cosine similarity
                        similarity = self._cosine_similarity(query_embedding, stored_embedding)
                        
                        if similarity >= similarity_threshold:
                            scored_results.append({
                                "content": record["message"],
                                "metadata": additional_data.get("memory_metadata", {}),
                                "id": additional_data.get("memory_id", record["id"]),
                                "similarity": similarity,
                                "distance": 1.0 - similarity,
                                "created_at": record["created_at"],
                                "email_id": record.get("email_id"),
                                "debtor_id": record.get("debtor_id")
                            })
                except Exception as e:
                    logger.warning(f"Error processing record for similarity: {e}")
                    continue
            
            # Sort by similarity (descending) and take top results
            scored_results.sort(key=lambda x: x["similarity"], reverse=True)
            top_results = scored_results[:n_results]
            
            # Format results to match ChromaDB format
            results = {
                "documents": [r["content"] for r in top_results],
                "metadatas": [r["metadata"] for r in top_results],
                "ids": [r["id"] for r in top_results],
                "distances": [r["distance"] for r in top_results],
                "similarities": [r["similarity"] for r in top_results]
            }
            
            logger.info(f"Semantic search found {len(top_results)} results with similarity >= {similarity_threshold}")
            return results
            
        except Exception as e:
            logger.error(f"Error in semantic search: {e}")
            return self._fallback_text_search(query_text, n_results, email_id, debtor_id)
    
    def _cosine_similarity(self, vec1: List[float], vec2: List[float]) -> float:
        """Calculate cosine similarity between two vectors."""
        try:
            # Convert to numpy arrays
            a = np.array(vec1)
            b = np.array(vec2)
            
            # Calculate cosine similarity
            dot_product = np.dot(a, b)
            norm_a = np.linalg.norm(a)
            norm_b = np.linalg.norm(b)
            
            if norm_a == 0 or norm_b == 0:
                return 0.0
            
            similarity = dot_product / (norm_a * norm_b)
            return float(similarity)
            
        except Exception as e:
            logger.error(f"Error calculating cosine similarity: {e}")
            return 0.0
    
    def _fallback_text_search(self, query_text: str, n_results: int, 
                             email_id: Optional[str], debtor_id: Optional[int]) -> Dict[str, Any]:
        """Fallback text search when vector search is not available."""
        try:
            query = self.supabase.table("system_logs").select("*")
            query = query.eq("module", "llm_memory_vector")
            query = query.ilike("message", f"%{query_text}%")
            
            if email_id:
                query = query.eq("email_id", email_id)
            if debtor_id:
                query = query.eq("debtor_id", debtor_id)
            
            result = query.order("created_at", desc=True).limit(n_results).execute()
            
            if not result.data:
                return {"documents": [], "metadatas": [], "ids": [], "distances": [], "similarities": []}
            
            # Format results
            documents = []
            metadatas = []
            ids = []
            
            for record in result.data:
                documents.append(record["message"])
                
                additional_data = record.get("additional_data", {})
                metadata = additional_data.get("memory_metadata", {})
                metadata.update({
                    "log_id": record["id"],
                    "created_at": record["created_at"],
                    "email_id": record.get("email_id"),
                    "debtor_id": record.get("debtor_id"),
                    "search_type": "text_fallback"
                })
                metadatas.append(metadata)
                
                record_id = additional_data.get("memory_id", record["id"])
                ids.append(record_id)
            
            return {
                "documents": documents,
                "metadatas": metadatas,
                "ids": ids,
                "distances": [0.5] * len(documents),  # Default distance for text search
                "similarities": [0.5] * len(documents)  # Default similarity for text search
            }
            
        except Exception as e:
            logger.error(f"Error in fallback text search: {e}")
            return {"documents": [], "metadatas": [], "ids": [], "distances": [], "similarities": []}
    
    def store_email_context(self, email_data: Dict[str, Any], extracted_order: Optional[Dict[str, Any]] = None) -> bool:
        """
        Store comprehensive email context with vector embeddings.
        
        Args:
            email_data: Email data dictionary
            extracted_order: Optional extracted order data
            
        Returns:
            bool: Success status
        """
        try:
            # Create comprehensive context document
            context_parts = [
                f"Email Subject: {email_data.get('subject', '')}",
                f"Email Sender: {email_data.get('sender', '')}",
                f"Email Body: {email_data.get('body', '')[:1000]}..."  # First 1000 chars
            ]
            
            if extracted_order:
                context_parts.extend([
                    f"Customer: {extracted_order.get('customer_name', '')}",
                    f"Debtor ID: {extracted_order.get('debtor_id', '')}",
                    f"Order Number: {extracted_order.get('customer_order_number', '')}",
                    f"Order Lines: {len(extracted_order.get('line_items', []))}"
                ])
            
            context_document = "\n".join(context_parts)
            
            # Prepare metadata
            metadata = {
                "type": "email_context",
                "subject": email_data.get('subject'),
                "sender": email_data.get('sender'),
                "has_order": extracted_order is not None,
                "timestamp": email_data.get('timestamp'),
                "content_length": len(context_document)
            }
            
            if extracted_order:
                metadata.update({
                    "customer_name": extracted_order.get('customer_name'),
                    "debtor_id": extracted_order.get('debtor_id'),
                    "order_number": extracted_order.get('customer_order_number'),
                    "order_lines_count": len(extracted_order.get('line_items', []))
                })
            
            # Store with vector embedding
            return self.add_memory_with_vectors(
                documents=[context_document],
                metadatas=[metadata],
                ids=[f"email_{email_data.get('id', datetime.now().isoformat())}"],
                email_id=email_data.get('id'),
                debtor_id=extracted_order.get('debtor_id') if extracted_order else None
            )
            
        except Exception as e:
            logger.error(f"Error storing email context: {e}")
            return False
    
    def get_relevant_context(self, query: str, customer_name: Optional[str] = None, 
                           debtor_id: Optional[int] = None, limit: int = 5,
                           use_semantic_search: bool = True) -> str:
        """
        Get relevant context using semantic search for LLM processing.
        
        Args:
            query: Context query
            customer_name: Optional customer filter
            debtor_id: Optional debtor ID filter
            limit: Maximum number of context items
            use_semantic_search: Whether to use vector similarity search
            
        Returns:
            str: Formatted context string
        """
        try:
            if use_semantic_search and self.embedding_model:
                # Use semantic search
                results = self.semantic_search(
                    query_text=query,
                    n_results=limit,
                    debtor_id=debtor_id,
                    similarity_threshold=0.3  # Lower threshold for more results
                )
            else:
                # Use basic text search
                results = self._fallback_text_search(query, limit, None, debtor_id)
            
            if not results["documents"]:
                return "No relevant context found."
            
            # Format context for LLM
            context_parts = ["=== RELEVANT CONTEXT (Semantic Search) ==="]
            
            for i, (doc, metadata, similarity) in enumerate(zip(
                results["documents"], 
                results["metadatas"], 
                results.get("similarities", [0.5] * len(results["documents"]))
            )):
                context_parts.append(f"\nContext {i+1} (Similarity: {similarity:.3f}):")
                context_parts.append(f"Created: {metadata.get('created_at', 'Unknown')}")
                
                if metadata.get('customer_name'):
                    context_parts.append(f"Customer: {metadata['customer_name']}")
                if metadata.get('subject'):
                    context_parts.append(f"Subject: {metadata['subject']}")
                if metadata.get('sender'):
                    context_parts.append(f"Sender: {metadata['sender']}")
                
                # Truncate long content
                content = doc[:300] + "..." if len(doc) > 300 else doc
                context_parts.append(f"Content: {content}")
                context_parts.append("-" * 50)
            
            return "\n".join(context_parts)
            
        except Exception as e:
            logger.error(f"Error getting relevant context: {e}")
            return "Error retrieving context."
    
    def store_order_in_memory(self, order_data: Dict[str, Any], email_id: Optional[str] = None) -> bool:
        """
        Store order processing context in memory for LLM reference with vector embeddings.
        
        Args:
            order_data: Order information to store
            email_id: Associated email ID
            
        Returns:
            bool: Success status
        """
        try:
            # Format order context for memory storage
            order_context = f"""
Order Processing Context:
Customer: {order_data.get('customer_name', 'Unknown')}
Debtor ID: {order_data.get('debtor_id', 'Unknown')}
Order Number: {order_data.get('customer_order_number', 'Unknown')}
Total Amount: ${order_data.get('total_amount', 0):.2f}
Line Items: {len(order_data.get('line_items', []))}
Status: {order_data.get('status', 'Unknown')}
Shipping: {order_data.get('shipping_method', 'Unknown')}
Processed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

Order Details:
{json.dumps(order_data.get('line_items', []), indent=2)}
"""
            
            metadata = {
                "type": "order_context",
                "customer_name": order_data.get('customer_name'),
                "debtor_id": order_data.get('debtor_id'),
                "order_number": order_data.get('customer_order_number'),
                "total_amount": order_data.get('total_amount'),
                "status": order_data.get('status'),
                "shipping_method": order_data.get('shipping_method'),
                "line_items_count": len(order_data.get('line_items', [])),
                "has_delivery_address": bool(order_data.get('delivery_address'))
            }
            
            return self.add_memory_with_vectors(
                documents=[order_context],
                metadatas=[metadata],
                ids=[f"order_{order_data.get('customer_order_number', datetime.now().isoformat())}"],
                email_id=email_id,
                debtor_id=order_data.get('debtor_id')
            )
            
        except Exception as e:
            logger.error(f"Error storing order in memory: {e}")
            return False

    def get_customer_context(self, debtor_id: int, limit: int = 10, 
                            use_semantic_search: bool = True) -> str:
        """
        Get customer-specific context using debtor ID filter.
        
        Args:
            debtor_id: Customer debtor ID to filter by
            limit: Maximum number of context items
            use_semantic_search: Whether to use vector similarity search
            
        Returns:
            str: Formatted customer context string
        """
        try:
            # Get customer details first
            customer_info = self._get_customer_info(debtor_id)
            customer_name = customer_info.get('customer_name', f'Customer {debtor_id}')
            
            # Use semantic search with customer filter
            if use_semantic_search and self.embedding_model:
                # Search for customer-related content
                query = f"customer orders {customer_name} purchases requirements"
                results = self.semantic_search(
                    query_text=query,
                    n_results=limit,
                    debtor_id=debtor_id,
                    similarity_threshold=0.2  # Lower threshold for customer-specific search
                )
            else:
                # Use basic text search with debtor_id filter
                results = self._fallback_text_search(customer_name, limit, None, debtor_id)
            
            if not results["documents"]:
                return f"No context found for customer {customer_name} (ID: {debtor_id})."
            
            # Format customer-specific context
            context_parts = [f"=== CUSTOMER CONTEXT: {customer_name} (ID: {debtor_id}) ==="]
            
            # Add customer info if available
            if customer_info:
                context_parts.append(f"Customer Info: {json.dumps(customer_info, indent=2)}")
                context_parts.append("-" * 50)
            
            # Add memory contexts
            for i, (doc, metadata, similarity) in enumerate(zip(
                results["documents"], 
                results["metadatas"], 
                results.get("similarities", [0.5] * len(results["documents"]))
            )):
                context_parts.append(f"\nMemory {i+1} (Similarity: {similarity:.3f}):")
                context_parts.append(f"Created: {metadata.get('created_at', 'Unknown')}")
                
                if metadata.get('type'):
                    context_parts.append(f"Type: {metadata['type']}")
                if metadata.get('order_number'):
                    context_parts.append(f"Order: {metadata['order_number']}")
                if metadata.get('subject'):
                    context_parts.append(f"Subject: {metadata['subject']}")
                
                # Truncate long content
                content = doc[:250] + "..." if len(doc) > 250 else doc
                context_parts.append(f"Content: {content}")
                context_parts.append("-" * 30)
            
            return "\n".join(context_parts)
            
        except Exception as e:
            logger.error(f"Error getting customer context: {e}")
            return f"Error retrieving context for customer {debtor_id}."
    
    def _get_customer_info(self, debtor_id: int) -> Dict[str, Any]:
        """Get customer information from the customers table."""
        try:
            result = self.supabase.table("customers")\
                .select("*")\
                .eq("debtor_id", debtor_id)\
                .limit(1)\
                .execute()
            
            if result.data:
                return result.data[0]
            else:
                return {"debtor_id": debtor_id, "customer_name": f"Customer {debtor_id}"}
                
        except Exception as e:
            logger.warning(f"Could not retrieve customer info for {debtor_id}: {e}")
            return {"debtor_id": debtor_id, "customer_name": f"Customer {debtor_id}"}

    # Backward compatibility methods
    def add_memory(self, documents: List[str], metadatas: Optional[List[Dict[str, Any]]] = None, 
                   ids: Optional[List[str]] = None) -> bool:
        """Backward compatibility method that uses vector embeddings."""
        return self.add_memory_with_vectors(documents, metadatas, ids)
    
    def query_memory(self, query_texts: List[str], n_results: int = 10, 
                     where: Optional[Dict[str, Any]] = None, 
                     email_id: Optional[str] = None,
                     debtor_id: Optional[int] = None) -> Dict[str, Any]:
        """Backward compatibility method that uses semantic search."""
        if not query_texts:
            return {"documents": [], "metadatas": [], "ids": [], "distances": [], "similarities": []}
        
        return self.semantic_search(
            query_text=query_texts[0],
            n_results=n_results,
            email_id=email_id,
            debtor_id=debtor_id
        )
    
    def delete_memory(self, ids: Optional[List[str]] = None, 
                      where: Optional[Dict[str, Any]] = None,
                      email_id: Optional[str] = None,
                      debtor_id: Optional[int] = None) -> bool:
        """Delete memory documents from Supabase."""
        try:
            if not any([ids, email_id, debtor_id]):
                logger.warning("No deletion criteria provided")
                return False
            
            if ids:
                # Delete by specific memory IDs
                for memory_id in ids:
                    result = self.supabase.table("system_logs")\
                        .delete()\
                        .eq("module", "llm_memory_vector")\
                        .contains("additional_data", {"memory_id": memory_id})\
                        .execute()
            else:
                # Delete by email_id or debtor_id
                query = self.supabase.table("system_logs").delete()
                query = query.eq("module", "llm_memory_vector")
                
                if email_id:
                    query = query.eq("email_id", email_id)
                if debtor_id:
                    query = query.eq("debtor_id", debtor_id)
                
                result = query.execute()
            
            logger.info("Memory deletion completed")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting memory: {e}")
            return False


# Singleton instance for global use
enhanced_memory_client = None

def get_enhanced_memory_client() -> EnhancedSupabaseMemoryClient:
    """Get or create the global enhanced Supabase memory client instance."""
    global enhanced_memory_client
    if enhanced_memory_client is None:
        enhanced_memory_client = EnhancedSupabaseMemoryClient()
    return enhanced_memory_client


if __name__ == "__main__":
    # Test the enhanced memory client
    try:
        print("Testing Enhanced Supabase Memory Client with Vector Search...")
        
        # Initialize client
        client = EnhancedSupabaseMemoryClient()
        
        # Test adding memory with vectors
        test_docs = [
            "Brady Australia placed order #12345 for safety equipment including hard hats and vests",
            "RSEA Safety requested quote for protective gear for construction workers",
            "Woolworths approved purchase order for team systems equipment delivery to Melbourne"
        ]
        
        test_metadata = [
            {"customer": "Brady Australia", "type": "order", "order_number": "12345", "products": ["hard hats", "vests"]},
            {"customer": "RSEA Safety", "type": "quote_request", "industry": "construction"},
            {"customer": "Woolworths", "type": "purchase_order", "location": "Melbourne"}
        ]
        
        print("Adding memories with vector embeddings...")
        success = client.add_memory_with_vectors(
            documents=test_docs,
            metadatas=test_metadata,
            ids=["test_vector_1", "test_vector_2", "test_vector_3"]
        )
        print(f"Add memory success: {success}")
        
        # Test semantic search
        print("\nTesting semantic search...")
        search_queries = [
            "safety equipment orders",
            "construction protective gear",
            "Melbourne delivery orders"
        ]
        
        for query in search_queries:
            print(f"\nSearching for: '{query}'")
            results = client.semantic_search(query, n_results=3, similarity_threshold=0.2)
            
            print(f"Found {len(results['documents'])} results:")
            for i, (doc, similarity) in enumerate(zip(results['documents'], results.get('similarities', []))):
                print(f"  {i+1}. Similarity: {similarity:.3f} - {doc[:100]}...")
        
        # Test context retrieval
        print("\nTesting context retrieval...")
        context = client.get_relevant_context("customer orders for safety equipment", limit=2)
        print(f"Context preview: {context[:200]}...")
        
        # Cleanup
        print("\nCleaning up test data...")
        client.delete_memory(ids=["test_vector_1", "test_vector_2", "test_vector_3"])
        
        print("\n✅ Enhanced memory client test completed successfully!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
