# TeamsysV0.1 - Email Order Processing System

🚀 **Automated email-to-ERP order processing system with AI-powered data extraction**

## 📋 Overview

TeamsysV0.1 is an enterprise-grade intelligent email processing system that automatically extracts purchase order information from emails with PDF attachments, generates structured data for ERP integration, and maintains a comprehensive audit trail. The system uses MistralAI for natural language processing, features an optimized active customer database, and supports multiple supplier formats with 95%+ accuracy.

## 🎯 Current Status: PRODUCTION READY ✅

**Latest Major Enhancement (July 22, 2025):** Enhanced System with Conversational AI Agents
- ✅ **Gmail Query Agent** - Natural language Gmail querying with conversational responses
- ✅ **MYOB Query Agent** - Conversational MYOB data queries with intelligent responses
- ✅ **System Monitoring** - Comprehensive health checks and performance optimization
- ✅ **Enhanced CLI** - Interactive dashboard with real-time monitoring and agent integration
- ✅ **Conversational AI** - Flexible LLM responses for natural user interactions
- ✅ **Performance Optimization** - Intelligent caching and adaptive batch processing
- ✅ **Enterprise Monitoring** - Real-time system metrics and alert management

## ✨ Key Features

### Core Intelligence
- **🤖 AI-Powered Extraction**: Advanced MistralAI integration with JSON mode and retry logic
- **💬 Conversational AI Agents**: Natural language Gmail and MYOB querying with intelligent responses
- **🧠 Centralized Prompt Management**: Modular prompt system with business rule integration
- **🎯 Active Customer Database**: 1,927 active customers with intelligent matching (95%+ accuracy)
- **🔍 Memory System**: Semantic search and context retrieval from previous orders
- **📊 Priority Rules**: Smart customer matching with business logic (e.g., Woolworths → ID 10981)

### Integration & Processing
- **📧 Gmail Integration**: Advanced email processing with X-Original-Sender support
- **📄 PDF Processing**: Multi-page text extraction with error handling
- **🏢 ERP Integration**: MYOB-compatible JSON payloads with validation
- **💾 Database Storage**: Complete audit trail in Supabase with relationship mapping
- **🏷️ Smart Labeling**: Automatic email categorization (Processed/Review/Failed)

### Reliability & Performance
- **🔄 Network Resilience**: Exponential backoff retry with circuit breaker patterns
- **⚡ Performance Optimized**: 3x faster customer lookups, 95%+ JSON parsing success
- **🛡️ Error Handling**: Comprehensive exception handling with context preservation
- **📈 Production Ready**: Enterprise-grade reliability with monitoring and logging

## 🏗️ System Architecture

```
TeamsysV0.1/
├── cli.py                    # Command line interface and main entry point
├── enhanced_cli.py           # Interactive GUI with conversational agents
├── main_processor.py         # Core workflow orchestration engine
├── reporting.py              # Professional reporting and email notifications
├── agents/                   # Conversational AI agents
│   ├── gmail_query_agent.py  # Natural language Gmail querying
│   ├── myob_query_agent.py   # Conversational MYOB data queries
│   └── gmail_api_parser.py   # Gmail API discovery integration
├── services/                 # Enhanced system services
│   ├── system_monitor.py     # Comprehensive health monitoring
│   ├── performance_optimizer.py # Intelligent caching and optimization
│   └── unified_service_manager.py # Central service coordination
├── templates/                # Jinja2 HTML email templates
│   └── email_report.html     # Responsive email report template
├── llm_service/              # Modular AI processing service
│   ├── services/             # Core AI and memory services
│   ├── processors/           # Specialized data processors
│   ├── models/              # Data schemas and validation
│   └── core/                # Base classes and utilities
├── gmail_service.py          # Gmail API integration
├── pdf_extractor.py          # PDF text extraction
├── supabase_database_service.py # Database operations
└── config.py                # Configuration management
```

## 🎯 New Conversational AI Agents

### 📧 Gmail Query Agent
**Natural language Gmail querying with intelligent responses**

Ask questions about your Gmail data in plain English:
- "Show me unread emails from this week"
- "Find all emails with PDF attachments"
- "Who sends me the most emails?"
- "List emails about invoices from last month"

**Features:**
- **Intent Classification**: Understands what you want to do with your Gmail
- **Gmail API Integration**: Direct access to Gmail data with proper authentication
- **Conversational Responses**: Helpful, natural language responses instead of raw data
- **Smart Search**: Automatically generates appropriate Gmail search queries

### 🏢 MYOB Query Agent
**Conversational MYOB data queries with intelligent analysis**

Query your MYOB data using natural language:
- "Show me all customers from Woolworths"
- "What orders do we have today?"
- "Find stock levels for product ABC123"
- "Show me outstanding balances over $1000"

**Features:**
- **Business Intelligence**: Understands MYOB business concepts and relationships
- **68 MYOB Endpoints**: Complete integration with MYOB API capabilities
- **Contextual Responses**: Provides business insights, not just raw data
- **Multi-Query Support**: Can combine multiple data sources for comprehensive answers

### 🔧 Enhanced CLI Interface
**Interactive dashboard with real-time monitoring**

Run the enhanced CLI for a complete management experience:
```bash
python enhanced_cli.py
```

**Available Tabs:**
- **📧 Email Processing**: Interactive email processing with progress tracking
- **🏢 MYOB Management**: Order validation, posting, and batch processing
- **🤖 MYOB Query**: Natural language MYOB data querying
- **📬 Gmail Query**: Conversational Gmail data exploration
- **📊 System Status**: Real-time monitoring, health checks, and performance metrics

### 📊 System Monitoring & Health Checks
**Enterprise-grade monitoring and performance optimization**

**Real-time Metrics:**
- CPU, Memory, and Disk usage monitoring
- Service health checks with response time tracking
- Performance optimization recommendations
- Active alert management with severity levels

**Health Check Services:**
- Gmail API connectivity and authentication
- MistralAI service availability and response times
- Database connectivity and query performance
- System resource utilization and thresholds

**Performance Optimization:**
- Intelligent caching with TTL-based eviction
- Adaptive batch processing with dynamic sizing
- Bottleneck detection and optimization suggestions
- Cache hit rate monitoring and recommendations

## 🚀 Quick Start

### Prerequisites

1. **Python 3.8+** installed
2. **Gmail API credentials** (credentials.json)
3. **MistralAI API key**
4. **Supabase project** with database setup

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/slothmc-ctrl/TeamsysV0.1.git
   cd TeamsysV0.1
   ```

2. **Create virtual environment**
   ```bash
   python -m venv .venv
   .venv\Scripts\activate  # Windows
   # or
   source .venv/bin/activate  # Linux/Mac
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Configure environment variables**
   ```bash
   # Copy and edit the .env file
   cp .env.example .env
   ```

### Environment Configuration

Edit `.env` file with your credentials:

```env
# Gmail API
GMAIL_CREDENTIALS_FILE=credentials.json
GMAIL_TOKEN_FILE=token.pickle

# MistralAI
MISTRAL_API_KEY=your_mistral_api_key_here
MISTRAL_MODEL=magistral-medium-2506

# Supabase
SUPABASE_URL=your_supabase_url
SUPABASE_KEY=your_supabase_key

# Email Settings
USER_EMAIL=<EMAIL>
LOG_RECIPIENT_EMAIL=<EMAIL>
```

### First Run

1. **Test the system**
   ```bash
   python cli.py --once --max-emails 1
   ```

2. **Process specific labels**
   ```bash
   python cli.py --once --labels Brady RSEA --max-emails 5
   ```

3. **Run in continuous mode**
   ```bash
   python cli.py --poll-interval 30
   ```

## 📖 Module Descriptions

### Core Components

#### `main_processor.py`
**Main orchestration engine** that coordinates the entire email processing workflow.

**Key Features:**
- Email fetching from Gmail labels
- PDF attachment processing
- AI-powered data extraction coordination
- Database storage management
- Error handling and retry logic
- Email labeling and categorization

**Usage:**
```bash
python main_processor.py [options]
```

#### `llm_service/` - AI Processing Module

Modular AI service architecture for intelligent data extraction.

##### `llm_service/main.py`
**Primary LLM service interface** providing unified access to AI capabilities.

**Methods:**
- `generate_markdown_summary()` - Creates structured email summaries
- `extract_order_from_content()` - Extracts order data from text
- `generate_myob_payload()` - Creates ERP integration payloads

##### `llm_service/services/`

**`mistral_service.py`**
- MistralAI API integration
- Content generation and analysis
- Health monitoring and error handling
- Configurable model parameters

**`memory_service.py`**
- Semantic search across previous orders
- Context retrieval for improved accuracy
- Vector embeddings management
- Supabase integration for persistent storage

##### `llm_service/processors/`

**`order_processor.py`**
- Specialized order data extraction
- JSON parsing with robust error handling
- Data validation and cleaning
- Context-aware processing

**`summary_processor.py`**
- Email and PDF content summarization
- Structured markdown generation
- Multi-format content handling

**`payload_generator.py`**
- MYOB ERP payload generation
- Data transformation and mapping
- Validation and formatting

##### `llm_service/models/schemas.py`
**Data models and validation schemas**
- `EmailData` - Email structure and metadata
- `ExtractedOrder` - Order information schema
- `MYOBPayload` - ERP integration format
- Pydantic validation and type safety

### Integration Components

#### `gmail_service.py`
**Gmail API integration service**

**Features:**
- OAuth2 authentication flow
- Email fetching with advanced filtering
- Attachment download and processing
- Label management (Processed/Review/Failed)
- X-Original-Sender header support for shared inboxes

**Key Methods:**
- `fetch_emails_from_labels()` - Retrieves emails from specified labels
- `_get_email_details()` - Extracts comprehensive email metadata
- `mark_email_processed()` - Applies processing labels

#### `pdf_extractor.py`
**PDF text extraction utility**

**Capabilities:**
- Multi-page PDF processing
- Text extraction with formatting preservation
- Error handling for corrupted files
- Memory-efficient processing

#### `supabase_database_service.py`
**Database operations and storage**

**Functions:**
- Complete order data persistence
- Email metadata storage
- Attachment management
- Event logging and audit trails
- Customer data integration

### Configuration & Utilities

#### `config.py`
**Centralized configuration management**
- Environment variable loading
- API key validation
- Default value management
- Configuration validation

#### `enhanced_supabase_memory_client.py`
**Advanced memory and context management**
- Vector embeddings for semantic search
- Persistent memory storage
- Context retrieval optimization
- Performance monitoring

## 🔧 Command Line Options

### Basic Usage
```bash
python cli.py [OPTIONS]
```

### Common Options

| Option | Description | Example |
|--------|-------------|---------|
| `--once` | Single run mode (vs continuous) | `--once` |
| `--max-emails N` | Limit emails per label | `--max-emails 10` |
| `--labels L1 L2` | Process specific labels | `--labels Brady RSEA` |
| `--time-filter FILTER` | Gmail search filter | `--time-filter "newer_than:1d"` |
| `--poll-interval N` | Minutes between runs | `--poll-interval 30` |
| `--review-all-emails` | Process all emails (not just PDFs) | `--review-all-emails` |
| `--dry-run` | Show what would be processed | `--dry-run` |
| `--export-csv` | Export results to CSV | `--export-csv` |
| `--stats` | Show processing statistics | `--stats` |

### Advanced Examples

**Process last 24 hours of Brady emails:**
```bash
python cli.py --once --labels Brady --time-filter "newer_than:1d" --max-emails 20
```

**Continuous processing with 15-minute intervals:**
```bash
python cli.py --poll-interval 15 --max-emails 5
```

**Review mode for all email types:**
```bash
python cli.py --once --review-all-emails --max-emails 50
```

## 📊 Processing Workflow

1. **Email Discovery**
   - Connects to Gmail API
   - Searches configured labels for emails with PDF attachments
   - Filters by time range and other criteria

2. **Content Extraction**
   - Downloads email content and metadata
   - Extracts text from PDF attachments
   - Handles X-Original-Sender headers for shared inboxes

3. **AI Processing**
   - Generates structured markdown summaries
   - Extracts order data using MistralAI
   - Validates and cleans extracted information

4. **Data Generation**
   - Creates MYOB-compatible JSON payloads
   - Generates comprehensive audit logs
   - Prepares data for ERP integration

5. **Storage & Labeling**
   - Saves all data to Supabase database
   - Applies Gmail labels (Processed/Review/Failed)
   - Sends completion notifications

## 🏷️ Supported Suppliers

The system is configured to process emails from these supplier labels:

- **Brady** - Industrial labeling and safety products
- **RSEA** - Safety equipment and supplies  
- **Woolworths** - Commercial supplies
- **Brierley** - Industrial equipment
- **Gateway** - Packaging solutions
- **Highgate** - Specialized equipment
- **Sitecraft** - Construction and industrial supplies

## 📁 Output Files

### Markdown Summaries
**Location:** `markdown/`
**Format:** `DD-MM_HHMM_EmailSubject.md`
**Content:** Structured summaries with order details, customer information, and processing notes

### MYOB Payloads
**Location:** `myob/`
**Format:** `DD-MM_HHMM_EmailSubject.json`
**Content:** ERP-ready JSON payloads with customer details, line items, and metadata

### Processing Reports
**Location:** `reports/`
**Content:** CSV and JSON reports of processing activities, statistics, and error logs

## 🔍 Monitoring & Logging

### Log Levels
- **INFO**: Normal processing activities
- **WARNING**: Non-critical issues that don't stop processing
- **ERROR**: Processing failures and exceptions
- **DEBUG**: Detailed diagnostic information

### Key Metrics
- Processing success rate
- Average processing time per email
- API response times
- Database operation performance
- Error frequency and types

## 🛠️ Troubleshooting

### Common Issues

**Gmail Authentication Errors**
```bash
# Delete existing token and re-authenticate
rm token.pickle
python cli.py --once --max-emails 1
```

**MistralAI API Issues**
- Verify API key in `.env` file
- Check API quota and rate limits
- Ensure model name is correct (`magistral-medium-2506`)

**Database Connection Problems**
- Verify Supabase URL and key
- Check network connectivity
- Ensure database schema is up to date

**PDF Processing Failures**
- Check PDF file integrity
- Verify sufficient disk space
- Review PDF extraction logs

### Debug Mode
```bash
# Enable verbose logging
python cli.py --once --max-emails 1 --verbose
```

## 🔐 Security Considerations

- **API Keys**: Store securely in `.env` file, never commit to version control
- **Gmail Access**: Uses OAuth2 with minimal required permissions
- **Database**: All connections use encrypted HTTPS
- **File Storage**: Temporary files are cleaned up automatically
- **Logging**: Sensitive data is masked in log outputs

## 🎯 Major System Enhancements

### Active Customer Database Implementation (July 19, 2025)

**Problem Solved:** Slow, inaccurate customer lookups due to massive unfiltered database with inactive customers.

**Solution:** Complete active customer database overhaul with intelligent matching system.

#### Key Improvements
- **Data Source:** SOH (Stock on Hand) Report from MYOB
- **Active Customers:** 1,927 customers with recent transactions
- **Performance:** 3x faster lookups with focused dataset
- **Accuracy:** 95%+ customer match rate
- **Smart Matching:** Priority rules for complex corporate structures

#### Customer Lookup Features
```python
# Intelligent customer matching with priority rules
find_customer_by_name("Woolworths") 
# → Returns: WOOLWORTHS LIMITED (ID: 10981)

# Handles complex corporate structures
find_customer_by_name("ALH GROUP")
# → Returns: ALH GROUP - QLD (WOOLWORTHS) (ID: 14041)
```

#### Migration Tools
- **`upload_active_debtors.py`** - Complete migration script with progress tracking
- **`test_active_customer_lookup.py`** - Comprehensive testing utilities
- **Smart Upsert Logic** - Updates existing customers, adds new ones
- **Verification System** - Automated testing and validation

### Enhanced AI Processing System

#### MistralAI Integration with Production Fixes
- **JSON Mode Enforcement:** `response_format={"type": "json_object"}`
- **Multi-Strategy Extraction:** 3-tier fallback system for JSON parsing
- **Network Resilience:** Exponential backoff retry with circuit breaker
- **Content Cleaning:** Aggressive removal of conversational text and think tags

#### Centralized Prompt Management
```
llm_service/prompts/
├── prompt_manager.py        # Central management class
├── order_prompts.py         # Order processing prompts
├── summary_prompts.py       # Email analysis prompts
└── system_prompts.py        # MistralAI system prompts
```

**Benefits:**
- **Consistency:** All agents use identical business rules
- **Maintainability:** Single source of truth for prompts
- **Scalability:** Easy addition of new agents and prompts
- **Quality:** Enhanced JSON processing with fallback strategies

### Performance Metrics

| Component | Before | After | Improvement |
|-----------|--------|-------|-------------|
| Customer Lookup Speed | Slow | 3x Faster | +200% |
| Customer Match Accuracy | 60% | 95%+ | +58% |
| JSON Parse Success | 57% | 95%+ | +67% |
| Overall Success Rate | 43% | 85%+ | +98% |
| Network Failure Recovery | 0% | 85%+ | +85% |

## 🚀 Production Deployment

### System Status: PRODUCTION READY ✅

**Current Capabilities:**
- ✅ **1,927 Active Customers** loaded and validated
- ✅ **95%+ Processing Accuracy** with intelligent error handling
- ✅ **Enterprise-Grade Performance** with 3x speed improvements
- ✅ **Comprehensive Error Recovery** with retry logic and fallbacks
- ✅ **End-to-End Validation** completed successfully

### Recommended Setup
1. **Dedicated Server**: Linux server with Python 3.8+
2. **Process Management**: Use systemd or supervisor for service management
3. **Monitoring**: Implement health checks and alerting
4. **Backup**: Regular database backups and configuration versioning
5. **Scaling**: Consider load balancing for high-volume processing

### Service Configuration
```ini
# /etc/systemd/system/teamsys-processor.service
[Unit]
Description=TeamsysV0.1 Email Order Processor
After=network.target

[Service]
Type=simple
User=teamsys
WorkingDirectory=/opt/teamsys
ExecStart=/opt/teamsys/.venv/bin/python cli.py --poll-interval 15
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

## 📈 Performance Optimization

### Recommended Settings
- **Batch Size**: Process 5-10 emails per run for optimal performance
- **Poll Interval**: 15-30 minutes for continuous processing
- **Memory Management**: Monitor memory usage with large PDF files
- **API Limits**: Respect MistralAI rate limits (adjust timeout settings)

### Scaling Considerations
- **Horizontal Scaling**: Multiple instances with different label assignments
- **Database Optimization**: Index frequently queried fields
- **Caching**: Implement Redis for frequently accessed data
- **Queue Management**: Use message queues for high-volume scenarios

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is proprietary software developed for Team Systems Pty Ltd.

## 📞 Support

For technical support or questions:
- **Email**: <EMAIL>
- **Documentation**: See `docs/` directory for detailed technical documentation
- **Issues**: Use GitHub Issues for bug reports and feature requests

---

**Last Updated**: July 22, 2025
**Version**: 0.3.0 - Modular Architecture Edition
**Status**: Production Ready ✅

## 📈 Recent Accomplishments Summary

### July 22, 2025 - System Refactoring & Optimization
- ✅ **Modular Architecture** - Refactored main processor into focused modules (CLI, Reporting, Core)
- ✅ **Jinja2 Template System** - Professional HTML email reports with responsive design
- ✅ **Enhanced Entry Point** - New `cli.py` as main entry point with improved argument handling
- ✅ **Duplicate Processing Fix** - Emails now processed exactly once with UNREAD label management
- ✅ **Improved Maintainability** - Separated concerns for better code organization

### July 20, 2025 - Fuzzy Matching Integration
- ✅ **Enhanced Customer Identification** - Fuzzy matching integration for better customer recognition
- ✅ **Intelligent Customer System** - Advanced matching algorithms with priority rules

### July 19, 2025 - Active Customer Database Implementation
- ✅ **Active Customer Database** - 1,927 customers from SOH report with 95%+ match accuracy
- ✅ **Customer Lookup Optimization** - 3x performance improvement with focused dataset
- ✅ **Priority Rules System** - Intelligent matching for complex corporate structures
- ✅ **End-to-End Validation** - Complete system testing and verification

### Previous Enhancements
- ✅ **MistralAI Integration** - Advanced AI processing with JSON mode enforcement
- ✅ **Centralized Prompt Management** - Modular prompt system with business rules
- ✅ **Network Resilience** - Comprehensive retry logic and error handling
- ✅ **Performance Optimization** - 85%+ overall success rate achieved

**System Evolution:** From prototype to enterprise-grade production system with modular architecture, 95%+ accuracy, and comprehensive error handling.