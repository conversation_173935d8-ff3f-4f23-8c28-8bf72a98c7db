<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Order Processing Report</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: system-ui, -apple-system, sans-serif;
        background: #f5f5f5;
        min-height: 100vh;
        padding: 20px;
        line-height: 1.4;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        background: white;
        border: 1px solid #ddd;
        overflow: hidden;
      }

      .header {
        background: #0b233d;
        color: white;
        padding: 30px;
        text-align: center;
        border-bottom: 3px solid #0a1f35;
      }

      .header-logo {
        margin-bottom: 15px;
      }

      .header-logo img {
        max-height: 60px;
        width: auto;
      }

      .header h1 {
        font-size: 1.8rem;
        font-weight: normal;
        margin-bottom: 8px;
      }

      .header .subtitle {
        font-size: 1rem;
        margin-bottom: 15px;
      }

      .timestamp {
        background: rgba(255, 255, 255, 0.1);
        padding: 8px 16px;
        display: inline-block;
        font-weight: normal;
        border: 1px solid rgba(255, 255, 255, 0.2);
      }

      .stats-section {
        padding: 30px;
        background: #fafafa;
        border-bottom: 1px solid #ddd;
      }

      .stats-table {
        width: 100%;
        border-collapse: collapse;
        background: white;
        border: 1px solid #ddd;
      }

      .stats-table th {
        background: #0b233d;
        color: white;
        padding: 12px;
        text-align: center;
        font-weight: normal;
        border-right: 1px solid #0a1f35;
      }

      .stats-table th:last-child {
        border-right: none;
      }

      .stats-table td {
        padding: 15px;
        text-align: center;
        border-right: 1px solid #ddd;
        border-bottom: 1px solid #ddd;
      }

      .stats-table td:last-child {
        border-right: none;
      }

      .stat-label {
        color: #666;
        font-size: 0.9rem;
      }

      .content {
        padding: 30px;
      }

      .section-title {
        font-size: 1.3rem;
        font-weight: normal;
        color: #0b233d;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid #0b233d;
      }

      .email-table {
        width: 100%;
        border-collapse: collapse;
        background: white;
        border: 1px solid #ddd;
      }

      .email-table th {
        background: #0b233d;
        color: white;
        padding: 12px;
        text-align: left;
        font-weight: normal;
        border-right: 1px solid #0a1f35;
      }

      .email-table th:last-child {
        border-right: none;
      }

      .email-table td {
        padding: 12px;
        border-bottom: 1px solid #ddd;
        border-right: 1px solid #ddd;
        vertical-align: top;
      }

      .email-table td:last-child {
        border-right: none;
      }

      .email-table tr:nth-child(even) {
        background: #f9f9f9;
      }

      .email-table tr:last-child td {
        border-bottom: none;
      }

      .status-analysed {
        color: #27ae60;
        font-weight: normal;
      }

      .status-review {
        color: #f39c12;
        font-weight: normal;
      }

      .status-error {
        color: #e74c3c;
        font-weight: normal;
      }

      .email-from {
        font-weight: normal;
        color: #2c3e50;
        word-break: break-all;
      }

      .email-subject {
        color: #2c3e50;
        font-weight: normal;
      }

      .email-summary {
        color: #555;
        font-size: 0.9rem;
        line-height: 1.4;
      }

      .email-time {
        color: #666;
        font-size: 0.9rem;
        white-space: nowrap;
      }

      .no-emails {
        text-align: center;
        padding: 40px 20px;
        color: #666;
        font-style: italic;
      }

      .footer {
        background: #ecf0f1;
        padding: 20px 30px;
        text-align: center;
        color: #666;
        border-top: 1px solid #bdc3c7;
        font-size: 0.9rem;
      }

      .footer p {
        margin: 5px 0;
      }

      .footer strong {
        color: #2c3e50;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <!-- Header Section -->
      <div class="header">
        <div class="header-logo">
          <img
            src="https://www.teamsystems.net.au/assets/images/new-theme-2023/icon/Teamsystems_Logo_1.png?1752550429"
            alt="Teamsystems Logo"
          />
        </div>
        <h1>Order Processing Report</h1>
        <div class="subtitle">TeamsysV0.1 - Email Processing Complete</div>
        <div class="timestamp">
{{ timestamp|default('Unknown') }}
        </div>
      </div>

      <!-- Statistics Section -->
      <div class="stats-section">
        <table class="stats-table">
          <thead>
            <tr>
              <th>Total Emails</th>
              <th>Orders Analysed</th>
              <th>Need Review</th>
              <th>Success Rate</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>
                <span class="stat-number">{{ total_emails|default(0) }}</span>
                <div class="stat-label">Analysed</div>
              </td>
              <td>
                <span class="stat-number"
                  >{{ processed_count|default(0) }}</span
                >
                <div class="stat-label">Extracted</div>
              </td>
              <td>
                <span class="stat-number">{{ review_count|default(0) }}</span>
                <div class="stat-label">Manual Review</div>
              </td>
              <td>
                <span class="stat-number"
                  >{{ "%.0f"|format(success_rate|default(0)) }}%</span
                >
                <div class="stat-label">Success</div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Email Details Section -->
      <div class="content">
        <h2 class="section-title">Email Processing Details</h2>

        {% if has_emails and emails %}
        <table class="email-table">
          <thead>
            <tr>
              <th style="width: 15%">Date/Time</th>
              <th style="width: 20%">From</th>
              <th style="width: 25%">Subject</th>
              <th style="width: 30%">Summary</th>
              <th style="width: 10%">Status</th>
            </tr>
          </thead>
          <tbody>
            {% for email in emails %}
            <tr>
              <td class="email-time">
                {{ email.timestamp|default('Unknown') }}
              </td>
              <td class="email-from">
                {{ email.sender|default('Unknown Sender') }}
              </td>
              <td class="email-subject">
                {{ email.subject|default('No Subject') }}
              </td>
              <td class="email-summary">
                {{ email.summary|default('No summary available') }}
              </td>
              <td class="{{ email.status_class|default('status-error') }}">
                {{ email.status|default('Unknown') }}
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
        {% else %}
        <div class="no-emails">
          <p>No emails were processed in this run.</p>
        </div>
        {% endif %}
      </div>

      <!-- Footer Section -->
      <div class="footer">
        <p>
          Files saved to: <strong>markdown/</strong> and
          <strong>myob/</strong> directories
        </p>
        <p>Generated by TeamsysV0.1 Email Order Processor</p>
      </div>
    </div>
  </body>
</html>
