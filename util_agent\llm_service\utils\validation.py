"""Validation utilities."""

from typing import Any, Dict, Optional
from ..models.schemas import ExtractedOrder, MYOBPayload


class ValidationService:
    """Service for validating data structures."""
    
    @staticmethod
    def validate_extracted_order(order_data: Dict[str, Any]) -> bool:
        """Validate extracted order data."""
        try:
            ExtractedOrder(**order_data)
            return True
        except Exception as e:
            raise ValueError(f"Invalid order data: {str(e)}")
    
    @staticmethod
    def validate_myob_payload(payload: Dict[str, Any]) -> bool:
        """Validate MYOB payload."""
        try:
            MYOBPayload(**payload)
            return True
        except Exception as e:
            raise ValueError(f"Invalid MYOB payload: {str(e)}")
    
    @staticmethod
    def validate_customer_id(customer_name: str) -> Optional[int]:
        """Validate and return customer ID based on name."""
        customer_mapping = {
            "WOOLWORTHS LIMITED": 10981,
            "ENDEAVOUR GROUP": 21570,
            "RSEA": 6207,
            "BRADY": 5760,
            "GATEWAY": 13924,
            "BRIERLEY": 11139,
            "REFLEX EQUIP": 11197,
            "SITECRAFT": 1365,
            "BLACKWOODS": 5228,
            "EQUIP2GO": 20078,
            "OMNI GROUP": 6375,
            "DANDENONG WHEELS AND CASTORS": 2996,
            "ACE INDUSTRIAL LEONGATHA": 18488
        }
        
        for name, id in customer_mapping.items():
            if name.upper() in customer_name.upper():
                return id
        
        return None