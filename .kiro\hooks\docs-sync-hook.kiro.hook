{"enabled": true, "name": "Documentation Sync", "description": "Monitors Python source files and configuration changes to automatically update project documentation in README.md and docs folder", "version": "1", "when": {"type": "fileCreated", "patterns": ["*.py", "agents/*.py", "llm_service/**/*.py", "services/*.py", "tests/*.py", "*.json", "*.md", "requirements.txt", ".env"]}, "then": {"type": "askAgent", "prompt": "Source files have been modified in this Python project. Please review the changes and update the documentation accordingly. Focus on:\n\n1. Update README.md if there are significant architectural changes, new features, or modified workflows\n2. Update relevant documentation in the /docs folder if specific components have changed\n3. Ensure documentation reflects current functionality, API changes, and configuration requirements\n4. Update any technical guides or implementation summaries that may be affected\n5. Maintain consistency between code and documentation\n\nPlease analyze the changed files and determine what documentation updates are needed to keep everything current and accurate."}}