"""
Gmail Query Agent - Natural language Gmail querying with LLM intelligence.
"""
import json
import logging
import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta

from gmail_service import GmailService
from llm_service.main import LLMService
from agents.gmail_api_parser import Gmail<PERSON>IParser

logger = logging.getLogger(__name__)


class GmailQueryAgent:
    """LLM-powered natural language Gmail querying."""
    
    def __init__(self):
        self.gmail_service = GmailService()
        self.llm_service = LLMService()
        self.api_parser = GmailAPIParser()
        self.endpoints_catalog = self.api_parser.get_endpoints_by_category()
        self.search_capabilities = self.api_parser.get_search_capabilities()
        
    async def process_natural_query(self, user_query: str) -> str:
        """Process natural language Gmail query."""
        try:
            logger.info(f"Processing Gmail query: {user_query}")
            
            # Step 1: Classify query intent
            intent = await self._classify_gmail_intent(user_query)
            logger.debug(f"Query intent: {intent}")
            
            # Step 2: Generate Gmail API calls
            api_calls = await self._generate_gmail_api_calls(user_query, intent)
            logger.debug(f"Generated API calls: {api_calls}")
            
            # Step 3: Execute and format results
            results = await self._execute_and_format_gmail(api_calls, user_query)
            
            return results
            
        except Exception as e:
            logger.error(f"Gmail query processing failed: {e}")
            return f"❌ Query processing failed: {str(e)}"
    
    async def _classify_gmail_intent(self, query: str) -> Dict:
        """Classify Gmail query into operation categories."""
        classification_prompt = f"""
        You are a helpful Gmail assistant. Analyze this Gmail query and determine what the user wants to do:
        
        Query: "{query}"
        
        Determine the primary intent from these categories:
        - email_search: Finding specific emails or messages
        - email_management: Organizing, labeling, archiving emails
        - thread_operations: Working with email conversations
        - contact_analysis: Information about senders/recipients
        - attachment_operations: Finding or managing attachments
        - label_management: Creating, modifying Gmail labels
        - draft_operations: Working with draft emails
        - analytics: Email statistics and analysis
        
        Also extract any specific entities mentioned like:
        - Email addresses, date ranges, keywords, labels, subjects, file types
        
        Respond in a helpful, conversational way explaining what you understand the user wants to do.
        """
        
        try:
            # Use generate_content_flexible for non-JSON responses
            response = await self.llm_service.mistral_service.generate_content_flexible(classification_prompt)
            
            # Parse the response to extract intent (simplified approach)
            intent_mapping = {
                "search": "email_search",
                "find": "email_search", 
                "show": "email_search",
                "list": "email_search",
                "label": "label_management",
                "organize": "email_management",
                "who": "contact_analysis",
                "sender": "contact_analysis",
                "attachment": "attachment_operations",
                "pdf": "attachment_operations",
                "excel": "attachment_operations",
                "conversation": "thread_operations",
                "thread": "thread_operations",
                "draft": "draft_operations",
                "count": "analytics",
                "how many": "analytics",
                "statistics": "analytics"
            }
            
            # Simple intent detection based on keywords
            primary_intent = "email_search"  # default
            query_lower = query.lower()
            for keyword, intent in intent_mapping.items():
                if keyword in query_lower:
                    primary_intent = intent
                    break
            
            return {
                "primary_intent": primary_intent,
                "confidence": 0.8,
                "secondary_intents": [],
                "extracted_entities": {},
                "reasoning": response,
                "llm_response": response
            }
            
        except Exception as e:
            logger.error(f"Intent classification failed: {e}")
            return {
                "primary_intent": "email_search",
                "confidence": 0.5,
                "secondary_intents": [],
                "extracted_entities": {},
                "reasoning": "Fallback to basic email search",
                "llm_response": "I'll help you search your Gmail."
            }
    
    async def _generate_gmail_api_calls(self, query: str, intent: Dict) -> List[Dict]:
        """Generate specific Gmail API calls based on intent."""
        # Simplified approach - map common intents to API calls
        primary_intent = intent.get("primary_intent", "email_search")
        
        # Basic API call mapping based on intent
        if primary_intent == "label_management" or "label" in query.lower():
            return [{
                "method": "GET",
                "endpoint": "users.labels.list",
                "parameters": {},
                "description": "List all Gmail labels"
            }]
        elif primary_intent == "analytics" or any(word in query.lower() for word in ["count", "how many", "statistics"]):
            return [{
                "method": "GET",
                "endpoint": "users.messages.list",
                "parameters": {"maxResults": 100},
                "description": "Get messages for counting and analysis"
            }]
        elif "attachment" in query.lower() or "pdf" in query.lower():
            return [{
                "method": "GET",
                "endpoint": "users.messages.list",
                "parameters": {"q": "has:attachment", "maxResults": 20},
                "description": "Search for emails with attachments"
            }]
        elif "unread" in query.lower():
            return [{
                "method": "GET",
                "endpoint": "users.messages.list",
                "parameters": {"q": "is:unread", "maxResults": 20},
                "description": "Search for unread emails"
            }]
        else:
            # Default to basic search
            search_query = query
            # Extract simple search terms
            if "from" in query.lower():
                # Try to extract email addresses
                import re
                emails = re.findall(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', query)
                if emails:
                    search_query = f"from:{emails[0]}"
            
            return [{
                "method": "GET",
                "endpoint": "users.messages.list",
                "parameters": {"q": search_query, "maxResults": 10},
                "description": "Search Gmail messages"
            }]
    
    async def _execute_and_format_gmail(self, api_calls: List[Dict], original_query: str) -> str:
        """Execute Gmail API calls and format results."""
        try:
            results = []
            
            for api_call in api_calls:
                endpoint = api_call.get("endpoint", "")
                parameters = api_call.get("parameters", {})
                description = api_call.get("description", "")
                
                logger.info(f"Executing: {endpoint} - {description}")
                
                # Execute the API call
                if endpoint == "users.messages.list":
                    result = await self._execute_message_list(parameters)
                elif endpoint == "users.messages.get":
                    result = await self._execute_message_get(parameters)
                elif endpoint == "users.threads.list":
                    result = await self._execute_thread_list(parameters)
                elif endpoint == "users.labels.list":
                    result = await self._execute_labels_list(parameters)
                else:
                    result = {"error": f"Unsupported endpoint: {endpoint}"}
                
                results.append({
                    "endpoint": endpoint,
                    "description": description,
                    "result": result
                })
            
            # Format results for user
            return await self._format_results_for_user(results, original_query)
            
        except Exception as e:
            logger.error(f"Gmail API execution failed: {e}")
            return f"❌ Failed to execute Gmail operations: {str(e)}"
    
    async def _execute_message_list(self, parameters: Dict) -> Dict:
        """Execute users.messages.list API call."""
        try:
            if not self.gmail_service.service:
                return {"error": "Gmail service not initialized"}
            
            # Build request parameters
            request_params = {"userId": "me"}
            request_params.update(parameters)
            
            # Execute the request
            response = self.gmail_service.service.users().messages().list(**request_params).execute()
            messages = response.get('messages', [])
            
            # Get details for first few messages
            detailed_messages = []
            for msg in messages[:5]:  # Limit to first 5 for performance
                try:
                    msg_detail = self.gmail_service.service.users().messages().get(
                        userId='me',
                        id=msg['id'],
                        format='metadata'
                    ).execute()
                    
                    # Extract key information
                    headers = msg_detail.get('payload', {}).get('headers', [])
                    subject = next((h['value'] for h in headers if h['name'].lower() == 'subject'), 'No Subject')
                    sender = next((h['value'] for h in headers if h['name'].lower() == 'from'), 'Unknown Sender')
                    date = next((h['value'] for h in headers if h['name'].lower() == 'date'), 'Unknown Date')
                    
                    detailed_messages.append({
                        "id": msg['id'],
                        "subject": subject,
                        "sender": sender,
                        "date": date,
                        "snippet": msg_detail.get('snippet', '')
                    })
                    
                except Exception as e:
                    logger.warning(f"Failed to get message details for {msg['id']}: {e}")
            
            return {
                "total_messages": len(messages),
                "messages_shown": len(detailed_messages),
                "messages": detailed_messages
            }
            
        except Exception as e:
            logger.error(f"Message list execution failed: {e}")
            return {"error": str(e)}
    
    async def _execute_message_get(self, parameters: Dict) -> Dict:
        """Execute users.messages.get API call."""
        try:
            if not self.gmail_service.service:
                return {"error": "Gmail service not initialized"}
            
            message_id = parameters.get("id")
            if not message_id:
                return {"error": "Message ID required"}
            
            msg = self.gmail_service.service.users().messages().get(
                userId='me',
                id=message_id,
                format=parameters.get("format", "full")
            ).execute()
            
            # Extract key information
            headers = msg.get('payload', {}).get('headers', [])
            subject = next((h['value'] for h in headers if h['name'].lower() == 'subject'), 'No Subject')
            sender = next((h['value'] for h in headers if h['name'].lower() == 'from'), 'Unknown Sender')
            recipient = next((h['value'] for h in headers if h['name'].lower() == 'to'), 'Unknown Recipient')
            date = next((h['value'] for h in headers if h['name'].lower() == 'date'), 'Unknown Date')
            
            return {
                "id": message_id,
                "subject": subject,
                "sender": sender,
                "recipient": recipient,
                "date": date,
                "snippet": msg.get('snippet', ''),
                "labels": msg.get('labelIds', [])
            }
            
        except Exception as e:
            logger.error(f"Message get execution failed: {e}")
            return {"error": str(e)}
    
    async def _execute_thread_list(self, parameters: Dict) -> Dict:
        """Execute users.threads.list API call."""
        try:
            if not self.gmail_service.service:
                return {"error": "Gmail service not initialized"}
            
            request_params = {"userId": "me"}
            request_params.update(parameters)
            
            response = self.gmail_service.service.users().threads().list(**request_params).execute()
            threads = response.get('threads', [])
            
            return {
                "total_threads": len(threads),
                "threads": threads[:10]  # Limit for performance
            }
            
        except Exception as e:
            logger.error(f"Thread list execution failed: {e}")
            return {"error": str(e)}
    
    async def _execute_labels_list(self, parameters: Dict) -> Dict:
        """Execute users.labels.list API call."""
        try:
            if not self.gmail_service.service:
                return {"error": "Gmail service not initialized"}
            
            response = self.gmail_service.service.users().labels().list(userId='me').execute()
            labels = response.get('labels', [])
            
            # Categorize labels
            system_labels = [l for l in labels if l.get('type') == 'system']
            user_labels = [l for l in labels if l.get('type') == 'user']
            
            return {
                "total_labels": len(labels),
                "system_labels": len(system_labels),
                "user_labels": len(user_labels),
                "labels": [{"name": l["name"], "id": l["id"], "type": l.get("type", "user")} for l in labels]
            }
            
        except Exception as e:
            logger.error(f"Labels list execution failed: {e}")
            return {"error": str(e)}
    
    async def _format_results_for_user(self, results: List[Dict], original_query: str) -> str:
        """Format API results into user-friendly response."""
        formatting_prompt = f"""
        Format these Gmail API results into a clear, user-friendly response:
        
        Original Query: "{original_query}"
        
        API Results:
        {json.dumps(results, indent=2)}
        
        Create a natural language response that:
        1. Directly answers the user's question
        2. Presents data in an organized, readable format
        3. Includes relevant details like sender, subject, date
        4. Uses emojis and formatting for clarity
        5. Mentions any limitations or errors
        
        Format as plain text with line breaks and emojis.
        """
        
        try:
            response = await self.llm_service.mistral_service.generate_content_flexible(formatting_prompt)
            return response
            
        except Exception as e:
            logger.error(f"Result formatting failed: {e}")
            # Fallback to basic formatting
            return self._basic_format_results(results, original_query)
    
    def _basic_format_results(self, results: List[Dict], original_query: str) -> str:
        """Basic fallback formatting for results."""
        output = [f"📧 Gmail Query Results for: '{original_query}'", "=" * 50, ""]
        
        for result in results:
            endpoint = result.get("endpoint", "")
            description = result.get("description", "")
            data = result.get("result", {})
            
            output.append(f"🔍 {description}")
            
            if "error" in data:
                output.append(f"❌ Error: {data['error']}")
            elif endpoint == "users.messages.list":
                total = data.get("total_messages", 0)
                messages = data.get("messages", [])
                output.append(f"📊 Found {total} messages (showing {len(messages)}):")
                
                for msg in messages:
                    output.append(f"  • {msg.get('subject', 'No Subject')}")
                    output.append(f"    From: {msg.get('sender', 'Unknown')}")
                    output.append(f"    Date: {msg.get('date', 'Unknown')}")
                    output.append("")
            
            elif endpoint == "users.labels.list":
                total = data.get("total_labels", 0)
                user_labels = data.get("user_labels", 0)
                output.append(f"🏷️ Found {total} labels ({user_labels} custom):")
                
                labels = data.get("labels", [])
                for label in labels[:10]:  # Show first 10
                    icon = "📁" if label.get("type") == "system" else "🏷️"
                    output.append(f"  {icon} {label.get('name', 'Unknown')}")
            
            output.append("")
        
        return "\n".join(output)
    
    def get_example_queries(self) -> List[str]:
        """Get example Gmail queries for user guidance."""
        return [
            "Show me <NAME_EMAIL> in the last week",
            "Find all emails with 'invoice' in the subject",
            "List unread emails from today", 
            "Show me emails with PDF attachments",
            "Who sends me the most emails?",
            "Show me all emails labeled 'Important'",
            "List all my Gmail labels",
            "Find the longest email conversation this month",
            "How many emails did I receive this week?",
            "Show me emails with Excel attachments"
        ]