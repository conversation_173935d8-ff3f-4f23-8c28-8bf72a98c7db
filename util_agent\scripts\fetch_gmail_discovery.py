"""
Fetch Gmail API discovery document for LLM agent integration.
"""
import requests
import json
import logging
from pathlib import Path

logger = logging.getLogger(__name__)


def fetch_gmail_discovery_doc():
    """Fetch Gmail API v1 discovery document."""
    try:
        url = "https://www.googleapis.com/discovery/v1/apis/gmail/v1/rest"
        
        logger.info(f"Fetching Gmail API discovery document from: {url}")
        response = requests.get(url, timeout=30)
        response.raise_for_status()
        
        discovery_doc = response.json()
        
        # Save to file for LLM agent
        output_path = Path('gmail_api_discovery.json')
        with open(output_path, 'w') as f:
            json.dump(discovery_doc, f, indent=2)
        
        logger.info(f"Gmail API discovery document saved to: {output_path}")
        logger.info(f"Document contains {len(discovery_doc.get('resources', {}))} resource types")
        
        return discovery_doc
        
    except requests.RequestException as e:
        logger.error(f"Failed to fetch Gmail discovery document: {e}")
        return None
    except Exception as e:
        logger.error(f"Error processing Gmail discovery document: {e}")
        return None


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    fetch_gmail_discovery_doc()