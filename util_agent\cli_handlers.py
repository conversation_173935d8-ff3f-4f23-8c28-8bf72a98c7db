"""
CLI Handlers - Command handlers for the enhanced CLI system.
Provides both interactive and command-line interfaces.
"""
import asyncio
import json
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime

from services.unified_service_manager import UnifiedServiceManager
from services.enhanced_myob_manager import EnhancedMYOBManager
from agents.myob_query_agent import MYOBQueryAgent

logger = logging.getLogger(__name__)


class CLIHandlers:
    """Handlers for all CLI commands."""
    
    def __init__(self):
        """Initialize all service handlers."""
        self.service_manager = UnifiedServiceManager()
        self.myob_manager = EnhancedMYOBManager()
        self.query_agent = MYOBQueryAgent()
        logger.info("CLI Handlers initialized")
    
    async def handle_email_command(self, args):
        """Handle email processing commands."""
        try:
            print(f"\n{'='*60}")
            print("EMAIL PROCESSING")
            print(f"{'='*60}")
            
            if args.once:
                print(f"Processing emails from labels: {', '.join(args.labels)}")
                print(f"Max emails per label: {args.max_emails}")
                
                if args.time_filter:
                    print(f"Time filter: {args.time_filter}")
                
                # Progress callback for command line
                def progress_callback(message: str, current: int, total: int):
                    print(f"[{current}/{total}] {message}")
                
                results = await self.service_manager.process_emails_interactive(
                    labels=args.labels,
                    max_emails=args.max_emails,
                    time_filter=args.time_filter,
                    callback=progress_callback
                )
                
                self._display_email_results(results)
                
            else:
                print("Continuous processing mode not implemented in CLI handlers")
                print("Use the interactive GUI for continuous processing")
                
        except Exception as e:
            print(f"❌ Email processing failed: {str(e)}")
            logger.error(f"Email command failed: {e}")
    
    async def handle_myob_command(self, args):
        """Handle MYOB management commands."""
        try:
            print(f"\n{'='*60}")
            print("MYOB MANAGEMENT")
            print(f"{'='*60}")
            
            if args.myob_action == 'list':
                orders = self.myob_manager.get_order_summary_table()
                self._display_orders_table(orders, args.format)
                
            elif args.myob_action == 'process':
                if args.batch:
                    print("🚀 Starting batch processing of all orders...")
                    
                    # Get all order IDs
                    orders_data = self.myob_manager.get_order_summary_table()
                    order_ids = [row[0] for row in orders_data]
                    
                    if not order_ids:
                        print("📭 No orders to process")
                        return
                    
                    # Progress callback
                    def progress_callback(message: str, current: int, total: int):
                        print(f"[{current}/{total}] {message}")
                    
                    results = await self.service_manager.process_order_batch(
                        order_ids, callback=progress_callback
                    )
                    
                    self._display_process_results(results)
                    
                elif args.order_ids:
                    print(f"Processing specific orders: {', '.join(args.order_ids)}")
                    
                    results = []
                    for order_id in args.order_ids:
                        if args.validate_only:
                            success, message, details = await self.myob_manager.validate_order_interactive(order_id)
                            results.append((order_id, success, message))
                            print(f"{'✅' if success else '❌'} {order_id}: {message}")
                        else:
                            success, message, details = await self.myob_manager.post_order_interactive(order_id)
                            results.append((order_id, success, message))
                            print(f"{'✅' if success else '❌'} {order_id}: {message}")
                    
                    self._display_process_results(results)
                
                else:
                    print("❌ Please specify --batch or --order-ids for processing")
                    
            elif args.myob_action == 'stats':
                stats = self.myob_manager.get_myob_statistics()
                self._display_myob_stats(stats)
                
            else:
                print(f"❌ Unknown MYOB action: {args.myob_action}")
                
        except Exception as e:
            print(f"❌ MYOB command failed: {str(e)}")
            logger.error(f"MYOB command failed: {e}")
    
    async def handle_query_command(self, args):
        """Handle natural language queries."""
        try:
            print(f"\n{'='*60}")
            print("MYOB NATURAL LANGUAGE QUERY")
            print(f"{'='*60}")
            
            print(f"Query: {args.query_text}")
            print("⏳ Processing...")
            
            result = await self.query_agent.process_natural_query(args.query_text)
            
            print(f"\n{'='*60}")
            print("QUERY RESULTS")
            print(f"{'='*60}")
            
            if args.format == 'json':
                try:
                    # Try to parse as JSON for pretty printing
                    json_result = json.loads(result)
                    print(json.dumps(json_result, indent=2))
                except:
                    # If not JSON, print as text
                    print(result)
            elif args.format == 'table':
                self._display_as_table(result)
            else:
                print(result)
            
            if args.save_results:
                with open(args.save_results, 'w') as f:
                    f.write(result)
                print(f"\n💾 Results saved to: {args.save_results}")
                
        except Exception as e:
            print(f"❌ Query processing failed: {str(e)}")
            logger.error(f"Query command failed: {e}")
    
    def handle_interactive_command(self, args):
        """Launch interactive GUI."""
        try:
            print(f"\n{'='*60}")
            print("LAUNCHING INTERACTIVE GUI")
            print(f"{'='*60}")
            
            print(f"Theme: {args.theme}")
            print("Starting Textual interface...")
            print("💡 Tip: Use Ctrl+C to exit the GUI")
            print()
            
            # Use subprocess to run the GUI in a separate process
            import subprocess
            import sys
            
            # Run the interactive GUI script
            result = subprocess.run([
                sys.executable, 
                "run_interactive_gui.py"
            ], cwd=".")
            
            if result.returncode == 0:
                print("✅ GUI closed successfully")
            else:
                print(f"⚠️ GUI exited with code: {result.returncode}")
            
        except Exception as e:
            print(f"❌ Failed to launch interactive GUI: {str(e)}")
            logger.error(f"Interactive command failed: {e}")
    
    def handle_status_command(self, args):
        """Handle system status commands."""
        try:
            print(f"\n{'='*60}")
            print("SYSTEM STATUS")
            print(f"{'='*60}")
            
            status = self.service_manager.get_system_status()
            
            print(f"Timestamp: {status.get('timestamp', 'Unknown')}")
            print()
            
            # Email processing status
            email_status = status.get("email_processing", {})
            print("📧 Email Processing:")
            print(f"  Recent orders: {email_status.get('recent_orders_count', 0)}")
            print(f"  Gmail connected: {'✅' if email_status.get('gmail_connected') else '❌'}")
            print(f"  LLM service: {'✅' if email_status.get('llm_service_healthy') else '❌'}")
            print()
            
            # MYOB status
            myob_status = status.get("myob_management", {})
            print("🏢 MYOB Management:")
            print(f"  Pending orders: {myob_status.get('pending_orders_count', 0)}")
            print(f"  Service connected: {'✅' if myob_status.get('myob_service_connected') else '❌'}")
            print()
            
            # Services status
            services = status.get("services", {})
            print("🔧 Services:")
            for service, health in services.items():
                print(f"  {service}: {'✅' if health == 'healthy' else '❌'}")
            
            if args.export:
                filename = f"system_status_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                with open(filename, 'w') as f:
                    json.dump(status, f, indent=2, default=str)
                print(f"\n💾 Status exported to: {filename}")
                
        except Exception as e:
            print(f"❌ Status command failed: {str(e)}")
            logger.error(f"Status command failed: {e}")
    
    def _display_email_results(self, results: List[Dict[str, Any]]):
        """Display email processing results in table format."""
        if not results:
            print("\n📭 No emails processed")
            return
        
        print(f"\n📊 EMAIL PROCESSING RESULTS ({len(results)} orders)")
        print("=" * 80)
        
        # Table headers
        headers = ["Order ID", "Customer", "PO Number", "Lines", "Status"]
        print(f"{headers[0]:<15} {headers[1]:<20} {headers[2]:<15} {headers[3]:<6} {headers[4]:<10}")
        print("-" * 80)
        
        # Table rows
        for result in results:
            # Handle ProcessedOrder objects from models.py
            if hasattr(result, 'email_id'):
                order_id = str(result.email_id)[:14]
                
                # Extract customer name from extracted_data
                if result.extracted_data and hasattr(result.extracted_data, 'customer_name'):
                    customer = str(result.extracted_data.customer_name)[:19]
                else:
                    customer = "N/A"
                
                # Extract PO number from extracted_data
                if result.extracted_data and hasattr(result.extracted_data, 'customer_po'):
                    po_number = str(result.extracted_data.customer_po)[:14]
                else:
                    po_number = "N/A"
                
                # Count order lines
                lines = str(len(result.order_line_items))
                
                # Status based on whether we have extracted data
                status = "✅ Done" if result.extracted_data else "❌ Failed"
            else:
                # Fallback for dict objects
                order_id = result.get("order_id", "N/A")[:14]
                customer = result.get("customer_name", "N/A")[:19]
                po_number = result.get("customer_po", "N/A")[:14]
                lines = str(len(result.get("order_lines", [])))
                status = "✅ Done"
            
            print(f"{order_id:<15} {customer:<20} {po_number:<15} {lines:<6} {status:<10}")
        
        print("-" * 80)
        print(f"Total processed: {len(results)} orders")
    
    def _display_orders_table(self, orders: List[List[str]], format_type: str = "table"):
        """Display orders in specified format."""
        if not orders:
            print("\n📭 No pending orders")
            return
        
        if format_type == "json":
            # Convert to JSON format
            headers = ["order_id", "debtor_id", "customer_po", "lines", "quantity", "city", "status"]
            json_data = []
            for row in orders:
                order_dict = {headers[i]: row[i] for i in range(min(len(headers), len(row)))}
                json_data.append(order_dict)
            print(json.dumps(json_data, indent=2))
            
        elif format_type == "csv":
            # CSV format
            headers = ["Order ID", "Debtor ID", "Customer PO", "Lines", "Quantity", "City", "Status"]
            print(",".join(headers))
            for row in orders:
                print(",".join(str(cell) for cell in row))
                
        else:  # table format
            print(f"\n📋 PENDING MYOB ORDERS ({len(orders)} orders)")
            print("=" * 90)
            
            # Table headers
            headers = ["Order ID", "Debtor ID", "Customer PO", "Lines", "Qty", "City", "Status"]
            print(f"{headers[0]:<12} {headers[1]:<10} {headers[2]:<15} {headers[3]:<6} {headers[4]:<6} {headers[5]:<15} {headers[6]:<8}")
            print("-" * 90)
            
            # Table rows
            for row in orders:
                formatted_row = [str(cell)[:width] for cell, width in zip(row, [11, 9, 14, 5, 5, 14, 7])]
                print(f"{formatted_row[0]:<12} {formatted_row[1]:<10} {formatted_row[2]:<15} {formatted_row[3]:<6} {formatted_row[4]:<6} {formatted_row[5]:<15} {formatted_row[6]:<8}")
            
            print("-" * 90)
            print(f"Total pending: {len(orders)} orders")
    
    def _display_process_results(self, results: List[tuple]):
        """Display order processing results."""
        if not results:
            print("\n📭 No results to display")
            return
        
        successful = sum(1 for _, success, _ in results if success)
        failed = len(results) - successful
        
        print(f"\n📊 PROCESSING RESULTS")
        print("=" * 60)
        print(f"Total: {len(results)} | Successful: {successful} | Failed: {failed}")
        print("=" * 60)
        
        for order_id, success, message in results:
            status = "✅" if success else "❌"
            print(f"{status} {order_id}: {message}")
        
        print("=" * 60)
    
    def _display_myob_stats(self, stats: Dict[str, Any]):
        """Display MYOB statistics."""
        print(f"\n📊 MYOB STATISTICS")
        print("=" * 40)
        
        if "error" in stats:
            print(f"❌ Error: {stats['error']}")
            return
        
        print(f"Total Orders: {stats.get('total_orders', 0)}")
        print(f"Total Lines: {stats.get('total_lines', 0)}")
        print(f"Total Quantity: {stats.get('total_quantity', 0)}")
        print(f"Unique Customers: {stats.get('unique_customers', 0)}")
        print(f"Avg Lines/Order: {stats.get('average_lines_per_order', 0):.1f}")
        print(f"Avg Qty/Order: {stats.get('average_quantity_per_order', 0):.1f}")
        print(f"Status: {stats.get('status', 'Unknown')}")
        
        customers = stats.get('customers', [])
        if customers:
            print(f"\nCustomers: {', '.join(map(str, customers[:10]))}")
            if len(customers) > 10:
                print(f"... and {len(customers) - 10} more")
    
    def _display_as_table(self, result: str):
        """Display result as a formatted table (basic implementation)."""
        # This is a simple implementation - could be enhanced with proper table parsing
        print(result)
    
    async def health_check(self) -> Dict[str, bool]:
        """Perform system health check."""
        return await self.service_manager.health_check()