"""
Enhanced base prompt templates and components for all agents.
Updated for v0.3.0 - Modular Architecture Edition
"""

from typing import Dict, Any
from dataclasses import dataclass


@dataclass
class PromptComponent:
    """Individual prompt component that can be combined."""
    name: str
    content: str
    category: str
    priority: int = 0


class BasePrompts:
    """Enhanced base prompt components used across all agents."""
    
    # Core system identity - Enhanced with self-awareness
    SYSTEM_IDENTITY = PromptComponent(
        name="system_identity",
        content="""You are an intelligent AI Agent working for 'Team Systems', an Australian material handling equipment importer and distributor.
You are capable of performing complex tasks, such as searching the internet, analysing emails, reading documents, and using tools.
These tools will scale according to the requirements of the business.
If you require a new tool or an API, you have the self-awareness to request it.""",
        category="system",
        priority=10
    )
    
    # JSON enforcement - Critical for structured output
    JSON_ENFORCEMENT = PromptComponent(
        name="json_enforcement",
        content="""CRITICAL INSTRUCTIONS:
- You MUST respond with ONLY a valid JSON object
- Do NOT include any explanations, thinking process, or markdown formatting
- Do NOT use <think> tags or any reasoning text
- Do NOT include any text before or after the JSON
- Return pure JSON only""",
        category="format",
        priority=9
    )
    
    # Company context - Enhanced with order confirmation handling
    COMPANY_CONTEXT = PromptComponent(
        name="company_context",
        content="""COMPANY CONTEXT:
- Team Systems operates multiple e-commerce stores: "Equip2go", "Castor Solutions", "Teamsystems.net.au"
- You will receive emails with order confirmations from these websites in the shared inbox - these do NOT require action
- Always identify "Team Systems" as the supplier; any other business name is the account_name (customer)
- Colleagues have email domains ending in @teamsystems.net.au, otherwise they are customers""",
        category="context",
        priority=8
    )
    
    # Email processing rules - Enhanced with priority and intent recognition
    EMAIL_RULES = PromptComponent(
        name="email_rules",
        content="""EMAIL PROCESSING RULES:
- If subject begins with RE: or FW: do not action
- Emails to "<EMAIL>" are critical and must be actioned
- Prioritize accurate email_intent recognition using keywords: "quote", "price", "stock", "General", "Purchase Order"
- Use default values for missing information: "" for strings, null for objects, quantity 1 for price/stock checks
- Capture critical instructions that don't fit other fields (specific invoice emails, labeling notes)""",
        category="rules",
        priority=7
    )
    
    # Product/SKU handling
    SKU_RULES = PromptComponent(
        name="sku_rules",
        content="""PRODUCT/SKU RULES:
- Remove all whitespace from SKUs before processing
- Map customer SKUs to internal SKUs: "FPS09K-PL3GST" → "MONSTAR3", "EQLB8012" → "TSSU-ORA"
- If SKU contains "Freight", transform to "CUSTOMER_FREIGHT" and capture unit value
- Provide detailed item description if SKU is ambiguous or non-standard""",
        category="rules",
        priority=6
    )
    
    @classmethod
    def get_component(cls, name: str) -> PromptComponent:
        """Get a specific prompt component by name."""
        return getattr(cls, name.upper())
    
    @classmethod
    def get_components_by_category(cls, category: str) -> list[PromptComponent]:
        """Get all components in a specific category."""
        components = []
        for attr_name in dir(cls):
            attr = getattr(cls, attr_name)
            if isinstance(attr, PromptComponent) and attr.category == category:
                components.append(attr)
        return sorted(components, key=lambda x: x.priority, reverse=True)
    
    @classmethod
    def build_prompt_section(cls, category: str) -> str:
        """Build a prompt section from all components in a category."""
        components = cls.get_components_by_category(category)
        return "\n\n".join([comp.content for comp in components])