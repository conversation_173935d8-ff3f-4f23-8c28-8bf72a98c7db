"""
Test script to demonstrate the centralized prompt system.
"""

import asyncio
import os
from llm_service.prompts.prompt_manager import prompt_manager

def test_centralized_prompts():
    """Test the centralized prompt system."""
    
    print("🚀 Testing Centralized Prompt System")
    print("=" * 50)
    
    # Test 1: List available prompts
    print("\n📋 Available Prompts:")
    for prompt_name in prompt_manager.list_available_prompts():
        info = prompt_manager.get_prompt_info(prompt_name)
        print(f"  • {prompt_name}: {info['description']}")
    
    # Test 2: Business rules loading
    print(f"\n📖 Business Rules Loaded:")
    rules = prompt_manager.get_all_business_rules()
    if rules:
        print("  ✅ Business rules successfully loaded from templates/rules.txt")
        print(f"  📊 Rules sections: {list(prompt_manager.business_rules.keys())}")
    else:
        print("  ⚠️  No business rules found")
    
    # Test 3: Generate sample prompts
    print(f"\n🔧 Sample Prompt Generation:")
    
    # Order extraction prompt
    sample_content = "Purchase Order #12345 from ACME Corp for 10x Widget A"
    order_prompt = prompt_manager.get_order_extraction_prompt(sample_content)
    print(f"  📦 Order Extraction Prompt: {len(order_prompt)} characters")
    
    # Email summary prompt
    email_summary_prompt = prompt_manager.get_email_summary_prompt(
        "Please send quote for 5x conveyor belts", 
        "Quote Request - Conveyor Belts"
    )
    print(f"  📧 Email Summary Prompt: {len(email_summary_prompt)} characters")
    
    # System prompts
    json_enforcement = prompt_manager.get_json_enforcement_prompt()
    print(f"  🔒 JSON Enforcement Prompt: {len(json_enforcement)} characters")
    
    # Test 4: Prompt enhancement
    print(f"\n⚡ Prompt Enhancement:")
    base_prompt = "Extract order data from this content"
    
    strict_enhanced = prompt_manager.enhance_prompt_for_json_strict(base_prompt)
    fallback_enhanced = prompt_manager.enhance_prompt_for_json_fallback(base_prompt)
    
    print(f"  📈 Base prompt: {len(base_prompt)} chars")
    print(f"  🎯 Strict enhanced: {len(strict_enhanced)} chars")
    print(f"  🔄 Fallback enhanced: {len(fallback_enhanced)} chars")
    
    # Test 5: Export prompts
    print(f"\n💾 Exporting Prompts:")
    try:
        prompt_manager.export_prompts_to_json("test_prompts_export.json")
        print("  ✅ Prompts exported successfully")
    except Exception as e:
        print(f"  ❌ Export failed: {e}")
    
    print(f"\n🎉 Centralized Prompt System Test Complete!")
    print("=" * 50)

def show_sample_prompts():
    """Show sample prompts to demonstrate the system."""
    
    print("\n📝 Sample Prompt Outputs:")
    print("=" * 50)
    
    # Show order extraction prompt
    print("\n🔍 ORDER EXTRACTION PROMPT:")
    print("-" * 30)
    sample_order_prompt = prompt_manager.get_order_extraction_prompt(
        "PO #12345 from Gateway Packaging\nShip to: 123 Main St\n10x WIDGET-A"
    )
    print(sample_order_prompt[:500] + "..." if len(sample_order_prompt) > 500 else sample_order_prompt)
    
    # Show email summary prompt
    print("\n📧 EMAIL SUMMARY PROMPT:")
    print("-" * 30)
    sample_summary_prompt = prompt_manager.get_email_summary_prompt(
        "Hi, can you please provide a quote for 5 conveyor belts? We need them by next week.",
        "Quote Request - Urgent"
    )
    print(sample_summary_prompt[:500] + "..." if len(sample_summary_prompt) > 500 else sample_summary_prompt)

if __name__ == "__main__":
    test_centralized_prompts()
    show_sample_prompts()