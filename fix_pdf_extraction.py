#!/usr/bin/env python3
"""
Fix PDF extraction and process Brady orders with real PDF content.
"""

from services.enhanced_gmail_service import EnhancedGmailService
from services.llm_service import LLMService
import fitz  # PyMuPDF
import json

def extract_pdf_text_safely(pdf_bytes):
    """Safely extract text from PDF bytes."""
    try:
        # Create a new document from bytes each time
        pdf_doc = fitz.open(stream=pdf_bytes, filetype="pdf")
        
        text_content = ""
        for page_num in range(pdf_doc.page_count):
            page = pdf_doc[page_num]
            text_content += page.get_text()
            text_content += "\n\n"
        
        pdf_doc.close()  # Properly close the document
        return text_content.strip()
        
    except Exception as e:
        print(f"Error extracting PDF text: {e}")
        return ""

def main():
    print('🚀 Processing Brady Orders with Fixed PDF Extraction')
    
    # Initialize services
    enhanced_gmail = EnhancedGmailService()
    llm_service = LLMService()
    
    # Fetch Brady emails
    print('📧 Fetching Brady emails...')
    emails = enhanced_gmail.fetch_emails_with_ai_filtering(
        labels=['Brady'],
        days_back=7,
        ai_filter=True,
        min_confidence=0.6
    )
    
    print(f'Found {len(emails)} Brady emails')
    
    if not emails:
        print('❌ No emails found')
        return
    
    orders_found = 0
    payloads_generated = 0
    
    # Process emails with PDF attachments
    for i, email in enumerate(emails[:3]):  # Process first 3
        print(f'\n📧 Processing Email {i+1}: {email.subject}')
        print(f'From: {email.sender}')
        print(f'Attachments: {len(email.attachments)}')
        
        # Look for PDF attachments
        pdf_content = ""
        for attachment in email.attachments:
            if attachment.get("filename", "").lower().endswith(".pdf") and attachment.get("data"):
                filename = attachment["filename"]
                pdf_data = attachment["data"]
                
                print(f'📄 Extracting text from {filename}...')
                
                # Use our safe PDF extraction
                pdf_text = extract_pdf_text_safely(pdf_data)
                
                if pdf_text:
                    print(f'✅ Extracted {len(pdf_text)} characters from PDF')
                    pdf_content += f"\n\nPDF: {filename}\n{pdf_text}"
                else:
                    print(f'❌ No text extracted from {filename}')
        
        if pdf_content:
            # Combine email info with PDF content
            full_content = f"""Subject: {email.subject}
From: {email.sender}
Email Body: {email.body}

PDF Content:
{pdf_content}"""
            
            print(f'🔄 Extracting order from combined content ({len(full_content)} chars)...')
            
            # Extract order data
            order_data = llm_service.extract_order_data(full_content)
            
            if order_data and order_data.get("order_lines"):
                orders_found += 1
                print(f'✅ Order with line items extracted!')
                
                # Display order details
                print(f'\n📦 Order Details:')
                if 'customer_details' in order_data:
                    customer = order_data['customer_details']
                    print(f'  Customer ID: {customer.get("debtor_id", "N/A")}')
                    print(f'  PO Number: {customer.get("customer_order_number", "N/A")}')
                
                if 'order_lines' in order_data:
                    print(f'  Order Lines: {len(order_data["order_lines"])} items')
                    for j, line in enumerate(order_data["order_lines"]):
                        print(f'    {j+1}. {line.get("stockcode", "N/A")}: {line.get("orderquantity", 0)} units')
                
                if 'X_SHIPVIA' in order_data:
                    print(f'  Shipping: {order_data["X_SHIPVIA"]}')
                
                # Generate MYOB payload
                try:
                    from models import ExtractedOrder, CustomerDetails, OrderLine
                    
                    customer_details = CustomerDetails(
                        debtor_id=order_data["customer_details"]["debtor_id"],
                        customer_order_number=order_data["customer_details"].get("customer_order_number")
                    )
                    
                    order_lines = [
                        OrderLine(
                            stockcode=line["stockcode"],
                            orderquantity=line["orderquantity"]
                        ) for line in order_data["order_lines"]
                    ]
                    
                    extracted_order = ExtractedOrder(
                        customer_details=customer_details,
                        order_lines=order_lines,
                        X_SHIPVIA=order_data.get("X_SHIPVIA"),
                        order_status=order_data.get("order_status", 3)
                    )
                    
                    # Generate MYOB payload
                    myob_payload = llm_service.generate_myob_payload_direct(extracted_order)
                    payloads_generated += 1
                    
                    print(f'\n💼 MYOB Payload Generated:')
                    print(json.dumps(myob_payload, indent=2))
                    
                    # Save payload to file
                    po_number = customer.get('customer_order_number', 'unknown')
                    filename = f"myob/brady_real_order_{po_number}.json"
                    with open(filename, 'w') as f:
                        json.dump(myob_payload, f, indent=2)
                    print(f'💾 Saved to: {filename}')
                    
                except Exception as e:
                    print(f'⚠️  Failed to generate MYOB payload: {e}')
            
            else:
                print('❌ No order lines extracted')
                # Show first 300 chars of PDF content for debugging
                print(f'PDF content preview: {pdf_content[:300]}...')
        
        else:
            print('❌ No PDF content extracted')
    
    print(f'\n📊 Final Results:')
    print(f'Emails processed: {min(len(emails), 3)}')
    print(f'Orders with line items found: {orders_found}')
    print(f'MYOB payloads generated: {payloads_generated}')
    
    if payloads_generated > 0:
        print(f'\n🎉 SUCCESS: Generated {payloads_generated} real MYOB payloads from Brady orders!')
    else:
        print(f'\n⚠️  No complete orders found. Check PDF content extraction.')

if __name__ == "__main__":
    main()