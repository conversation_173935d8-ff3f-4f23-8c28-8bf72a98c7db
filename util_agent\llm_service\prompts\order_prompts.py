"""
Order processing specific prompts and templates.
"""

from typing import Dict, Any
from .base_prompts import BasePrompts, PromptComponent


class OrderPrompts(BasePrompts):
    """Order extraction and processing prompts."""
    
    # Order extraction system prompt
    ORDER_EXTRACTION_SYSTEM = PromptComponent(
        name="order_extraction_system",
        content="""You are a JSON extraction tool specialized in extracting sales order information from email and PDF content.""",
        category="order_system",
        priority=10
    )
    
    # Enhanced Customer/Debtor identification rules with all business rules
    DEBTOR_RULES = PromptComponent(
        name="debtor_rules",
        content="""CUSTOMER/DEBTOR IDENTIFICATION:
- Look for customer account numbers, debtor IDs, or customer codes in the document
- Search for patterns: "Account:", "Customer ID:", "Debtor:", "Account Number:", "Customer Code:"
- If no valid debtor_id found, use null (not 0)
- If customer name not found, use "UNKNOWN CUSTOMER"

CUSTOMER-SPECIFIC DISPATCH RULES:
- Gateway Packaging: dispatch method = "CUSTOMERS CARRIER"
- Sitecraft: dispatch method = "EMAIL WHEN READY"  
- RSEA: dispatch method = "DIRECT FREIGHT EXPRESS"
- Safety Xpress: dispatch method = "DELTA"
- Endeavour Group: dispatch method = "CAPITAL"

CUSTOMER-SPECIFIC ORDER PROCESSING:
- Brady accounts: 
  * Find our SKU next to "Your material number:"
  * For drop-ship orders: dispatch method = "BEST WAY"
  * Map "Ship to" to delivery_address and "PO Number" to order_number
- Woolworths: find order_number next to "PURCHASE ORDER"
- Brierley Industrial: find order_number next to "Original P.O #"

GENERAL DISPATCH RULES:
- If dispatch method is "pickup" or empty, default to "RING WHEN READY"
- If mentions "Account preferred carrier" or specific carrier account, use "CUSTOMERS CARRIER"
- If mentions "Freight" without SKU (and not pickup), set to "BEST WAY"
- Australian location defaults: Metro="DELTA", Regional/Small Metro="DIRECT FREIGHT EXPRESS"
- For drop-ship orders, account_name may differ from delivery_address recipient""",
        category="order_rules",
        priority=9
    )

    # Address parsing rules with character limit enforcement
    ADDRESS_RULES = PromptComponent(
        name="address_rules",
        content="""ADDRESS PARSING RULES:
- CRITICAL: Each `line` in the `delivery_address` object MUST be a maximum of 30 characters.
- Extract the full multi-line delivery address.
- Logically split the address across `line1`, `line2`, etc., to adhere to the 30-character limit per line.
- Do NOT truncate words. Split the address at logical breaks (commas, spaces) before the 30-character limit is reached.
- Populate the `line` fields sequentially.
- Common split patterns:
  - Line 1: Company Name or C/O Name (if present and under 30 chars)
  - Line 2: Street Address (e.g., '123 Industrial Avenue')
  - Line 3: Suburb, State, and Postcode (e.g., 'Dandenong VIC 3175')
- If the street address is too long, split it into two lines. Example: 'Unit 5, 123-145 Long Industrial Avenue' becomes `line2: "Unit 5, 123-145 Long"` and `line3: "Industrial Avenue"`.
- Do not include the recipient's name in the address lines.
- Leave unused lines as null.""",
        category="order_rules",
        priority=8.5  # Placed between debtor and shipping rules
    )
    
    # Shipping/dispatch rules
    SHIPPING_RULES = PromptComponent(
        name="shipping_rules",
        content="""SHIPPING/DISPATCH RULES:
- If dispatch method is "pickup" or empty, default to "RING WHEN READY"
- If mentions "Account preferred carrier" or specific carrier account, use "CUSTOMERS CARRIER"
- If mentions "Freight" without SKU (and not pickup), set to "BEST WAY"
- Australian location defaults: Metro = "DELTA", Regional/Small Metro = "DIRECT FREIGHT EXPRESS"
- For drop-ship orders, account_name may differ from delivery_address recipient""",
        category="order_rules",
        priority=8
    )
    
    # Order line processing
    ORDER_LINE_RULES = PromptComponent(
        name="order_line_rules",
        content="""ORDER LINE PROCESSING:
- Extract all line items with quantities and stock codes
- Remove all whitespace from stock codes
- Only include positive quantities
- If shipping method empty, use "BEST WAY"
- Validate each line has both stockcode and orderquantity""",
        category="order_rules",
        priority=7
    )
    
    # JSON structure template (Updated for structured address)
    ORDER_JSON_TEMPLATE = PromptComponent(
        name="order_json_template",
        content="""Required JSON format:
{
    "customer_details": {
        "debtor_id": null,
        "customer_order_number": "order_number_here",
        "customer_name": "customer_name_here", 
        "delivery_address": {
            "line1": "Company Name (max 30 chars)",
            "line2": "Street Address (max 30 chars)",
            "line3": "Suburb STATE Postcode (max 30)",
            "line4": null,
            "line5": null,
            "line6": null
        },
        "shipping_method": "BEST WAY"
    },
    "order_lines": [
        {
            "stockcode": "stock_code_here",
            "orderquantity": quantity_here
        }
    ],
    "order_status": 0
}""",
        category="template",
        priority=10
    )
    
    @classmethod
    def build_order_extraction_prompt(cls, content: str, context: str = "") -> str:
        """Build complete order extraction prompt."""
        
        # Build prompt sections
        system_section = cls.ORDER_EXTRACTION_SYSTEM.content
        json_enforcement = cls.JSON_ENFORCEMENT.content
        company_context = cls.COMPANY_CONTEXT.content
        debtor_rules = cls.DEBTOR_RULES.content
        address_rules = cls.ADDRESS_RULES.content  # Integrate the new address rules
        shipping_rules = cls.SHIPPING_RULES.content
        order_line_rules = cls.ORDER_LINE_RULES.content
        sku_rules = cls.SKU_RULES.content
        json_template = cls.ORDER_JSON_TEMPLATE.content
        
        # Add context if provided
        context_section = ""
        if context:
            context_section = f"\nRelevant previous examples:\n{context}\n"
        
        # Combine all sections
        prompt = f"""{system_section}

{json_enforcement}

{company_context}

{debtor_rules}

{address_rules}

{shipping_rules}

{order_line_rules}

{sku_rules}

{context_section}

Content to process:
{content}

{json_template}"""
        
        return prompt
    
    @classmethod
    def build_validation_prompt(cls, data: Dict[str, Any]) -> str:
        """Build prompt for order validation."""
        return f"""Validate this extracted order data against business rules:

{cls.DEBTOR_RULES.content}

{cls.ADDRESS_RULES.content}

{cls.SHIPPING_RULES.content}

{cls.ORDER_LINE_RULES.content}

Order data to validate:
{data}

Return validation result as JSON:
{{
    "is_valid": true/false,
    "errors": ["list of validation errors"],
    "warnings": ["list of warnings"],
    "suggestions": ["list of improvement suggestions"]
}}"""