"""Order processor for extracting order data with unstructured LLM analysis."""

import json
import logging
from datetime import datetime
from typing import Dict, Any, Optional, List

from ..services.mistral_service import MistralService
from ..services.memory_service import MemoryService
from ..prompts.prompt_manager import prompt_manager

# Import the fuzzy matching service
try:
    import sys
    import os
    # Add the root directory to the Python path to import debtor_lookup_service
    root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    if root_dir not in sys.path:
        sys.path.insert(0, root_dir)
    
    from debtor_lookup_service import DebtorLookupService
    FUZZY_MATCHING_AVAILABLE = True
    logging.info("Fuzzy matching service imported successfully")
except ImportError as e:
    FUZZY_MATCHING_AVAILABLE = False
    logging.warning(f"Fuzzy matching service not available: {e} - falling back to Supabase lookup only")

logger = logging.getLogger(__name__)


class OrderProcessor:
    """Processor for extracting order data from content."""
    
    def __init__(self, mistral_service: MistralService, memory_service: MemoryService, supabase_service=None):
        self.mistral_service = mistral_service
        self.memory_service = memory_service
        self.supabase_service = supabase_service
        
        # Initialize fuzzy matching service if available
        self.fuzzy_lookup = None
        if FUZZY_MATCHING_AVAILABLE:
            try:
                self.fuzzy_lookup = DebtorLookupService(csv_path="docs/active_debtors.csv", threshold=80)
                logger.info("Fuzzy matching service initialized successfully")
            except Exception as e:
                logger.warning(f"Failed to initialize fuzzy matching service: {e}")
                self.fuzzy_lookup = None
    
    async def analyze_email_content(self, content: str, sender_email: str = None) -> Dict[str, Any]:
        """Analyze email content with unstructured LLM reasoning - no validation."""
        
        # Get relevant context from memory database
        memory_contexts = self.memory_service.get_relevant_context(content[:500], n_results=3)
        context_text = ""
        if memory_contexts:
            context_text = "\nRelevant previous examples:\n" + "\n---\n".join(memory_contexts)
        
        # Create an open-ended analysis prompt
        analysis_prompt = self._build_unstructured_analysis_prompt(content, sender_email, context_text)
        
        try:
            # Use flexible content generation (no JSON enforcement)
            response = await self.mistral_service.generate_content_flexible(analysis_prompt)
            
            if response:
                logger.info("Successfully generated email analysis")
                
                # Return the raw analysis with metadata
                return {
                    "analysis_text": response,
                    "sender_email": sender_email,
                    "content_length": len(content),
                    "analysis_timestamp": datetime.now().isoformat(),
                    "has_context": bool(memory_contexts),
                    "context_count": len(memory_contexts) if memory_contexts else 0
                }
            else:
                logger.warning("LLM returned empty analysis")
                return {
                    "analysis_text": "No analysis generated",
                    "sender_email": sender_email,
                    "error": "Empty LLM response"
                }
            
        except Exception as e:
            logger.error(f"Error analyzing email content: {str(e)}")
            return {
                "analysis_text": f"Analysis failed: {str(e)}",
                "sender_email": sender_email,
                "error": str(e)
            }
    
    def _build_unstructured_analysis_prompt(self, content: str, sender_email: str = None, context: str = "") -> str:
        """Build an open-ended analysis prompt for unstructured LLM reasoning."""
        
        sender_context = ""
        if sender_email:
            if "@teamsystems.net.au" in sender_email.lower():
                sender_context = f"""
SENDER CONTEXT:
- Email from: {sender_email}
- This is a Team Systems employee forwarding or discussing customer communications
- Look for the actual customer information within the email content
"""
            else:
                sender_context = f"""
SENDER CONTEXT:
- Email from: {sender_email}
- This appears to be from an external party (potentially a customer)
"""
        
        context_section = ""
        if context:
            context_section = f"\nRELEVANT CONTEXT FROM PREVIOUS EMAILS:\n{context}\n"
        
        return f"""You are an expert email analyst for Team Systems, an Australian industrial equipment supplier. 

TASK: Analyze the email content below and provide comprehensive insights about its business relevance, potential actions needed, and any order-related information.

{sender_context}

{context_section}

EMAIL CONTENT TO ANALYZE:
{content}

ANALYSIS INSTRUCTIONS:
Please provide a thorough analysis covering:

1. EMAIL CLASSIFICATION:
   - What type of email is this? (order, inquiry, complaint, information, etc.)
   - Business priority level (urgent, high, medium, low)
   - Confidence in your classification

2. CUSTOMER IDENTIFICATION:
   - Who is the actual customer? (not Team Systems staff)
   - Any customer account numbers or references mentioned
   - Customer contact information if available

3. ORDER ANALYSIS (if applicable):
   - Is this a purchase order or order-related communication?
   - What products/services are being requested?
   - Quantities, specifications, delivery requirements
   - Purchase order numbers or references
   - Delivery addresses and special instructions

4. BUSINESS CONTEXT:
   - Relationship to Team Systems business operations
   - Any urgency indicators or deadlines
   - Special requirements or considerations
   - Follow-up actions needed

5. KEY INFORMATION EXTRACTION:
   - Important dates, numbers, references
   - Contact details and communication preferences
   - Any technical specifications or requirements

6. REASONING AND CONFIDENCE:
   - Explain your reasoning for key conclusions
   - Indicate confidence levels for uncertain information
   - Note any ambiguities or missing information

Please provide your analysis in clear, structured text. Focus on business relevance and actionable insights rather than rigid data formats."""
    
    async def extract_order_data_for_myob(self, analysis_result: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Extract structured order data from analysis result for MYOB processing."""
        
        if not analysis_result or "analysis_text" not in analysis_result:
            logger.warning("No analysis result provided for MYOB extraction")
            return None
        
        analysis_text = analysis_result["analysis_text"]
        sender_email = analysis_result.get("sender_email")
        
        # Create a structured extraction prompt based on the analysis
        extraction_prompt = f"""Based on the following email analysis, extract specific order data for MYOB system integration.

EMAIL ANALYSIS:
{analysis_text}

SENDER EMAIL: {sender_email or 'Unknown'}

TASK: Extract structured order information ONLY if this is clearly a purchase order. If not a purchase order, return null.

If this IS a purchase order, provide the following structure:
{{
  "is_order": true,
  "customer_details": {{
    "customer_name": "CUSTOMER NAME",
    "debtor_id": null,
    "customer_order_number": "PO NUMBER",
    "delivery_address": {{
      "line1": "ADDRESS LINE 1",
      "line2": "ADDRESS LINE 2", 
      "line3": "CITY STATE POSTCODE"
    }}
  }},
  "order_lines": [
    {{
      "stockcode": "PRODUCT CODE",
      "orderquantity": QUANTITY_NUMBER
    }}
  ],
  "order_status": 0,
  "confidence": "high/medium/low",
  "extraction_notes": "Any important notes about the extraction"
}}

If this is NOT a purchase order, return:
{{
  "is_order": false,
  "reason": "Explanation of why this is not an order",
  "email_type": "inquiry/complaint/information/etc"
}}

Return valid JSON only."""
        
        try:
            response = await self.mistral_service.generate_content(extraction_prompt)
            
            if response.text:
                try:
                    data = json.loads(response.text)
                    
                    if data.get("is_order"):
                        logger.info("Successfully extracted order data for MYOB")
                        return data
                    else:
                        logger.info(f"Email is not an order: {data.get('reason', 'Unknown reason')}")
                        return None
                        
                except json.JSONDecodeError as e:
                    logger.error(f"Failed to parse MYOB extraction JSON: {e}")
                    return None
            
            return None
            
        except Exception as e:
            logger.error(f"Error extracting order data for MYOB: {str(e)}")
            return None
            
    async def _try_fallback_extraction(self, content: str, sender_email: str = None) -> Optional[Dict[str, Any]]:
        """Fallback method for extraction with a more explicit structure prompt."""
        fallback_prompt = f"""
You are an order extraction specialist for Team Systems, an Australian industrial equipment supplier.

TASK: Extract order information from the content below into a SPECIFIC JSON FORMAT.

CONTENT:
{content}

OUTPUT FORMAT - YOU MUST USE EXACTLY THIS STRUCTURE:
```json
{{
  "customer_details": {{
    "debtor_id": 5760,  # Use 5760 for Woolworths, 5761 for Brady, or null if unknown
    "customer_name": "CUSTOMER NAME HERE",
    "customer_order_number": "PO NUMBER HERE",
    "delivery_address": {{
      "line1": "ADDRESS LINE 1",
      "line2": "ADDRESS LINE 2",
      "line3": "CITY STATE POSTCODE"
    }}
  }},
  "order_lines": [
    {{
      "stockcode": "PRODUCT CODE",
      "orderquantity": QUANTITY_AS_NUMBER
    }}
  ],
  "order_status": 0
}}
```

IMPORTANT RULES:
1. Use EXACTLY the field names shown above
2. "customer_details" must be an object with the fields shown
3. "order_lines" must be an array of objects with "stockcode" and "orderquantity"
4. Return valid JSON only, no explanations or other text
"""

        try:
            # Use the enhanced Mistral service with JSON mode
            response = await self.mistral_service.generate_content(fallback_prompt)
            
            if response.text:
                try:
                    # Extract JSON from the response
                    json_text = response.text
                    
                    # If the response contains markdown code blocks, extract the JSON
                    if "```json" in json_text:
                        json_text = json_text.split("```json")[1].split("```")[0].strip()
                    elif "```" in json_text:
                        json_text = json_text.split("```")[1].split("```")[0].strip()
                    
                    # Parse the JSON
                    data = json.loads(json_text)
                    
                    # Validate the structure
                    if self._validate_extracted_structure(data):
                        logger.info("Successfully extracted order data with fallback method")
                        return data
                    else:
                        logger.warning("Fallback extraction failed - data still doesn't match expected structure")
                        return None
                        
                except json.JSONDecodeError as e:
                    logger.error(f"Failed to parse JSON from fallback response: {e}")
                    return None
            
            logger.warning("Fallback LLM did not return expected JSON response")
            return None
            
        except Exception as e:
            logger.error(f"Error in fallback extraction: {str(e)}")
            return None
            
        except Exception as e:
            logger.error(f"Error extracting order data with LLM: {str(e)}")
            return None
    
    def _validate_extracted_structure(self, data: Dict[str, Any]) -> bool:
        """Validate that extracted data has the expected structure."""
        try:
            # Check required top-level keys
            if not isinstance(data, dict):
                return False
            
            required_keys = ['customer_details', 'order_lines', 'order_status']
            
            # ENHANCEMENT: Transform data format if it doesn't match expected structure
            if not all(key in data for key in required_keys):
                logger.warning(f"Missing required keys. Expected: {required_keys}, Got: {list(data.keys())}")
                
                # Check if we have an alternative structure we can transform
                if 'customer' in data and 'line_items' in data:
                    logger.info("Found alternative structure with 'customer' and 'line_items'. Transforming to expected format.")
                    
                    # Transform the data structure
                    transformed_data = {
                        'customer_details': {
                            'customer_name': data.get('customer', ''),
                            'customer_order_number': data.get('order_number', ''),
                            'debtor_id': None,  # Initialize as None, will be looked up later
                            'delivery_address': {
                                'line1': data.get('ship_to', {}).get('line1', '') if isinstance(data.get('ship_to'), dict) else data.get('ship_to', ''),
                                'line2': data.get('ship_to', {}).get('line2', '') if isinstance(data.get('ship_to'), dict) else '',
                                'line3': data.get('ship_to', {}).get('line3', '') if isinstance(data.get('ship_to'), dict) else '',
                            }
                        },
                        'order_lines': [],
                        'order_status': 0
                    }
                    
                    # Transform line items
                    line_items = data.get('line_items', [])
                    if isinstance(line_items, list):
                        for item in line_items:
                            if isinstance(item, dict):
                                # Extract stockcode and quantity from various possible formats
                                stockcode = item.get('stockcode', item.get('item_code', item.get('part_number', item.get('sku', ''))))
                                quantity = item.get('orderquantity', item.get('quantity', item.get('qty', 0)))
                                
                                # Only add if we have both stockcode and quantity
                                if stockcode and quantity:
                                    try:
                                        # Convert quantity to float
                                        qty = float(quantity)
                                        transformed_data['order_lines'].append({
                                            'stockcode': str(stockcode).strip(),
                                            'orderquantity': qty
                                        })
                                    except (ValueError, TypeError):
                                        logger.warning(f"Invalid quantity format: {quantity}")
                    
                    # Update the original data with transformed structure
                    data.update(transformed_data)
                    logger.info(f"Transformed data structure. Now has {len(data['order_lines'])} order lines.")
                else:
                    logger.warning("Cannot transform data - missing required alternative structure")
                    return False
            
            # Re-check required keys after potential transformation
            if not all(key in data for key in required_keys):
                logger.warning("Data structure still missing required keys after transformation attempt")
                return False
            
            # Check customer_details structure
            customer_details = data.get('customer_details', {})
            if not isinstance(customer_details, dict):
                return False
            
            # Check order_lines structure
            order_lines = data.get('order_lines', [])
            if not isinstance(order_lines, list):
                return False
            
            # Validate each order line
            for line in order_lines:
                if not isinstance(line, dict):
                    return False
                if 'stockcode' not in line or 'orderquantity' not in line:
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating extracted structure: {e}")
            return False
    
    def _lookup_customer_by_email(self, sender_email: str, sender_name: str = None) -> Optional[Dict[str, Any]]:
        """Lookup customer using fuzzy matching on email address."""
        if not sender_email or not self.fuzzy_lookup:
            return None
        
        try:
            match = self.fuzzy_lookup.find_debtor_by_email(sender_email, sender_name)
            if match:
                logger.info(f"Fuzzy match found: {match['customer_name']} (ID: {match['debtor_id']}, Confidence: {match['confidence_score']}%)")
                return match
            return None
        except Exception as e:
            logger.error(f"Error in fuzzy email lookup for '{sender_email}': {e}")
            return None
    
    def _lookup_customer_by_name(self, customer_name: str) -> Optional[int]:
        """Lookup customer debtor_id by name using enhanced fuzzy matching first, then Supabase fallback."""
        if not customer_name:
            return None
        
        # Try enhanced fuzzy matching first if available
        if self.fuzzy_lookup:
            try:
                # Use the new direct customer name matching method
                match = self.fuzzy_lookup.find_debtor_by_customer_name(customer_name)
                if match:
                    logger.info(f"Direct customer name match: {match['customer_name']} (ID: {match['debtor_id']}, Score: {match['confidence_score']}%)")
                    return match['debtor_id']
                
                # Fallback to search method for broader matching
                results = self.fuzzy_lookup.search_customers(customer_name, limit=1)
                if results and results[0]['confidence_score'] >= 70:  # Lower threshold for search
                    match = results[0]
                    logger.info(f"Search-based name match: {match['customer_name']} (ID: {match['debtor_id']}, Score: {match['confidence_score']}%)")
                    return match['debtor_id']
            except Exception as e:
                logger.warning(f"Fuzzy name lookup failed for '{customer_name}': {e}")
        
        # Fallback to Supabase lookup
        if self.supabase_service:
            try:
                customer = self.supabase_service.find_customer_by_name(customer_name)
                if customer:
                    logger.info(f"Supabase lookup found: {customer_name} -> {customer.get('debtor_id')}")
                    return customer.get('debtor_id')
            except Exception as e:
                logger.error(f"Error in Supabase lookup for '{customer_name}': {e}")
        
        return None
    
    async def validate_extracted_order(self, data: Dict[str, Any], sender_email: str = None, sender_name: str = None) -> bool:
        """Validate extracted order data with improved debtor_id handling and fuzzy matching."""
        try:
            # Ensure customer_details is a dict
            if 'customer_details' not in data or not isinstance(data['customer_details'], dict):
                data['customer_details'] = {
                    'debtor_id': None,  # Use None instead of 0 for unknown customers
                    'customer_order_number': '',
                    'customer_name': 'UNKNOWN CUSTOMER',
                    'delivery_address': '',
                    'shipping_method': 'BEST WAY'
                }
            
            # Handle debtor_id properly - convert to int or set to None
            debtor_id = data['customer_details'].get('debtor_id')
            if debtor_id is not None:
                if isinstance(debtor_id, str):
                    try:
                        # Try to convert string to int
                        parsed_id = int(debtor_id.strip())
                        # If it's 0, treat as unknown customer (set to None)
                        data['customer_details']['debtor_id'] = parsed_id if parsed_id > 0 else None
                    except ValueError:
                        logger.warning(f"Invalid debtor_id format: {debtor_id}, setting to None")
                        data['customer_details']['debtor_id'] = None
                elif isinstance(debtor_id, (int, float)):
                    # If it's 0 or negative, treat as unknown customer
                    data['customer_details']['debtor_id'] = int(debtor_id) if debtor_id > 0 else None
                else:
                    data['customer_details']['debtor_id'] = None
            
            # If no debtor_id found, try fuzzy matching by customer name first, then by email
            final_debtor_id = data['customer_details']['debtor_id']
            if final_debtor_id is None:
                # Priority 1: Try fuzzy matching by customer name (most accurate for direct matches)
                customer_name = data['customer_details'].get('customer_name')
                if customer_name and customer_name.strip().upper() not in ['UNKNOWN CUSTOMER', 'UNKNOWN', '']:
                    logger.info(f"Attempting customer name lookup for: '{customer_name}'")
                    looked_up_debtor_id = self._lookup_customer_by_name(customer_name)
                    if looked_up_debtor_id:
                        data['customer_details']['debtor_id'] = looked_up_debtor_id
                        final_debtor_id = looked_up_debtor_id
                        logger.info(f"✅ Customer name match: '{customer_name}' -> debtor_id: {looked_up_debtor_id}")
                        
                        # Store fuzzy match metadata for reference
                        data['customer_details']['_fuzzy_match'] = {
                            'method': 'customer_name_direct',
                            'confidence': 95,  # High confidence for direct name matches
                            'matched_text': f"Direct name match: {customer_name}"
                        }
                    else:
                        logger.warning(f"❌ Customer name lookup failed for: '{customer_name}'")
                
                # Priority 2: If customer name lookup failed, try by email address
                if final_debtor_id is None and sender_email:
                    logger.info(f"Attempting fuzzy email lookup for: '{sender_email}'")
                    email_match = self._lookup_customer_by_email(sender_email, sender_name)
                    if email_match:
                        data['customer_details']['debtor_id'] = email_match['debtor_id']
                        data['customer_details']['customer_name'] = email_match['customer_name']
                        final_debtor_id = email_match['debtor_id']
                        logger.info(f"✅ Fuzzy email match: {email_match['customer_name']} (ID: {final_debtor_id}, Confidence: {email_match['confidence_score']}%)")
                        
                        # Store fuzzy match metadata for reference
                        data['customer_details']['_fuzzy_match'] = {
                            'method': email_match['match_method'],
                            'confidence': email_match['confidence_score'],
                            'matched_text': email_match['matched_text']
                        }
                
                # Final check
                if final_debtor_id is None:
                    logger.warning("❌ No valid debtor_id found - order will require manual review")
                    logger.info(f"📧 Sender email: {sender_email or 'Not provided'}")
                    logger.info(f"👤 Sender name: {sender_name or 'Not provided'}")
                    logger.info(f"🏢 Extracted customer name: {data['customer_details'].get('customer_name', 'Not extracted')}")
            else:
                logger.info(f"Extracted debtor_id: {final_debtor_id}")
            
            # Ensure order_lines is a list
            if 'order_lines' not in data or not isinstance(data['order_lines'], list):
                data['order_lines'] = []
            
            # Ensure all order lines have required fields
            validated_lines = []
            for line in data['order_lines']:
                if isinstance(line, dict) and 'stockcode' in line and 'orderquantity' in line:
                    try:
                        validated_line = {
                            'stockcode': str(line['stockcode']).strip().replace(' ', ''),  # Remove all whitespace
                            'orderquantity': float(line['orderquantity'])
                        }
                        if validated_line['orderquantity'] > 0:  # Only include positive quantities
                            validated_lines.append(validated_line)
                    except (ValueError, TypeError) as e:
                        logger.warning(f"Skipping invalid order line: {line}, error: {e}")
                        continue
            
            data['order_lines'] = validated_lines
            
            # Validate with Pydantic - but handle debtor_id as Optional[int]
            # We need to update the model to handle None values properly
            if data['customer_details']['debtor_id'] is None:
                # For Pydantic validation, we'll use 0 but flag it for review
                data['customer_details']['debtor_id'] = 0
                logger.info("Setting debtor_id to 0 for Pydantic validation - order flagged for review")
            
            order = ExtractedOrder(**data)
            return True
            
        except Exception as e:
            logger.error(f"Order validation failed: {str(e)}")
            raise ValidationException(f"Invalid order data: {str(e)}")