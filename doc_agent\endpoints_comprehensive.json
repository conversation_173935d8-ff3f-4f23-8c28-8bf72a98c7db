{"metadata": {"scraped_date": "2025-07-14T16:43:20.849989", "source": "https://developer.myob.com/api/exo/endpoints/", "total_endpoints": 68, "categories": ["activities", "contacts", "creditors", "debtors", "discovery", "job_management", "prospects", "purchase_orders", "reference_data", "reports", "sales_orders", "schemas", "search_templates", "stock_management", "stock_operations", "system"], "base_uri_pattern": "{URI}", "notes": ["Replace {URI} with your actual MYOB EXO API server address", "e.g., http://***********:8888 for on-premise or exo.api.myob.com for cloud", "All endpoints support GET by default, additional methods listed where applicable", "Search endpoints require query parameter: ?q={search_term}", "Report endpoints require ID parameter in the URL path"]}, "endpoints": [{"rel": "collection/discovery", "title": "Endpoints", "href": "{URI}/discovery", "methods": ["GET"], "category": "discovery", "description": "Endpoints management and operations", "documentation_url": "https://developer.myob.com/api/exo/endpoints/"}, {"rel": "collection/debtor", "title": "Debtor", "href": "{URI}/debtor", "methods": ["GET"], "category": "debtors", "description": "Debtor management and operations", "documentation_url": "https://developer.myob.com/api/exo/endpoints/debtor/", "search_supported": true, "search_href": "{URI}/debtor/search?q={query}", "report_href": "{URI}/debtor/{id}/report"}, {"rel": "collection/debtordebtorhistorynote", "title": "Debtor History Note", "href": "{URI}/debtordebtorhistorynote", "methods": ["GET"], "category": "debtors", "description": "Debtor History Note management and operations", "documentation_url": "https://developer.myob.com/api/exo/endpoints/debtor/debtorhistorynote/"}, {"rel": "collection/debtordebtortrans", "title": "Debtor Transaction", "href": "{URI}/debtordebtortrans", "methods": ["GET", "POST"], "category": "debtors", "description": "Debtor Transaction management and operations", "documentation_url": "https://developer.myob.com/api/exo/endpoints/debtor/debtortrans/"}, {"rel": "collection/debtordebtorreport", "title": "Debtor Transaction Report", "href": "{URI}/debtordebtorreport", "methods": ["GET", "POST"], "category": "debtors", "description": "Debtor Transaction Report management and operations", "documentation_url": "https://developer.myob.com/api/exo/endpoints/debtor/debtorreport/"}, {"rel": "collection/debtordebtorstatement", "title": "Debtor Statement", "href": "{URI}/debtordebtorstatement", "methods": ["GET"], "category": "debtors", "description": "Debtor Statement management and operations", "documentation_url": "https://developer.myob.com/api/exo/endpoints/debtor/debtorstatement/"}, {"rel": "collection/debtordebtoractivity", "title": "Debtor Activities", "href": "{URI}/debtordebtoractivity", "methods": ["GET"], "category": "debtors", "description": "Debtor Activities management and operations", "documentation_url": "https://developer.myob.com/api/exo/endpoints/debtor/debtoractivity/"}, {"rel": "collection/prospect", "title": "Prospect", "href": "{URI}/prospect", "methods": ["GET"], "category": "prospects", "description": "Prospect management and operations", "documentation_url": "https://developer.myob.com/api/exo/endpoints/prospect/"}, {"rel": "collection/prospectprospecthistorynote", "title": "Prospect History Note", "href": "{URI}/prospectprospecthistorynote", "methods": ["GET"], "category": "prospects", "description": "Prospect History Note management and operations", "documentation_url": "https://developer.myob.com/api/exo/endpoints/prospect/prospecthistorynote/"}, {"rel": "collection/prospectprospectactivity", "title": "Prospect Activities", "href": "{URI}/prospectprospectactivity", "methods": ["GET"], "category": "prospects", "description": "Prospect Activities management and operations", "documentation_url": "https://developer.myob.com/api/exo/endpoints/prospect/prospectactivity/"}, {"rel": "collection/stock", "title": "Stock", "href": "{URI}/stock", "methods": ["GET"], "category": "stock_management", "description": "Stock management and operations", "documentation_url": "https://developer.myob.com/api/exo/endpoints/stock/", "search_supported": true, "search_href": "{URI}/stock/search?q={query}"}, {"rel": "collection/stockstockpricegroup", "title": "Stock Price Group", "href": "{URI}/stockstockpricegroup", "methods": ["GET"], "category": "stock_management", "description": "Stock Price Group management and operations", "documentation_url": "https://developer.myob.com/api/exo/endpoints/stock/stockpricegroup/"}, {"rel": "collection/stockstockunitofmeasure", "title": "Stock Unit of Measure", "href": "{URI}/stockstockunitofmeasure", "methods": ["GET"], "category": "stock_management", "description": "Stock Unit of Measure management and operations", "documentation_url": "https://developer.myob.com/api/exo/endpoints/stock/stockunitofmeasure/"}, {"rel": "collection/stockstocklocation", "title": "Stock Location", "href": "{URI}/stockstocklocation", "methods": ["GET"], "category": "stock_management", "description": "Stock Location management and operations", "documentation_url": "https://developer.myob.com/api/exo/endpoints/stock/stocklocation/"}, {"rel": "collection/stockstockclassification", "title": "Stock Classification", "href": "{URI}/stockstockclassification", "methods": ["GET"], "category": "stock_management", "description": "Stock Classification management and operations", "documentation_url": "https://developer.myob.com/api/exo/endpoints/stock/stockclassification/"}, {"rel": "collection/stockstocksecondarygroup", "title": "Secondary Stock Group", "href": "{URI}/stockstocksecondarygroup", "methods": ["GET"], "category": "stock_management", "description": "Secondary Stock Group management and operations", "documentation_url": "https://developer.myob.com/api/exo/endpoints/stock/stocksecondarygroup/"}, {"rel": "collection/stockstockprimarygroup", "title": "Primary Stock Group", "href": "{URI}/stockstockprimarygroup", "methods": ["GET"], "category": "stock_management", "description": "Primary Stock Group management and operations", "documentation_url": "https://developer.myob.com/api/exo/endpoints/stock/stockprimarygroup/"}, {"rel": "collection/stockstockitemreport", "title": "Stock Item Report", "href": "{URI}/stockstockitemreport", "methods": ["GET"], "category": "stock_management", "description": "Stock Item Report management and operations", "documentation_url": "https://developer.myob.com/api/exo/endpoints/stock/stockitemreport/"}, {"rel": "collection/stock_item", "title": "Stock Item", "href": "{URI}/stock_item", "methods": ["GET"], "category": "stock_management", "description": "Stock Item management and operations", "documentation_url": "https://developer.myob.com/api/exo/endpoints/stock_item/", "report_href": "{URI}/stock_item/{id}/report"}, {"rel": "collection/stock_itemstock_bestprice", "title": "Stock Item Price", "href": "{URI}/stock_itemstock_bestprice", "methods": ["GET"], "category": "stock_management", "description": "Stock Item Price management and operations", "documentation_url": "https://developer.myob.com/api/exo/endpoints/stock_item/stock_bestprice/"}, {"rel": "collection/salesorder", "title": "Sales Order", "href": "{URI}/salesorder", "methods": ["GET", "POST"], "category": "sales_orders", "description": "Sales Order management and operations", "documentation_url": "https://developer.myob.com/api/exo/endpoints/salesorder/", "search_supported": true, "search_href": "{URI}/salesorder/search?q={query}", "report_href": "{URI}/salesorder/{id}/report"}, {"rel": "collection/salesordersalesorderreport", "title": "Sales Order Report", "href": "{URI}/salesordersalesorderreport", "methods": ["GET", "POST"], "category": "sales_orders", "description": "Sales Order Report management and operations", "documentation_url": "https://developer.myob.com/api/exo/endpoints/salesorder/salesorderreport/"}, {"rel": "collection/salesorderbom", "title": "Bill Of Materials", "href": "{URI}/salesorderbom", "methods": ["GET"], "category": "sales_orders", "description": "Bill Of Materials management and operations", "documentation_url": "https://developer.myob.com/api/exo/endpoints/salesorder/bom/"}, {"rel": "collection/salesorderbombom_bestprice", "title": "Bill of Materials Price", "href": "{URI}/salesorderbombom_bestprice", "methods": ["GET"], "category": "sales_orders", "description": "Bill of Materials Price management and operations", "documentation_url": "https://developer.myob.com/api/exo/endpoints/salesorder/bom/bom_bestprice/"}, {"rel": "collection/report", "title": "Report", "href": "{URI}/report", "methods": ["GET"], "category": "reports", "description": "Report management and operations", "documentation_url": "https://developer.myob.com/api/exo/endpoints/report/"}, {"rel": "collection/reportreportparameters", "title": "Report Parameters", "href": "{URI}/reportreportparameters", "methods": ["GET"], "category": "reports", "description": "Report Parameters management and operations", "documentation_url": "https://developer.myob.com/api/exo/endpoints/report/reportparameters/"}, {"rel": "collection/reportrunreport", "title": "Run Report", "href": "{URI}/reportrunreport", "methods": ["GET"], "category": "reports", "description": "Run Report management and operations", "documentation_url": "https://developer.myob.com/api/exo/endpoints/report/runreport/"}, {"rel": "collection/reportfetchreport", "title": "Fetch Report", "href": "{URI}/reportfetchreport", "methods": ["GET"], "category": "reports", "description": "Fetch Report management and operations", "documentation_url": "https://developer.myob.com/api/exo/endpoints/report/fetchreport/"}, {"rel": "collection/jobproject", "title": "Job Project", "href": "{URI}/jobproject", "methods": ["GET"], "category": "job_management", "description": "Job Project management and operations", "documentation_url": "https://developer.myob.com/api/exo/endpoints/jobproject/"}, {"rel": "collection/jobtype", "title": "Job Type", "href": "{URI}/jobtype", "methods": ["GET"], "category": "job_management", "description": "Job Type management and operations", "documentation_url": "https://developer.myob.com/api/exo/endpoints/jobtype/"}, {"rel": "collection/jobstatus", "title": "Job Status", "href": "{URI}/jobstatus", "methods": ["GET"], "category": "job_management", "description": "Job Status management and operations", "documentation_url": "https://developer.myob.com/api/exo/endpoints/jobstatus/"}, {"rel": "collection/jobflagdescription", "title": "Job Flag", "href": "{URI}/jobflagdescription", "methods": ["GET"], "category": "job_management", "description": "Job Flag management and operations", "documentation_url": "https://developer.myob.com/api/exo/endpoints/jobflagdescription/"}, {"rel": "collection/jobcategory", "title": "Job Category", "href": "{URI}/jobcategory", "methods": ["GET"], "category": "job_management", "description": "Job Category management and operations", "documentation_url": "https://developer.myob.com/api/exo/endpoints/jobcategory/"}, {"rel": "collection/contact", "title": "Contact", "href": "{URI}/contact", "methods": ["GET"], "category": "contacts", "description": "Contact management and operations", "documentation_url": "https://developer.myob.com/api/exo/endpoints/contact/"}, {"rel": "collection/searchtemplate", "title": "Search Template", "href": "{URI}/searchtemplate", "methods": ["GET"], "category": "search_templates", "description": "Search Template management and operations", "documentation_url": "https://developer.myob.com/api/exo/endpoints/searchtemplate/", "search_supported": true, "search_href": "{URI}/searchtemplate/search?q={query}"}, {"rel": "collection/stocksearchtemplate", "title": "Stock Search Template", "href": "{URI}/stocksearchtemplate", "methods": ["GET"], "category": "search_templates", "description": "Stock Search Template management and operations", "documentation_url": "https://developer.myob.com/api/exo/endpoints/stocksearchtemplate/", "search_supported": true, "search_href": "{URI}/stocksearchtemplate/search?q={query}"}, {"rel": "collection/companysearchtemplate", "title": "Company Search Template", "href": "{URI}/companysearchtemplate", "methods": ["GET"], "category": "search_templates", "description": "Company Search Template management and operations", "documentation_url": "https://developer.myob.com/api/exo/endpoints/companysearchtemplate/", "search_supported": true, "search_href": "{URI}/companysearchtemplate/search?q={query}"}, {"rel": "collection/geosearchtemplate", "title": "Geolocation Search Template", "href": "{URI}/geosearchtemplate", "methods": ["GET"], "category": "search_templates", "description": "Geolocation Search Template management and operations", "documentation_url": "https://developer.myob.com/api/exo/endpoints/geosearchtemplate/", "search_supported": true, "search_href": "{URI}/geosearchtemplate/search?q={query}"}, {"rel": "collection/token", "title": "Token", "href": "{URI}/token", "methods": ["GET"], "category": "system", "description": "Token management and operations", "documentation_url": "https://developer.myob.com/api/exo/endpoints/token/"}, {"rel": "collection/companydatafileinfo", "title": "Company Data File Info", "href": "{URI}/companydatafileinfo", "methods": ["GET"], "category": "system", "description": "Company Data File Info management and operations", "documentation_url": "https://developer.myob.com/api/exo/endpoints/companydatafileinfo/"}, {"rel": "collection/activity", "title": "Activity", "href": "{URI}/activity", "methods": ["GET", "POST"], "category": "activities", "description": "Activity management and operations", "documentation_url": "https://developer.myob.com/api/exo/endpoints/activity/"}, {"rel": "collection/activitytype", "title": "Activity Type", "href": "{URI}/activitytype", "methods": ["GET", "POST"], "category": "activities", "description": "Activity Type management and operations", "documentation_url": "https://developer.myob.com/api/exo/endpoints/activitytype/"}, {"rel": "collection/activitystatus", "title": "Activity Status", "href": "{URI}/activitystatus", "methods": ["GET", "POST"], "category": "activities", "description": "Activity Status management and operations", "documentation_url": "https://developer.myob.com/api/exo/endpoints/activitystatus/"}, {"rel": "collection/custom-table", "title": "Custom Table", "href": "{URI}/custom-table", "methods": ["GET"], "category": "system", "description": "Custom Table management and operations", "documentation_url": "https://developer.myob.com/api/exo/endpoints/custom-table/"}, {"rel": "collection/purchaseorder", "title": "Purchase Order", "href": "{URI}/purchaseorder", "methods": ["GET", "POST"], "category": "purchase_orders", "description": "Purchase Order management and operations", "documentation_url": "https://developer.myob.com/api/exo/endpoints/purchaseorder/", "search_supported": true, "search_href": "{URI}/purchaseorder/search?q={query}", "report_href": "{URI}/purchaseorder/{id}/report"}, {"rel": "collection/list-endpoints", "title": "List Endpoints", "href": "{URI}/list-endpoints", "methods": ["GET"], "category": "reference_data", "description": "List Endpoints management and operations", "documentation_url": "https://developer.myob.com/api/exo/endpoints/list-endpoints/"}, {"rel": "collection/list-endpointstaxrate", "title": "Tax Rate", "href": "{URI}/list-endpointstaxrate", "methods": ["GET"], "category": "reference_data", "description": "Tax Rate management and operations", "documentation_url": "https://developer.myob.com/api/exo/endpoints/list-endpoints/taxrate/"}, {"rel": "collection/list-endpointsstaff", "title": "Staff", "href": "{URI}/list-endpointsstaff", "methods": ["GET"], "category": "reference_data", "description": "Staff management and operations", "documentation_url": "https://developer.myob.com/api/exo/endpoints/list-endpoints/staff/"}, {"rel": "collection/list-endpointspaymenttype", "title": "Payment Type", "href": "{URI}/list-endpointspaymenttype", "methods": ["GET"], "category": "reference_data", "description": "Payment Type management and operations", "documentation_url": "https://developer.myob.com/api/exo/endpoints/list-endpoints/paymenttype/"}, {"rel": "collection/list-endpointscurrency", "title": "<PERSON><PERSON><PERSON><PERSON>", "href": "{URI}/list-endpointscurrency", "methods": ["GET"], "category": "reference_data", "description": "Currency management and operations", "documentation_url": "https://developer.myob.com/api/exo/endpoints/list-endpoints/currency/"}, {"rel": "collection/list-endpointscreditterm", "title": "Credit Term", "href": "{URI}/list-endpointscreditterm", "methods": ["GET"], "category": "reference_data", "description": "Credit Term management and operations", "documentation_url": "https://developer.myob.com/api/exo/endpoints/list-endpoints/creditterm/"}, {"rel": "collection/list-endpointscompany", "title": "Company", "href": "{URI}/list-endpointscompany", "methods": ["GET"], "category": "reference_data", "description": "Company management and operations", "documentation_url": "https://developer.myob.com/api/exo/endpoints/list-endpoints/company/"}, {"rel": "collection/list-endpointscommunicationprocess", "title": "Communication Process", "href": "{URI}/list-endpointscommunicationprocess", "methods": ["GET"], "category": "reference_data", "description": "Communication Process management and operations", "documentation_url": "https://developer.myob.com/api/exo/endpoints/list-endpoints/communicationprocess/"}, {"rel": "collection/list-endpointscommonphrases", "title": "Common Phrases", "href": "{URI}/list-endpointscommonphrases", "methods": ["GET"], "category": "reference_data", "description": "Common Phrases management and operations", "documentation_url": "https://developer.myob.com/api/exo/endpoints/list-endpoints/commonphrases/"}, {"rel": "collection/list-endpointsbranch", "title": "Branch", "href": "{URI}/list-endpointsbranch", "methods": ["GET"], "category": "reference_data", "description": "Branch management and operations", "documentation_url": "https://developer.myob.com/api/exo/endpoints/list-endpoints/branch/"}, {"rel": "collection/list-endpointsbaseprice", "title": "Base Price", "href": "{URI}/list-endpointsbaseprice", "methods": ["GET"], "category": "reference_data", "description": "Base Price management and operations", "documentation_url": "https://developer.myob.com/api/exo/endpoints/list-endpoints/baseprice/"}, {"rel": "collection/list-endpointsadverttype", "title": "Advert Type", "href": "{URI}/list-endpointsadverttype", "methods": ["GET"], "category": "reference_data", "description": "Advert Type management and operations", "documentation_url": "https://developer.myob.com/api/exo/endpoints/list-endpoints/adverttype/"}, {"rel": "collection/list-endpointsaccountgroup", "title": "Account Group", "href": "{URI}/list-endpointsaccountgroup", "methods": ["GET"], "category": "reference_data", "description": "Account Group management and operations", "documentation_url": "https://developer.myob.com/api/exo/endpoints/list-endpoints/accountgroup/"}, {"rel": "collection/creditor", "title": "Creditor", "href": "{URI}/creditor", "methods": ["GET"], "category": "creditors", "description": "Creditor management and operations", "documentation_url": "https://developer.myob.com/api/exo/endpoints/creditor/", "search_supported": true, "search_href": "{URI}/creditor/search?q={query}", "report_href": "{URI}/creditor/{id}/report"}, {"rel": "collection/creditorcreditorhistorynote", "title": "Creditor History Note", "href": "{URI}/creditorcreditorhistorynote", "methods": ["GET"], "category": "creditors", "description": "Creditor History Note management and operations", "documentation_url": "https://developer.myob.com/api/exo/endpoints/creditor/creditorhistorynote/"}, {"rel": "collection/stockitem", "title": "StockItem", "href": "{URI}/stockitem", "methods": ["GET"], "category": "stock_management", "description": "Stock item management", "search_supported": true, "search_href": "{URI}/stockitem/search"}, {"rel": "collection/stock/sale", "title": "Stock Sale", "href": "{URI}/stock/sale", "methods": ["GET", "POST"], "category": "stock_operations", "description": "Stock sale transactions"}, {"rel": "collection/stock/receipt", "title": "Stock Receipt", "href": "{URI}/stock/receipt", "methods": ["GET", "POST"], "category": "stock_operations", "description": "Stock receipt transactions"}, {"rel": "collection/stock/transfer", "title": "Stock Transfer", "href": "{URI}/stock/transfer", "methods": ["GET", "POST"], "category": "stock_operations", "description": "Stock transfer between locations"}, {"rel": "collection/stock/adjustin", "title": "Stock AdjustIn", "href": "{URI}/stock/adjustin", "methods": ["GET", "POST"], "category": "stock_operations", "description": "Stock adjustment inward"}, {"rel": "collection/stock/adjustout", "title": "Stock AdjustOut", "href": "{URI}/stock/adjustout", "methods": ["GET", "POST"], "category": "stock_operations", "description": "Stock adjustment outward"}, {"rel": "collection/stock/transaction", "title": "Stock Transactions", "href": "{URI}/stock/transaction/{ledger_type}/{id}", "methods": ["GET"], "category": "stock_operations", "description": "Stock transaction history"}, {"rel": "collection/schema", "title": "GetForm <PERSON>a", "href": "{URI}/schema/getform", "methods": ["GET"], "category": "schemas", "description": "Form schema definitions for API validation and form generation"}]}