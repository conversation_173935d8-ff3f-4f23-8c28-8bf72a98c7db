# Technology Stack

## Core Technologies

- **Python 3.8+**: Primary development language
- **Flask**: Web framework for dashboard and API endpoints
- **Pydantic 2.0+**: Data validation and serialization with type hints
- **ChromaDB**: Vector database for AI context memory

## External APIs & Services

- **Gmail API**: Email fetching, label management, OAuth2 authentication
- **Google Gemini AI**: LLM for intelligent text processing and data extraction (gemini-2.5-flash)
- **MYOB EXO API**: Sales order creation and validation

## Key Libraries

- `google-api-python-client`: Gmail integration
- `google-genai`: Google Gen AI SDK (2025 GA version)
- `PyMuPDF`: PDF text extraction (also known as fitz)
- `beautifulsoup4`: HTML parsing
- `python-dotenv`: Environment configuration
- `colorlog`: Enhanced logging with colors
- `requests`: HTTP client for MYOB API
- `openai`: OpenAI-compatible Gemini interface

## Common Commands

### Setup & Installation

```bash
# Install dependencies
pip install -r requirements.txt

# Setup environment (create .env file with API keys)
# Required: GEMINI_API_KEY, Gmail credentials, MYOB API settings
```

### Running the System

```bash
# Main entry point - comprehensive system
python run_restructured_system.py system

# Individual components
python run_restructured_system.py dashboard
python run_restructured_system.py processor
python run_restructured_system.py enhanced
python run_restructured_system.py universal
python run_restructured_system.py polling

# Demo mode
python run_restructured_system.py dashboard --demo
python demo_dashboard.py

# Custom port
python run_restructured_system.py dashboard --port 8080
```

### Legacy Entry Points

```bash
# Direct component execution (legacy)
python comprehensive_email_system.py
python email_dashboard.py
python enhanced_universal_agent.py
python continuous_polling_agent.py
```

### Testing & Validation

```bash
# Setup validation
python test_setup.py

# Component tests
python test_basic_imports.py
python test_myob_service.py
python test_email_labeling.py
python test_brady_order.py

# System verification
python verify_working.py
```

### Development Tools

```bash
# Clean deprecated code
python cleanup_deprecated.py

# Fix imports
python fix_imports.py

# Search ChromaDB
python search_chroma.py
```

## Configuration Files

- `.env`: Environment variables and API keys
- `system_config.json`: System-wide configuration
- `universal_agent_config.yaml`: Universal agent settings
- `polling_config.json`: Continuous polling configuration
- `processing_rules.yaml`: Email processing rules
- `credentials.json`: Gmail OAuth2 credentials

## Environment Variables

### Required Gmail Settings

- `GMAIL_CREDENTIALS_FILE`: Path to OAuth2 credentials
- `GMAIL_LABELS_TO_PROCESS`: Comma-separated Gmail labels
- `GMAIL_UNREAD_ONLY`: Process only unread emails (True/False)
- `MAX_GMAIL_RESULTS`: Maximum emails per fetch

### Required AI Settings

- `GEMINI_API_KEY`: Google Gemini API key
- `GEMINI_MODEL`: Model name (default: gemini-2.5-flash)

### Required MYOB Settings

- `EXO_IP`, `EXO_PORT`: MYOB server details
- `USER`, `PWD`: MYOB API credentials
- `API_KEY`, `EXO_TOK`: MYOB API tokens

### Optional Settings

- `LOG_LEVEL`: Logging verbosity (DEBUG, INFO, WARNING, ERROR)
