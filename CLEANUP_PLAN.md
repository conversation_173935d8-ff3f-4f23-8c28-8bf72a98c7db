# TeamsysV0.1 Cleanup Plan

## Phase 1: Remove Demo Files
```bash
# Remove demo scripts (after testing)
del demo_comprehensive_gmail.py
del demo_enhanced_gmail.py
del demo_enhanced_llm.py
del demo_gmail_cli.py
del test_enhanced_gmail.py

# Remove one-time fix scripts
del fix_pdf_extraction.py
del process_brady_orders.py
del run_real_processing.py
```

## Phase 2: Archive Documentation
```bash
# Create archive directory
mkdir docs\archive

# Move old documentation
move doc_agent\*.md docs\archive\
move doc_agent\*.json docs\archive\
move doc_agent\*.html docs\archive\
```

## Phase 3: Clean Generated Files
```bash
# Clear old generated files (keep directories)
del myob\*.json
del markdown\*.md

# Remove temporary files
del token.pickle
```

## Phase 4: Consolidate Structure
```bash
# Review and potentially merge:
# - utils/ with services/
# - util_agent/ functionality into main system
# - tests/ organization
```

## Final Structure Should Be:
```
TeamsysV0.1/
├── main.py                    # Main entry point
├── gmail_cli.py              # CLI tool
├── config.py                 # Configuration
├── services/                 # Core services
├── db_agent/                 # Database operations
├── email_agent/              # Email processing (if distinct)
├── myob_agent/               # MYOB integration (if distinct)
├── docs/                     # Documentation
│   └── archive/              # Archived docs
├── myob/                     # Generated MYOB files (empty)
├── markdown/                 # Generated summaries (empty)
├── tests/                    # Organized tests
└── .kiro/                    # AI assistant config
```

## Priority Actions:
1. **Test current system** with `python main.py`
2. **Backup important data** before cleanup
3. **Remove demo files** first
4. **Archive documentation** to docs/archive/
5. **Clean generated files** but keep directory structure
6. **Test system** after each cleanup phase