"""
Unified Service Manager - Central coordination for all TeamsysV0.1 services.
Integrates email processing, MYOB management, and LLM services.
"""
import asyncio
import logging
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime

from main_processor import EmailOrderProcessor
from myob_service import MyobService
from myob_poster import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from llm_service.main import LLMService
from supabase_database_service import SupabaseService
from gmail_service import GmailService
from services.system_monitor import SystemMonitor
from services.performance_optimizer import PerformanceOptimizer

logger = logging.getLogger(__name__)


class UnifiedServiceManager:
    """Central manager for all TeamsysV0.1 services."""
    
    def __init__(self):
        """Initialize all services."""
        self.email_processor = EmailOrderProcessor()
        self.myob_service = MyobService()
        self.myob_poster = MYOBPoster()
        self.llm_service = LLMService()
        self.database_service = SupabaseService()
        self.gmail_service = GmailService()
        
        # Initialize monitoring and optimization services
        self.system_monitor = SystemMonitor()
        self.performance_optimizer = PerformanceOptimizer()
        
        logger.info("Unified Service Manager initialized with monitoring capabilities")
    
    # Email Processing Methods
    async def process_emails_interactive(
        self, 
        labels: List[str], 
        max_emails: int = 5,
        time_filter: Optional[str] = None,
        callback=None
    ) -> List[Dict[str, Any]]:
        """
        Process emails with real-time progress updates.
        
        Args:
            labels: Gmail labels to process
            max_emails: Maximum emails per label
            time_filter: Gmail time filter query
            callback: Progress callback function
            
        Returns:
            List of processed orders
        """
        logger.info(f"Starting interactive email processing: {labels}")
        
        try:
            if callback:
                callback("Starting email processing...", 0, len(labels))
            
            processed_orders = await self.email_processor.run_extraction_workflow(
                label_names=labels,
                max_emails=max_emails,
                time_filter=time_filter
            )
            
            if callback:
                callback("Email processing completed", len(labels), len(labels))
            
            logger.info(f"Processed {len(processed_orders)} orders")
            return processed_orders
            
        except Exception as e:
            logger.error(f"Email processing failed: {e}")
            if callback:
                callback(f"Error: {str(e)}", 0, 0)
            raise
    
    def get_email_processing_status(self) -> Dict[str, Any]:
        """Get current email processing status."""
        try:
            # Get recent processing statistics
            recent_orders = self.database_service.get_recent_emails(limit=10)
            
            return {
                "recent_orders_count": len(recent_orders) if recent_orders else 0,
                "last_processed": recent_orders[0].get('created_at') if recent_orders else None,
                "gmail_connected": hasattr(self.gmail_service, 'service') and self.gmail_service.service is not None,
                "llm_service_healthy": True  # TODO: Add health check
            }
        except Exception as e:
            logger.error(f"Failed to get email processing status: {e}")
            return {"error": str(e)}
    
    # MYOB Management Methods
    def get_pending_orders(self) -> List[Dict[str, Any]]:
        """Get all pending MYOB orders with details."""
        try:
            order_ids = self.myob_poster.list_pending_orders()
            orders = []
            
            for order_id in order_ids:
                order_data = self.myob_poster.load_order(order_id)
                if order_data:
                    # Add summary information
                    order_summary = {
                        "order_id": order_id,
                        "debtor_id": order_data.get('debtorid', 'N/A'),
                        "customer_po": order_data.get('customerordernumber', 'N/A'),
                        "line_count": len(order_data.get('lines', [])),
                        "status": "Ready",
                        "created_date": datetime.now().strftime("%Y-%m-%d"),  # TODO: Get actual date
                        "total_quantity": sum(line.get('orderquantity', 0) for line in order_data.get('lines', []))
                    }
                    orders.append(order_summary)
            
            logger.info(f"Found {len(orders)} pending orders")
            return orders
            
        except Exception as e:
            logger.error(f"Failed to get pending orders: {e}")
            return []
    
    async def validate_order_interactive(self, order_id: str, callback=None) -> Tuple[bool, str]:
        """
        Validate order with progress updates.
        
        Args:
            order_id: Order ID to validate
            callback: Progress callback function
            
        Returns:
            Tuple of (success, message)
        """
        try:
            if callback:
                callback(f"Loading order {order_id}...", 1, 4)
            
            order_data = self.myob_poster.load_order(order_id)
            if not order_data:
                return False, f"Could not load order {order_id}"
            
            if callback:
                callback("Validating order structure...", 2, 4)
            
            # Basic validation
            is_valid, errors = self.myob_poster.validate_order_data(order_data)
            if not is_valid:
                return False, f"Structure validation failed: {'; '.join(errors)}"
            
            if callback:
                callback("Validating with MYOB API...", 3, 4)
            
            # MYOB API validation
            success, validated_order, message = self.myob_poster.validate_with_myob(order_data)
            
            if callback:
                callback("Validation completed", 4, 4)
            
            return success, message
            
        except Exception as e:
            logger.error(f"Order validation failed: {e}")
            return False, f"Validation error: {str(e)}"
    
    async def post_order_interactive(self, order_id: str, callback=None) -> Tuple[bool, str]:
        """
        Post order to MYOB with real-time feedback.
        
        Args:
            order_id: Order ID to post
            callback: Progress callback function
            
        Returns:
            Tuple of (success, message)
        """
        try:
            if callback:
                callback(f"Processing order {order_id}...", 0, 1)
            
            success, message = self.myob_poster.post_order_to_myob(order_id)
            
            if callback:
                status = "completed successfully" if success else "failed"
                callback(f"Order {order_id} {status}", 1, 1)
            
            return success, message
            
        except Exception as e:
            logger.error(f"Order posting failed: {e}")
            return False, f"Posting error: {str(e)}"
    
    async def process_order_batch(self, order_ids: List[str], callback=None) -> List[Tuple[str, bool, str]]:
        """
        Process multiple orders with progress updates.
        
        Args:
            order_ids: List of order IDs to process
            callback: Progress callback function
            
        Returns:
            List of (order_id, success, message) tuples
        """
        results = []
        total = len(order_ids)
        
        try:
            for i, order_id in enumerate(order_ids):
                if callback:
                    callback(f"Processing order {order_id}...", i, total)
                
                success, message = await self.post_order_interactive(order_id)
                results.append((order_id, success, message))
                
                # Small delay to avoid overwhelming the API
                if i < total - 1:
                    await asyncio.sleep(1)
            
            if callback:
                successful = sum(1 for _, success, _ in results if success)
                callback(f"Batch completed: {successful}/{total} successful", total, total)
            
            return results
            
        except Exception as e:
            logger.error(f"Batch processing failed: {e}")
            if callback:
                callback(f"Batch failed: {str(e)}", 0, total)
            return results
    
    def get_myob_status(self) -> Dict[str, Any]:
        """Get MYOB service status."""
        try:
            pending_orders = self.get_pending_orders()
            
            return {
                "pending_orders_count": len(pending_orders),
                "myob_service_connected": True,  # TODO: Add health check
                "last_order_date": pending_orders[0].get('created_date') if pending_orders else None,
                "total_quantity_pending": sum(order.get('total_quantity', 0) for order in pending_orders)
            }
        except Exception as e:
            logger.error(f"Failed to get MYOB status: {e}")
            return {"error": str(e)}
    
    # System Status Methods
    def get_system_status(self) -> Dict[str, Any]:
        """Get overall system status."""
        try:
            email_status = self.get_email_processing_status()
            myob_status = self.get_myob_status()
            
            return {
                "timestamp": datetime.now().isoformat(),
                "email_processing": email_status,
                "myob_management": myob_status,
                "services": {
                    "email_processor": "healthy",
                    "myob_service": "healthy",
                    "llm_service": "healthy",
                    "database_service": "healthy"
                }
            }
        except Exception as e:
            logger.error(f"Failed to get system status: {e}")
            return {"error": str(e), "timestamp": datetime.now().isoformat()}
    
    # Enhanced Monitoring Methods
    async def comprehensive_health_check(self) -> Dict[str, Any]:
        """Perform comprehensive health check using system monitor."""
        try:
            return await self.system_monitor.comprehensive_health_check()
        except Exception as e:
            logger.error(f"Comprehensive health check failed: {e}")
            return {"error": str(e)}
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance optimization summary."""
        try:
            return self.performance_optimizer.get_performance_summary()
        except Exception as e:
            logger.error(f"Performance summary failed: {e}")
            return {"error": str(e)}
    
    def get_system_metrics(self) -> Dict[str, Any]:
        """Get current system performance metrics."""
        try:
            metrics = self.system_monitor.get_system_metrics()
            return {
                "cpu_percent": metrics.cpu_percent,
                "memory_percent": metrics.memory_percent,
                "disk_percent": metrics.disk_percent,
                "uptime_hours": metrics.uptime_seconds / 3600,
                "timestamp": metrics.timestamp.isoformat()
            }
        except Exception as e:
            logger.error(f"System metrics failed: {e}")
            return {"error": str(e)}
    
    # Performance Optimization Methods
    @property
    def performance_monitor(self):
        """Get performance monitor decorator."""
        return self.performance_optimizer.performance_monitor
    
    async def optimize_email_batch_processing(self, emails: List[Any], process_func) -> List[Any]:
        """Optimize email batch processing with adaptive sizing."""
        try:
            return await self.performance_optimizer.optimize_batch_processing(
                items=emails,
                process_func=process_func,
                initial_batch_size=5,
                max_batch_size=20
            )
        except Exception as e:
            logger.error(f"Email batch optimization failed: {e}")
            raise
    
    def cache_customer_lookup(self, customer_id: str, data: Dict[str, Any]) -> None:
        """Cache customer lookup data for performance."""
        self.performance_optimizer.cache_customer_data(customer_id, data)
    
    def get_cached_customer_lookup(self, customer_id: str) -> Optional[Dict[str, Any]]:
        """Get cached customer lookup data."""
        return self.performance_optimizer.get_cached_customer_data(customer_id)
    
    # Enhanced System Status Methods
    def get_enhanced_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status with monitoring data."""
        try:
            base_status = self.get_system_status()
            health_summary = self.system_monitor.get_health_summary()
            performance_summary = self.performance_optimizer.get_performance_summary()
            system_metrics = self.get_system_metrics()
            
            return {
                **base_status,
                "health_summary": health_summary,
                "performance_summary": performance_summary,
                "system_metrics": system_metrics,
                "monitoring_enabled": True
            }
        except Exception as e:
            logger.error(f"Enhanced system status failed: {e}")
            return {"error": str(e), "timestamp": datetime.now().isoformat()}
    
    # Utility Methods
    async def health_check(self) -> Dict[str, bool]:
        """Perform basic health check on all services."""
        health = {}
        
        try:
            # Test email processor
            health["email_processor"] = hasattr(self.email_processor, 'gmail_service')
            
            # Test MYOB service
            health["myob_service"] = hasattr(self.myob_service, 'base_url')
            
            # Test LLM service
            health["llm_service"] = hasattr(self.llm_service, 'mistral_service')
            
            # Test database service
            health["database_service"] = hasattr(self.database_service, 'client')
            
            # Test Gmail service
            health["gmail_service"] = hasattr(self.gmail_service, 'service')
            
            # Test monitoring services
            health["system_monitor"] = hasattr(self.system_monitor, 'health_history')
            health["performance_optimizer"] = hasattr(self.performance_optimizer, 'metrics')
            
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            health["error"] = str(e)
        
        return health
    
    def export_comprehensive_reports(self) -> Dict[str, Any]:
        """Export comprehensive system and performance reports."""
        try:
            return {
                "timestamp": datetime.now().isoformat(),
                "system_status": self.get_enhanced_system_status(),
                "health_report": self.system_monitor.export_health_report(),
                "performance_report": self.performance_optimizer.export_performance_report()
            }
        except Exception as e:
            logger.error(f"Report export failed: {e}")
            return {"error": str(e)}