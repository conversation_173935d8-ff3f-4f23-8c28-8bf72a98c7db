{"enabled": true, "name": "Session Handover Generator", "description": "Generates a temporary handover file at the end of agent sessions to provide context to the next session, which can be removed after being read", "version": "1", "when": {"type": "userTriggered", "patterns": ["*.py", "*.md", "*.json", "config.py", "cli.py", "main_processor.py"]}, "then": {"type": "askAgent", "prompt": "A session is ending. Generate a handover temp file that summarizes the current state, recent changes, and important context for the next agent session. Include: 1) Recent file modifications and their purpose, 2) Current system state and any ongoing processes, 3) Key decisions made or issues encountered, 4) Next steps or priorities for the incoming session. Save this as 'session_handover_temp.md' in the root directory. This file should be comprehensive enough for the next agent to understand the current context and continue work seamlessly."}}