# 📧 Gmail LLM Agent Implementation Plan

## 🎯 Overview
Create a natural language Gmail query agent similar to the MYOB Query Agent, enabling users to interact with Gmail data using conversational queries powered by LLM intelligence.

## 📋 Current State Analysis

### **Existing Gmail Integration**
- ✅ **Gmail Service** (`gmail_service.py`) - Full Gmail API integration
- ✅ **Authentication** - OAuth2 flow working
- ✅ **Email Processing** - Fetch, parse, and label emails
- ✅ **Label Management** - Create and manage Gmail labels

### **Gmail API Capabilities** (from discovery document)
- **Messages**: Search, get, list, modify, send, delete
- **Threads**: List, get, modify, delete
- **Labels**: Create, delete, list, update, patch
- **Drafts**: Create, delete, get, list, send, update
- **History**: List changes since historyId
- **Settings**: Manage Gmail settings and filters
- **Attachments**: Get attachment data

## 🚀 Implementation Plan

### **Phase 1: Gmail API Discovery Document Integration**

#### **1.1 Fetch Gmail API Schema**
```python
# File: scripts/fetch_gmail_discovery.py
import requests
import json
from pathlib import Path

def fetch_gmail_discovery_doc():
    """Fetch Gmail API v1 discovery document."""
    url = "https://www.googleapis.com/discovery/v1/apis/gmail/v1/rest"
    
    response = requests.get(url)
    discovery_doc = response.json()
    
    # Save to file for LLM agent
    with open('gmail_api_discovery.json', 'w') as f:
        json.dump(discovery_doc, f, indent=2)
    
    return discovery_doc
```

#### **1.2 Parse API Endpoints and Methods**
```python
# File: agents/gmail_api_parser.py
class GmailAPIParser:
    """Parse Gmail API discovery document for LLM consumption."""
    
    def __init__(self, discovery_doc_path: str):
        self.discovery_doc = self._load_discovery_doc(discovery_doc_path)
        self.endpoints = self._parse_endpoints()
    
    def _parse_endpoints(self) -> List[Dict]:
        """Extract all Gmail API endpoints with descriptions."""
        endpoints = []
        
        resources = self.discovery_doc.get('resources', {})
        for resource_name, resource_data in resources.items():
            methods = resource_data.get('methods', {})
            for method_name, method_data in methods.items():
                endpoint = {
                    "resource": resource_name,
                    "method": method_name,
                    "http_method": method_data.get('httpMethod', 'GET'),
                    "path": method_data.get('path', ''),
                    "description": method_data.get('description', ''),
                    "parameters": method_data.get('parameters', {}),
                    "scopes": method_data.get('scopes', [])
                }
                endpoints.append(endpoint)
        
        return endpoints
    
    def get_endpoints_by_category(self) -> Dict[str, List[Dict]]:
        """Categorize endpoints for LLM understanding."""
        categories = {
            "message_operations": [],
            "thread_operations": [],
            "label_operations": [],
            "search_operations": [],
            "draft_operations": [],
            "settings_operations": [],
            "attachment_operations": []
        }
        
        for endpoint in self.endpoints:
            resource = endpoint["resource"]
            method = endpoint["method"]
            
            if resource == "users.messages":
                if "search" in method or "list" in method:
                    categories["search_operations"].append(endpoint)
                else:
                    categories["message_operations"].append(endpoint)
            elif resource == "users.threads":
                categories["thread_operations"].append(endpoint)
            elif resource == "users.labels":
                categories["label_operations"].append(endpoint)
            elif resource == "users.drafts":
                categories["draft_operations"].append(endpoint)
            elif resource == "users.settings":
                categories["settings_operations"].append(endpoint)
            elif "attachments" in resource:
                categories["attachment_operations"].append(endpoint)
        
        return categories
```

### **Phase 2: Gmail Query Agent Core**

#### **2.1 Create Gmail Query Agent**
```python
# File: agents/gmail_query_agent.py
class GmailQueryAgent:
    """LLM-powered natural language Gmail querying."""
    
    def __init__(self):
        self.gmail_service = GmailService()
        self.llm_service = LLMService()
        self.api_parser = GmailAPIParser('gmail_api_discovery.json')
        self.endpoints_catalog = self.api_parser.get_endpoints_by_category()
        
    async def process_natural_query(self, user_query: str) -> str:
        """Process natural language Gmail query."""
        
        # Step 1: Classify query intent
        intent = await self._classify_gmail_intent(user_query)
        
        # Step 2: Map to Gmail API operations
        operations = self._map_intent_to_operations(intent)
        
        # Step 3: Generate Gmail API calls
        api_calls = await self._generate_gmail_api_calls(user_query, operations)
        
        # Step 4: Execute and format results
        results = await self._execute_and_format_gmail(api_calls, user_query)
        
        return results
    
    async def _classify_gmail_intent(self, query: str) -> Dict:
        """Classify Gmail query into operation categories."""
        classification_prompt = f"""
        Classify this Gmail query into categories:
        
        Query: "{query}"
        
        Categories:
        - email_search: Finding specific emails or messages
        - email_management: Organizing, labeling, archiving emails
        - thread_operations: Working with email conversations
        - contact_analysis: Information about senders/recipients
        - attachment_operations: Finding or managing attachments
        - label_management: Creating, modifying Gmail labels
        - draft_operations: Working with draft emails
        - settings_management: Gmail settings and configuration
        - analytics: Email statistics and analysis
        
        Extract entities:
        - sender_emails: Email addresses mentioned
        - date_ranges: Time periods (last week, this month, etc.)
        - keywords: Important search terms
        - labels: Gmail labels mentioned
        - subjects: Email subjects referenced
        - attachment_types: File types mentioned
        
        Return JSON: {{
            "primary_intent": "category",
            "confidence": 0.95,
            "secondary_intents": ["other_categories"],
            "extracted_entities": {{
                "sender_emails": ["<EMAIL>"],
                "date_ranges": ["last week"],
                "keywords": ["urgent", "invoice"],
                "labels": ["Important"],
                "subjects": ["Meeting"],
                "attachment_types": ["pdf", "xlsx"]
            }},
            "reasoning": "explanation"
        }}
        """
        
        response = await self.llm_service.mistral_service.generate_content(classification_prompt)
        return json.loads(response)
```

#### **2.2 Gmail API Operation Mapping**
```python
def _map_intent_to_operations(self, intent: Dict) -> List[Dict]:
    """Map classified intent to Gmail API operations."""
    intent_mapping = {
        "email_search": ["users.messages.list", "users.messages.get"],
        "email_management": ["users.messages.modify", "users.labels.create"],
        "thread_operations": ["users.threads.list", "users.threads.get"],
        "contact_analysis": ["users.messages.list", "users.history.list"],
        "attachment_operations": ["users.messages.attachments.get"],
        "label_management": ["users.labels.list", "users.labels.create"],
        "draft_operations": ["users.drafts.list", "users.drafts.get"],
        "settings_management": ["users.settings.getAutoForwarding"],
        "analytics": ["users.messages.list", "users.threads.list"]
    }
    
    primary = intent.get("primary_intent", "")
    relevant_operations = intent_mapping.get(primary, ["users.messages.list"])
    
    # Get operation details from endpoints catalog
    operations = []
    for category, endpoints in self.endpoints_catalog.items():
        for endpoint in endpoints:
            operation_id = f"{endpoint['resource']}.{endpoint['method']}"
            if operation_id in relevant_operations:
                operations.append(endpoint)
    
    return operations
```

### **Phase 3: Gmail API Integration**

#### **3.1 Enhanced Gmail Service Methods**
```python
# File: gmail_service.py - Add new methods
class GmailService:
    # ... existing methods ...
    
    def search_messages_advanced(self, query: str, max_results: int = 50) -> List[Dict]:
        """Advanced message search with detailed results."""
        try:
            results = self.service.users().messages().list(
                userId='me',
                q=query,
                maxResults=max_results
            ).execute()
            
            messages = results.get('messages', [])
            detailed_messages = []
            
            for msg in messages:
                msg_detail = self.service.users().messages().get(
                    userId='me',
                    id=msg['id'],
                    format='metadata'
                ).execute()
                detailed_messages.append(msg_detail)
            
            return detailed_messages
            
        except Exception as e:
            logger.error(f"Advanced search failed: {e}")
            return []
    
    def get_thread_details(self, thread_id: str) -> Dict:
        """Get detailed thread information."""
        try:
            thread = self.service.users().threads().get(
                userId='me',
                id=thread_id
            ).execute()
            return thread
        except Exception as e:
            logger.error(f"Thread details failed: {e}")
            return {}
    
    def analyze_sender_patterns(self, days_back: int = 30) -> Dict:
        """Analyze email sender patterns."""
        try:
            from datetime import datetime, timedelta
            
            # Calculate date range
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days_back)
            
            query = f"after:{start_date.strftime('%Y/%m/%d')}"
            messages = self.search_messages_advanced(query, max_results=500)
            
            # Analyze senders
            sender_counts = {}
            for msg in messages:
                headers = msg.get('payload', {}).get('headers', [])
                for header in headers:
                    if header['name'] == 'From':
                        sender = header['value']
                        sender_counts[sender] = sender_counts.get(sender, 0) + 1
            
            # Sort by frequency
            sorted_senders = sorted(sender_counts.items(), key=lambda x: x[1], reverse=True)
            
            return {
                "total_messages": len(messages),
                "unique_senders": len(sender_counts),
                "top_senders": sorted_senders[:10],
                "date_range": f"{start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}"
            }
            
        except Exception as e:
            logger.error(f"Sender analysis failed: {e}")
            return {}
```

### **Phase 4: Natural Language Query Examples**

#### **4.1 Supported Query Types**
```python
EXAMPLE_GMAIL_QUERIES = [
    # Email Search
    "Show me <NAME_EMAIL> in the last week",
    "Find all emails with 'invoice' in the subject",
    "List unread emails from today",
    "Show me emails with PDF attachments",
    
    # Thread Operations
    "Find the longest email conversation this month",
    "Show me all replies to the email about 'project update'",
    
    # Contact Analysis
    "Who sends me the most emails?",
    "Show me all emails from @company.com domain",
    "List all unique senders from last month",
    
    # Label Management
    "Show me all emails labeled 'Important'",
    "List all my Gmail labels",
    "Find emails that need to be labeled",
    
    # Analytics
    "How many emails did I receive this week?",
    "What are my most active email threads?",
    "Show me email volume by day for the last month",
    
    # Attachment Operations
    "Find all emails with Excel attachments",
    "Show me the largest email attachments",
    "List emails with multiple attachments"
]
```

#### **4.2 Query Processing Examples**
```python
async def _generate_gmail_api_calls(self, query: str, operations: List[Dict]) -> List[Dict]:
    """Generate specific Gmail API calls."""
    api_generation_prompt = f"""
    Generate Gmail API calls for this query:
    
    Query: "{query}"
    
    Available operations:
    {json.dumps(operations[:3], indent=2)}
    
    Generate 1-3 specific API calls with:
    - method: HTTP method (GET, POST, etc.)
    - endpoint: Gmail API endpoint
    - parameters: Query parameters and filters
    - description: What this call retrieves
    
    Gmail Query Syntax:
    - from:<EMAIL> (emails from specific sender)
    - subject:"text" (emails with specific subject)
    - has:attachment (emails with attachments)
    - after:2024/01/01 (emails after date)
    - before:2024/12/31 (emails before date)
    - is:unread (unread emails)
    - label:Important (emails with specific label)
    
    Return JSON array:
    [
        {{
            "method": "GET",
            "endpoint": "users.messages.list",
            "parameters": {{"q": "from:<EMAIL> after:2024/01/01"}},
            "description": "Search for emails from specific sender"
        }}
    ]
    """
    
    response = await self.llm_service.mistral_service.generate_content(api_generation_prompt)
    return json.loads(response)
```

### **Phase 5: GUI Integration**

#### **5.1 Add Gmail Query Tab to Enhanced CLI**
```python
# File: enhanced_cli.py - Add new tab
class GmailQueryTab(Container):
    """Tab for natural language Gmail querying."""
    
    def __init__(self, gmail_query_agent: GmailQueryAgent):
        super().__init__()
        self.gmail_query_agent = gmail_query_agent
        self.processing = False
    
    def compose(self) -> ComposeResult:
        yield Vertical(
            # Query input section
            Container(
                Input(
                    placeholder="Ask about your Gmail data... (e.g., 'Show me unread emails from this week')",
                    id="gmail-query-input"
                ),
                Horizontal(
                    Button("Query Gmail", id="gmail-query-btn", variant="primary"),
                    Button("Clear Results", id="gmail-clear-btn"),
                    Button("Show Examples", id="gmail-examples-btn"),
                    classes="gmail-query-controls"
                ),
                classes="gmail-query-section"
            ),
            
            # Results section
            Container(
                Static("Gmail Query Results", classes="section-title"),
                Log(id="gmail-query-results", auto_scroll=True),
                classes="gmail-results-section"
            ),
            
            # Examples section
            Container(
                Static("Example Gmail Queries", classes="section-title"),
                Log(id="gmail-query-examples", auto_scroll=False),
                classes="gmail-examples-section"
            ),
            
            classes="gmail-query-tab"
        )
```

#### **5.2 Update Main App with Gmail Tab**
```python
# File: enhanced_cli.py - Update TeamsysApp
def compose(self) -> ComposeResult:
    """Compose the main UI."""
    yield Header(show_clock=True)
    
    with TabbedContent(initial="email"):
        with TabPane("Email Processing", id="email"):
            yield EmailProcessingTab(self.service_manager)
        
        with TabPane("MYOB Management", id="myob"):
            yield MYOBManagementTab(self.myob_manager)
        
        with TabPane("MYOB Query", id="myob-query"):
            yield MYOBQueryTab(self.query_agent)
        
        with TabPane("Gmail Query", id="gmail-query"):
            yield GmailQueryTab(self.gmail_query_agent)  # New tab
        
        with TabPane("System Status", id="status"):
            yield SystemStatusTab(self.service_manager)
    
    yield Footer()
```

### **Phase 6: Implementation Steps**

#### **Week 1: Foundation**
1. ✅ Fetch Gmail API discovery document using Playwright/requests
2. ✅ Create Gmail API parser for endpoint extraction
3. ✅ Build basic Gmail Query Agent structure
4. ✅ Implement intent classification for Gmail queries

#### **Week 2: Core Functionality**
5. ✅ Implement Gmail API operation mapping
6. ✅ Create enhanced Gmail service methods
7. ✅ Build query-to-API-call generation
8. ✅ Implement result formatting and display

#### **Week 3: GUI Integration**
9. ✅ Add Gmail Query tab to enhanced CLI
10. ✅ Implement interactive query interface
11. ✅ Add example queries and help system
12. ✅ Test end-to-end functionality

#### **Week 4: Advanced Features**
13. ✅ Add email analytics and insights
14. ✅ Implement attachment analysis
15. ✅ Create sender pattern analysis
16. ✅ Add export capabilities

## 🎯 Expected Capabilities

### **Natural Language Queries**
- "Show me all emails from my boss this week"
- "Find emails with invoices that I haven't read"
- "Who are my top 10 email contacts?"
- "Show me the biggest email attachments"
- "List all emails about the Johnson project"

### **Advanced Analytics**
- Email volume trends over time
- Sender frequency analysis
- Attachment type distribution
- Response time patterns
- Label usage statistics

### **Smart Insights**
- Identify important unread emails
- Suggest emails that need follow-up
- Find duplicate or similar emails
- Detect unusual email patterns
- Recommend label organization

## 🚀 Implementation Priority

### **High Priority (Immediate)**
1. Fetch Gmail discovery document
2. Create basic Gmail Query Agent
3. Implement core search functionality
4. Add Gmail Query tab to GUI

### **Medium Priority (Next)**
1. Advanced analytics features
2. Attachment analysis
3. Sender pattern recognition
4. Export capabilities

### **Low Priority (Future)**
1. Email automation suggestions
2. Smart filtering recommendations
3. Integration with calendar/tasks
4. Advanced visualization

This plan will create a powerful Gmail LLM agent that matches the sophistication of the MYOB Query Agent, enabling natural language interaction with Gmail data through the enhanced CLI interface.