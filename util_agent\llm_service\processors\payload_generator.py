"""Payload generator for MYOB-compatible data."""

import logging
from typing import Dict, Any, Optional

from models import ExtractedOrder, MYOBPayload
from ..core.exceptions import ValidationException

logger = logging.getLogger(__name__)


class PayloadGenerator:
    """Generator for MYOB-compatible payloads."""
    
    def __init__(self):
        # Using direct payload generation - no LLM service needed
        pass
    
    def generate_myob_payload_direct(self, extracted_order: ExtractedOrder) -> Dict[str, Any]:
        """Generate MYOB payload directly from extracted order (no LLM) - more reliable."""
        
        customer_details = extracted_order.customer_details
        
        # A valid debtor_id is required. The OrderProcessor should have set it to 0 if unknown.
        debtor_id = customer_details.debtor_id
        
        # Check if we have a valid debtor_id
        if debtor_id is None or debtor_id == 0:
            # Try to determine debtor_id from customer name
            customer_name = customer_details.customer_name
            if customer_name:
                if "woolworths" in customer_name.lower():
                    logger.info("Setting debtor_id to 5760 for Woolworths")
                    debtor_id = 5760
                elif "brady" in customer_name.lower():
                    logger.info("Setting debtor_id to 5761 for <PERSON>")
                    debtor_id = 5761
                else:
                    logger.warning(f"Could not determine debtor_id for customer: {customer_name}. Defaulting to 0.")
                    debtor_id = 0
            else:
                logger.warning("Debtor ID was None during payload generation and no customer name available. Defaulting to 0 for MYOB.")
                debtor_id = 0

        # Build the basic payload
        payload = {
            "debtorid": debtor_id,
            "status": extracted_order.order_status or 0,
            "defaultlocationid": 1,
            "lines": [],
        }
        
        # Add customer order number if present
        if customer_details.customer_order_number:
            payload["customerordernumber"] = customer_details.customer_order_number
        
        # Add order lines
        for line in extracted_order.order_lines:
            payload["lines"].append({
                "stockcode": line.stockcode,
                "orderquantity": float(line.orderquantity)
            })
        
        # --- THIS IS THE CORRECTED LOGIC ---
        # Add delivery address if present by looking inside customer_details
        if customer_details.delivery_address:
            addr = customer_details.delivery_address
            payload["deliveryaddress"] = {
                "line1": addr.line1 or None,
                "line2": addr.line2 or None,
                "line3": addr.line3 or None,
                "line4": addr.line4 or None,
                "line5": addr.line5 or None,
                "line6": addr.line6 or None,
            }
            # Remove empty lines from the final payload to keep it clean
            payload["deliveryaddress"] = {k: v for k, v in payload["deliveryaddress"].items() if v}

        # Add extrafields for shipping method
        extrafields = []
        if extracted_order.X_SHIPVIA:
            extrafields.append({
                "key": "X_SHIPVIA",
                "value": extracted_order.X_SHIPVIA
            })
        
        if extrafields:
            payload["extrafields"] = extrafields
        
        logger.info("Generated minimal MYOB payload successfully")
        return payload
    
    def validate_myob_payload(self, payload: Dict[str, Any]) -> bool:
        """Validate MYOB payload structure."""
        required_fields = ["debtorid", "lines"]
        
        for field in required_fields:
            if field not in payload:
                raise ValidationException(f"Missing required field: {field}")
        
        if not isinstance(payload["lines"], list) or len(payload["lines"]) == 0:
            raise ValidationException("Lines must be a non-empty array")
        
        for i, line in enumerate(payload["lines"]):
            if "stockcode" not in line or "orderquantity" not in line:
                raise ValidationException(f"Line {i+1} missing required fields: stockcode, orderquantity")
        
        # Validate extrafields format if present
        if "extrafields" in payload:
            if not isinstance(payload["extrafields"], list):
                raise ValidationException("extrafields must be an array")
            
            for i, field in enumerate(payload["extrafields"]):
                if not isinstance(field, dict):
                    raise ValidationException(f"extrafields[{i}] must be an object")
                if "key" not in field or "value" not in field:
                    raise ValidationException(f"extrafields[{i}] must have 'key' and 'value' properties")
        
        return True
    
    def create_myob_payload(self, extracted_order: ExtractedOrder) -> MYOBPayload:
        """Create validated MYOB payload from extracted order."""
        payload_dict = self.generate_myob_payload_direct(extracted_order)
        self.validate_myob_payload(payload_dict)
        return MYOBPayload(**payload_dict)