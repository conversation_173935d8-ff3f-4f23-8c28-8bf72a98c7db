"""
Supabase Database Service for TeamsysV0.1
Handles all database operations with proper schema mapping.
"""
import logging
import json
import os
from typing import List, Optional, Dict, Any, Union
from datetime import datetime
import uuid

from supabase import create_client, Client
from config import config
from models import (
    EmailData, EmailAttachment, EmailCategorization, ERPPayload, 
    OrderLineItem, ExtractedOrder, ProcessedOrder
)

logger = logging.getLogger(__name__)

class SupabaseService:
    """Supabase database service with proper schema mapping."""
    
    def __init__(self):
        """Initialize Supabase connection."""
        self.supabase: Client = create_client(config.SUPABASE_URL, config.SUPABASE_KEY)
        logger.info("Supabase database service initialized")
    
    # =============================================================================
    # EMAIL OPERATIONS
    # =============================================================================
    
    def save_email(self, email_data: EmailData) -> Optional[str]:
        """Save email to Supabase and return the database UUID."""
        try:
            # Check if email already exists
            existing = self.supabase.table("emails").select("id").eq("gmail_id", email_data.gmail_id).execute()
            if existing.data:
                logger.info(f"Email {email_data.gmail_id} already exists")
                return existing.data[0]["id"]
            
            # Prepare email record for Supabase schema
            email_record = {
                "gmail_id": email_data.gmail_id,
                "subject": email_data.subject,
                "sender": email_data.sender,
                "recipient": email_data.recipient,
                "body": email_data.body,
                "html_body": email_data.html_body,
                "thread_id": email_data.thread_id,
                "label_ids": email_data.label_ids,
                "received_date": email_data.received_date.isoformat() if email_data.received_date else None,
                "processed_date": email_data.processed_date.isoformat() if email_data.processed_date else None,
                "category": email_data.category,
                "debtor_id": email_data.debtor_id,
                "confidence_score": email_data.confidence_score,
                "has_attachments": email_data.has_attachments,
                "attachment_count": email_data.attachment_count
            }
            
            # Insert email
            result = self.supabase.table("emails").insert(email_record).execute()
            if result.data:
                email_uuid = result.data[0]["id"]
                logger.info(f"Saved email {email_data.gmail_id} with UUID {email_uuid}")
                
                # Save attachments if any
                if email_data.attachments:
                    self._save_attachments(email_uuid, email_data.attachments)
                
                return email_uuid
            return None
            
        except Exception as e:
            logger.error(f"Failed to save email {email_data.gmail_id}: {e}")
            return None
    
    def _save_attachments(self, email_uuid: str, attachments: List[EmailAttachment]) -> bool:
        """Save email attachments to database."""
        try:
            attachment_records = []
            for attachment in attachments:
                # Truncate filename if too long (max 255 chars for varchar)
                filename = attachment.filename
                if len(filename) > 255:
                    # Keep extension but truncate the name part
                    name, ext = os.path.splitext(filename)
                    max_name_length = 255 - len(ext)
                    filename = name[:max_name_length] + ext
                    logger.warning(f"Truncated long filename: {attachment.filename} -> {filename}")
                
                # Handle extracted text - use TEXT type which supports longer content
                extracted_text = attachment.extracted_text
                if extracted_text and len(extracted_text) > 1000000:  # 1MB limit for safety
                    extracted_text = extracted_text[:1000000]
                    logger.warning(f"Truncated very long extracted text for attachment {filename}")
                
                record = {
                    "email_id": email_uuid,
                    "filename": filename,
                    "content_type": attachment.content_type,
                    "size_bytes": attachment.size_bytes,
                    "attachment_id": attachment.attachment_id,
                    "processed": attachment.processed,
                    "extracted_text": extracted_text
                }
                attachment_records.append(record)
            
            result = self.supabase.table("email_attachments").insert(attachment_records).execute()
            logger.info(f"Saved {len(attachment_records)} attachments for email {email_uuid}")
            return bool(result.data)
            
        except Exception as e:
            logger.error(f"Failed to save attachments: {e}")
            return False
    
    def save_email_categorization(self, categorization: EmailCategorization) -> Optional[str]:
        """Save email categorization to database."""
        try:
            record = {
                "email_id": categorization.email_id,
                "category": categorization.category,
                "subcategory": categorization.subcategory,
                "confidence_score": categorization.confidence_score,
                "extracted_data": categorization.extracted_data,
                "model_used": categorization.model_used,
                "processing_time_ms": categorization.processing_time_ms
            }
            
            result = self.supabase.table("email_categorizations").insert(record).execute()
            if result.data:
                logger.info(f"Saved categorization for email {categorization.email_id}")
                return result.data[0]["id"]
            return None
            
        except Exception as e:
            logger.error(f"Failed to save categorization: {e}")
            return None
    
    # =============================================================================
    # ERP PAYLOAD OPERATIONS
    # =============================================================================
    
    def save_erp_payload(self, payload: ERPPayload) -> Optional[str]:
        """Save ERP payload to database."""
        try:
            # Validate that debtor_id exists in customers table
            if not self.validate_debtor_id(payload.debtor_id):
                logger.error(f"Cannot save ERP payload: debtor_id {payload.debtor_id} not found in customers table")
                return None
            
            record = {
                "email_id": payload.email_id,
                "debtor_id": payload.debtor_id,
                "payload_type": payload.payload_type,
                "payload_data": payload.payload_data,
                "status": payload.status,
                "approval_required": payload.approval_required,
                "approved_by": payload.approved_by,
                "approved_at": payload.approved_at.isoformat() if payload.approved_at else None,
                "submitted_at": payload.submitted_at.isoformat() if payload.submitted_at else None,
                "erp_response": payload.erp_response,
                "customer_order_number": payload.customer_order_number,
                "po_number": payload.po_number,
                "total_amount": payload.total_amount
            }
            
            result = self.supabase.table("erp_payloads").insert(record).execute()
            if result.data:
                payload_uuid = result.data[0]["id"]
                logger.info(f"Saved ERP payload {payload_uuid}")
                return payload_uuid
            return None
            
        except Exception as e:
            logger.error(f"Failed to save ERP payload: {e}")
            return None
    
    def save_order_line_items(self, line_items: List[OrderLineItem]) -> bool:
        """Save order line items to database."""
        try:
            if not line_items:
                return True
                
            records = []
            for item in line_items:
                record = {
                    "email_id": item.email_id,
                    "erp_payload_id": item.erp_payload_id,
                    "line_number": item.line_number,
                    "stock_code": item.stock_code,
                    "description": item.description,
                    "quantity": item.quantity,
                    "unit_price": item.unit_price,
                    "line_total": item.line_total,
                    "unit_of_measure": item.unit_of_measure
                }
                records.append(record)
            
            result = self.supabase.table("order_line_items").insert(records).execute()
            logger.info(f"Saved {len(records)} order line items")
            return bool(result.data)
            
        except Exception as e:
            logger.error(f"Failed to save order line items: {e}")
            return False
    
    # =============================================================================
    # CUSTOMER OPERATIONS
    # =============================================================================
    
    def validate_debtor_id(self, debtor_id: int) -> bool:
        """Validate that a debtor_id exists in the customers table."""
        try:
            result = self.supabase.table("customers").select("debtor_id").eq("debtor_id", debtor_id).execute()
            exists = bool(result.data)
            if not exists:
                logger.warning(f"Debtor ID {debtor_id} not found in customers table")
            return exists
        except Exception as e:
            logger.error(f"Failed to validate debtor ID {debtor_id}: {e}")
            return False
    
    def get_customer_by_debtor_id(self, debtor_id: int) -> Optional[Dict[str, Any]]:
        """Get customer by debtor ID."""
        try:
            result = self.supabase.table("customers").select("*").eq("debtor_id", debtor_id).execute()
            return result.data[0] if result.data else None
        except Exception as e:
            logger.error(f"Failed to get customer {debtor_id}: {e}")
            return None
    
    def find_customer_by_email_domain(self, email_address: str) -> Optional[Dict[str, Any]]:
        """Find customer by email domain matching."""
        try:
            domain = email_address.split('@')[-1] if '@' in email_address else email_address
            result = self.supabase.table("customers").select("*").ilike("email", f"%{domain}%").execute()
            return result.data[0] if result.data else None
        except Exception as e:
            logger.error(f"Failed to find customer by email domain: {e}")
            return None
    
    def find_customer_by_name(self, customer_name: str) -> Optional[Dict[str, Any]]:
        """Find customer by name using Supabase database with priority rules (optimized for active customers only)."""
        try:
            if not customer_name or customer_name.strip().upper() in ['UNKNOWN CUSTOMER', 'UNKNOWN', '']:
                return None
            
            # Clean the customer name for matching
            clean_name = customer_name.strip()
            logger.info(f"Looking up customer: '{clean_name}' in Supabase active customers database")
            
            # Try exact match first
            result = self.supabase.table("customers").select("*").ilike("customer_name", clean_name).execute()
            if result.data:
                logger.info(f"Found exact customer match for '{customer_name}': {result.data[0]['customer_name']} (ID: {result.data[0]['debtor_id']})")
                return result.data[0]
            
            # Try partial match with wildcards - get all matches for prioritization
            result = self.supabase.table("customers").select("*").ilike("customer_name", f"%{clean_name}%").execute()
            if result.data:
                # Apply priority rules for better matching
                best_match = self._prioritize_customer_matches(result.data, clean_name)
                if best_match:
                    logger.info(f"Found prioritized customer match for '{customer_name}': {best_match['customer_name']} (ID: {best_match['debtor_id']})")
                    return best_match
            
            # Try matching individual words (for cases like "Woolworths Group Limited" vs "Woolworths")
            words = clean_name.split()
            if len(words) > 1:
                for word in words:
                    if len(word) > 3:  # Only try meaningful words
                        result = self.supabase.table("customers").select("*").ilike("customer_name", f"%{word}%").execute()
                        if result.data:
                            # Apply priority rules here too
                            best_match = self._prioritize_customer_matches(result.data, word)
                            if best_match:
                                logger.info(f"Found word-based customer match for '{customer_name}' using word '{word}': {best_match['customer_name']} (ID: {best_match['debtor_id']})")
                                return best_match
            
            logger.warning(f"No customer found for name: '{customer_name}' in active customers database")
            return None
            
        except Exception as e:
            logger.error(f"Failed to find customer by name '{customer_name}': {e}")
            return None
    
    def _prioritize_customer_matches(self, matches: List[Dict[str, Any]], search_term: str) -> Optional[Dict[str, Any]]:
        """Prioritize customer matches based on business rules."""
        if not matches:
            return None
        
        if len(matches) == 1:
            return matches[0]
        
        search_lower = search_term.lower()
        
        # Priority rules for specific customers
        if 'woolworth' in search_lower:
            # For Woolworths, prioritize WOOLWORTHS LIMITED over ALH GROUP variants
            priority_order = [
                'WOOLWORTHS LIMITED',           # Main entity - highest priority
                'WOOLWORTHS GROUP',             # Group entities
                'ALH GROUP'                     # ALH entities - lower priority
            ]
            
            for priority_name in priority_order:
                for match in matches:
                    if priority_name in match['customer_name'].upper():
                        logger.info(f"Applied Woolworths priority rule: selected '{match['customer_name']}' over other matches")
                        return match
        
        # Default: return the first match (original behavior)
        return matches[0]
    
    def get_all_customers(self) -> List[Dict[str, Any]]:
        """Get all customers for debugging/reference."""
        try:
            result = self.supabase.table("customers").select("debtor_id, customer_name").order("customer_name").execute()
            return result.data or []
        except Exception as e:
            logger.error(f"Failed to get all customers: {e}")
            return []
    
    def find_customer_by_name_direct(self, customer_name: str) -> Optional[Dict[str, Any]]:
        """
        Find customer by name using direct database search.
        This method assumes the customers table only contains active customers (synced from MYOB).
        """
        try:
            if not customer_name or customer_name.strip().upper() in ['UNKNOWN CUSTOMER', 'UNKNOWN', '']:
                return None
            
            # Clean the customer name for matching
            clean_name = customer_name.strip()
            
            # Try exact match first
            result = self.supabase.table("customers").select("*").ilike("customer_name", clean_name).execute()
            if result.data:
                logger.info(f"Found exact customer match for '{customer_name}': {result.data[0]['customer_name']} (ID: {result.data[0]['debtor_id']})")
                return result.data[0]
            
            # Try partial match with wildcards - get all matches for prioritization
            result = self.supabase.table("customers").select("*").ilike("customer_name", f"%{clean_name}%").execute()
            if result.data:
                # Apply priority rules for better matching
                best_match = self._prioritize_customer_matches(result.data, clean_name)
                if best_match:
                    logger.info(f"Found prioritized customer match for '{customer_name}': {best_match['customer_name']} (ID: {best_match['debtor_id']})")
                    return best_match
            
            # Try matching individual words (for cases like "Woolworths Group Limited" vs "Woolworths")
            words = clean_name.split()
            if len(words) > 1:
                for word in words:
                    if len(word) > 3:  # Only try meaningful words
                        result = self.supabase.table("customers").select("*").ilike("customer_name", f"%{word}%").execute()
                        if result.data:
                            # Apply priority rules here too
                            best_match = self._prioritize_customer_matches(result.data, word)
                            if best_match:
                                logger.info(f"Found word-based customer match for '{customer_name}' using word '{word}': {best_match['customer_name']} (ID: {best_match['debtor_id']})")
                                return best_match
            
            logger.warning(f"No customer found for name: '{customer_name}'")
            return None
            
        except Exception as e:
            logger.error(f"Failed to find customer by name '{customer_name}': {e}")
            return None
    
    # =============================================================================
    # COMPLETE WORKFLOW OPERATIONS
    # =============================================================================
    
    def save_processed_order(self, processed_order: ProcessedOrder) -> bool:
        """Save a complete processed order with all related data."""
        try:
            # 1. Save email if not already saved
            email_uuid = self.save_email(processed_order.email_data)
            if not email_uuid:
                return False
            
            # 2. Save email categorization if extracted data exists
            if processed_order.extracted_data:
                categorization = EmailCategorization(
                    email_id=email_uuid,
                    category="order",
                    subcategory="extracted_order",
                    confidence_score=processed_order.email_data.confidence_score,
                    extracted_data=processed_order.extracted_data.dict(),
                    model_used="llm_extraction"
                )
                categorization_id = self.save_email_categorization(categorization)
                processed_order.categorization_id = categorization_id
            
            # 3. Save ERP payload if MYOB data exists
            if processed_order.myob_payload and processed_order.extracted_data:
                erp_payload = ERPPayload(
                    email_id=email_uuid,
                    debtor_id=processed_order.extracted_data.customer_details.debtor_id,
                    payload_type="order",
                    payload_data=processed_order.myob_payload,
                    customer_order_number=processed_order.extracted_data.customer_details.customer_order_number,
                    status="draft"
                )
                payload_id = self.save_erp_payload(erp_payload)
                processed_order.erp_payload_id = payload_id
                
                # 4. Save order line items
                if processed_order.extracted_data.order_lines and payload_id:
                    line_items = []
                    for i, order_line in enumerate(processed_order.extracted_data.order_lines):
                        line_item = OrderLineItem(
                            email_id=email_uuid,
                            erp_payload_id=payload_id,
                            line_number=i + 1,
                            stock_code=order_line.stockcode,
                            quantity=order_line.orderquantity,
                            description=f"Stock code: {order_line.stockcode}"
                        )
                        line_items.append(line_item)
                    
                    self.save_order_line_items(line_items)
                    processed_order.order_line_items = line_items
            
            # 5. Log successful processing
            self.log_email_event(
                email_id=email_uuid,
                debtor_id=processed_order.extracted_data.customer_details.debtor_id if processed_order.extracted_data else None,
                event_type="processing_complete",
                event_data={
                    "markdown_file": processed_order.markdown_filepath,
                    "myob_file": processed_order.myob_filepath,
                    "has_extracted_data": processed_order.extracted_data is not None,
                    "has_myob_payload": processed_order.myob_payload is not None
                }
            )
            
            logger.info(f"Successfully saved complete processed order for email {email_uuid}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to save processed order: {e}")
            return False
    
    def log_email_event(self, email_id: str, event_type: str, event_data: Dict[str, Any], 
                       debtor_id: Optional[int] = None, agent_name: Optional[str] = None) -> bool:
        """Log an email processing event."""
        try:
            # Validate debtor_id if provided
            if debtor_id is not None and not self.validate_debtor_id(debtor_id):
                logger.warning(f"Skipping email event log: debtor_id {debtor_id} not found in customers table")
                # Continue without debtor_id rather than failing completely
                debtor_id = None
            
            record = {
                "email_id": email_id,
                "debtor_id": debtor_id,
                "event_type": event_type,
                "event_data": event_data,
                "agent_name": agent_name or "email_processor",
                "status": "completed"
            }
            
            result = self.supabase.table("email_events").insert(record).execute()
            return bool(result.data)
            
        except Exception as e:
            logger.error(f"Failed to log email event: {e}")
            return False
    
    # =============================================================================
    # QUERY OPERATIONS
    # =============================================================================
    
    def get_recent_emails(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Get recent emails from database."""
        try:
            result = self.supabase.table("emails").select("*").order("created_at", desc=True).limit(limit).execute()
            return result.data or []
        except Exception as e:
            logger.error(f"Failed to get recent emails: {e}")
            return []
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """Get processing statistics."""
        try:
            # Get email counts by category
            emails_result = self.supabase.table("emails").select("category", count="exact").execute()
            
            # Get ERP payload counts by status
            payloads_result = self.supabase.table("erp_payloads").select("status", count="exact").execute()
            
            return {
                "total_emails": emails_result.count or 0,
                "total_payloads": payloads_result.count or 0,
                "emails_by_category": {},  # Could be enhanced with grouping
                "payloads_by_status": {}   # Could be enhanced with grouping
            }
            
        except Exception as e:
            logger.error(f"Failed to get processing stats: {e}")
            return {"total_emails": 0, "total_payloads": 0}
