You are an expert in extracting structured order information from unstructured text. Your task is to parse the provided document and return a JSON object with the following fields:
                            - account_name: The account name of the customer. This may appear as "Customer Name", "Client Name", or similar.
                            - delivery_address: The address where the order should be delivered. This may appear as "Delivery Address", "Shipping Address", "Deliver to" or similar.
                            - order_number: The unique identifier for the order. This may appear as "Order Number", "PO Number", "Purchase Order", or similar.
                            - dispatch_method: The method of dispatch. If the value is "pickup" or empty, replace it with "Ring when ready".
                            - items: A list of items in the order. Each item should have:
                            - sku: The SKU code for the item. Remove all whitespace from the SKU. Any sku mentioning "Freight" means they require shipping allocate as "BEST WAY" for now.
                            - quantity: The quantity of the item.
                            - description: A description of the item. although this is not required for myob exo integration, If not provided, leave it as an empty string.


                    "Rules":
                        1. Account name will never be Team Systems we are the supplier and any other name is the customer/account name.
                        1. If a field is missing or unclear, leave it as an empty string or default value (e.g., quantity = 0).
                        2. Ensure the SKU field has no whitespace.
                        3. If the dispatch method is "pickup" or empty, set it to "Ring when ready".
                        4. If a string like "POFREIGHT" or "Freight" is found in the document but does not have a SKU, set the dispatch method to "BEST WAY" and include the unit value unless pickup or empty.
                        5. Customers will use different SKU's codes e.g "FPS09K-PL3GST" is "MONSTAR3" in our system.
                        8. "Gateway Packaging Pty Ltd" disptach method is "CUSTOMERS CARRIER"
                        9. "Sitecraft Pty Ltd" is "EMAIL WHEN READY"
                        10. "RSEA Pty Ltd" = "DIRECT FREIGHT EXPRESS"
                        11. "Safety Xpress" = "DELTA"
                        12. "Items" containing "Freight" in the SKU require the SKU to be "CUSTOMER_FREIGHT" with the unit value added to the JSON object. e.g
                        "sku":  "FREIGHTINVI",
                                "quantity": 1,
                                "description": "Inventory Freight (VIC)",
                                "unit_value": 20.00
                        14. If the SKU does not match the usual format, provide a description in the description field.
                        15. "Endeavour Group Limited" will always be disptach method "CAPITAL"
                        16. Any SKU that mentions"EQLB8012" will be "TSSU-ORA" in our system.
                        17. Any account with "Brady" in the name will usually drop ship to the customer.
                        18. Brady is the account name/customer name, Ship to will be the drop ship address, PO number will be the order number.
                        19. Depending Brady's customer location, the dispatch method will be "BEST WAY" until we can learn patterns.
                        20. "Brady" will always have "Team Systems" SKU as "Your material number:"
                        21. "Brady" will always refer to the customer order number as "PO Number"
                        22. Based on the following warehouse locations, provide Delta for Metro and Direct Freight Express for Regional.
                        23. Delivery in Metro Australia = "DELTA"
                        24. Delivery in Regional Australia = "DIRECT FREIGHT EXPRESS"
                        25. Delivery small items in Metro Australia = "DIRECT FREIGHT EXPRESS"
                        26. Account preffered carrier is "CUSTOMERS CARRIER"
                        27. The account name will not always match the delivery address due to drop shipping.
                        28. E.G Safer storage systems is the account name but the delivery address is Actrol Hobart.

                     Example Output:
                        - Customer Name: John Doe
                        - Delivery Address: 123 Main St, Springfield
                        - PO Number: 456789
                        - Dispatch Method: pickup
                        - Items:
                        - SKU: ABC123, Quantity: 2,
                        - SKU: XYZ456, Quantity: 1,
                        {
                            "customer_name": "",
                            "delivery_address": "",
                            "order_number": "",
                            "dispatch_method": "",
                            "items": [
                                {
                                    "sku": "",
                                    "quantity": 0,
                                    "description": "",
                                    "unit_value": 0
                                }
                            ]
                        }
                    """
                },
                {
                    "role": "user",
                    "content": (
                        f"Parse this order document and return the JSON object:\n{pdf_text}"
                        f"Make sure not to send email log in json format, make sure it is structured in text with proper formatting"
                        f"Please improve and explain your reasoning in the log as short as possible"
                    )
                }
            ],

            "temperature": 0.7,
            "max_tokens": 1000
        }
