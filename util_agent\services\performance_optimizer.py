"""
Performance optimization service for TeamsysV0.1.
"""
import asyncio
import logging
import time
import functools
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass
from collections import defaultdict, deque

logger = logging.getLogger(__name__)


@dataclass
class PerformanceMetric:
    """Performance metric data."""
    operation: str
    duration_ms: float
    timestamp: datetime
    success: bool
    details: Optional[Dict[str, Any]] = None


class PerformanceCache:
    """Simple in-memory cache for frequently accessed data."""
    
    def __init__(self, max_size: int = 1000, ttl_seconds: int = 300):
        self.cache = {}
        self.access_times = {}
        self.max_size = max_size
        self.ttl_seconds = ttl_seconds
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        if key not in self.cache:
            return None
        
        # Check TTL
        if time.time() - self.access_times[key] > self.ttl_seconds:
            self.delete(key)
            return None
        
        # Update access time
        self.access_times[key] = time.time()
        return self.cache[key]
    
    def set(self, key: str, value: Any) -> None:
        """Set value in cache."""
        # Evict oldest if at capacity
        if len(self.cache) >= self.max_size:
            oldest_key = min(self.access_times.keys(), key=lambda k: self.access_times[k])
            self.delete(oldest_key)
        
        self.cache[key] = value
        self.access_times[key] = time.time()
    
    def delete(self, key: str) -> None:
        """Delete key from cache."""
        self.cache.pop(key, None)
        self.access_times.pop(key, None)
    
    def clear(self) -> None:
        """Clear all cache entries."""
        self.cache.clear()
        self.access_times.clear()
    
    def stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        hit_count = getattr(self, '_hit_count', 0)
        total_requests = getattr(self, '_total_requests', 0)
        hit_rate = hit_count / total_requests if total_requests > 0 else 0.0
        
        return {
            "size": len(self.cache),
            "max_size": self.max_size,
            "ttl_seconds": self.ttl_seconds,
            "hit_rate": hit_rate
        }


class PerformanceOptimizer:
    """Performance optimization and monitoring service."""
    
    def __init__(self):
        self.metrics: deque = deque(maxlen=1000)  # Keep last 1000 metrics
        self.operation_stats = defaultdict(list)
        self.cache = PerformanceCache()
        self.slow_query_threshold_ms = 1000.0
        self.batch_size_recommendations = {}
        
    def performance_monitor(self, operation_name: str):
        """Decorator to monitor function performance."""
        def decorator(func: Callable):
            @functools.wraps(func)
            async def async_wrapper(*args, **kwargs):
                start_time = time.time()
                success = True
                error = None
                
                try:
                    if asyncio.iscoroutinefunction(func):
                        result = await func(*args, **kwargs)
                    else:
                        result = func(*args, **kwargs)
                    return result
                except Exception as e:
                    success = False
                    error = str(e)
                    raise
                finally:
                    duration_ms = (time.time() - start_time) * 1000
                    
                    metric = PerformanceMetric(
                        operation=operation_name,
                        duration_ms=duration_ms,
                        timestamp=datetime.now(),
                        success=success,
                        details={"error": error} if error else None
                    )
                    
                    self.record_metric(metric)
            
            @functools.wraps(func)
            def sync_wrapper(*args, **kwargs):
                start_time = time.time()
                success = True
                error = None
                
                try:
                    result = func(*args, **kwargs)
                    return result
                except Exception as e:
                    success = False
                    error = str(e)
                    raise
                finally:
                    duration_ms = (time.time() - start_time) * 1000
                    
                    metric = PerformanceMetric(
                        operation=operation_name,
                        duration_ms=duration_ms,
                        timestamp=datetime.now(),
                        success=success,
                        details={"error": error} if error else None
                    )
                    
                    self.record_metric(metric)
            
            return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
        return decorator
    
    def record_metric(self, metric: PerformanceMetric) -> None:
        """Record a performance metric."""
        self.metrics.append(metric)
        self.operation_stats[metric.operation].append(metric.duration_ms)
        
        # Keep only recent stats per operation
        if len(self.operation_stats[metric.operation]) > 100:
            self.operation_stats[metric.operation] = self.operation_stats[metric.operation][-100:]
        
        # Log slow operations
        if metric.duration_ms > self.slow_query_threshold_ms:
            logger.warning(f"Slow operation detected: {metric.operation} took {metric.duration_ms:.0f}ms")
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary statistics."""
        if not self.metrics:
            return {"status": "no_data"}
        
        # Overall stats
        total_operations = len(self.metrics)
        successful_operations = sum(1 for m in self.metrics if m.success)
        success_rate = successful_operations / total_operations if total_operations > 0 else 0
        
        # Recent performance (last 10 minutes)
        recent_cutoff = datetime.now() - timedelta(minutes=10)
        recent_metrics = [m for m in self.metrics if m.timestamp > recent_cutoff]
        
        # Operation-specific stats
        operation_summaries = {}
        for operation, durations in self.operation_stats.items():
            if durations:
                operation_summaries[operation] = {
                    "count": len(durations),
                    "avg_ms": sum(durations) / len(durations),
                    "min_ms": min(durations),
                    "max_ms": max(durations),
                    "p95_ms": self._percentile(durations, 95),
                    "slow_queries": sum(1 for d in durations if d > self.slow_query_threshold_ms)
                }
        
        # Identify bottlenecks
        bottlenecks = self._identify_bottlenecks()
        
        return {
            "status": "healthy" if success_rate > 0.95 else "degraded",
            "total_operations": total_operations,
            "success_rate": round(success_rate, 3),
            "recent_operations": len(recent_metrics),
            "operation_summaries": operation_summaries,
            "bottlenecks": bottlenecks,
            "cache_stats": self.cache.stats(),
            "recommendations": self._get_performance_recommendations()
        }
    
    def _percentile(self, data: List[float], percentile: int) -> float:
        """Calculate percentile of data."""
        if not data:
            return 0.0
        
        sorted_data = sorted(data)
        index = int((percentile / 100) * len(sorted_data))
        return sorted_data[min(index, len(sorted_data) - 1)]
    
    def _identify_bottlenecks(self) -> List[Dict[str, Any]]:
        """Identify performance bottlenecks."""
        bottlenecks = []
        
        for operation, durations in self.operation_stats.items():
            if not durations:
                continue
            
            avg_duration = sum(durations) / len(durations)
            slow_queries = sum(1 for d in durations if d > self.slow_query_threshold_ms)
            
            # High average duration
            if avg_duration > self.slow_query_threshold_ms:
                bottlenecks.append({
                    "type": "slow_operation",
                    "operation": operation,
                    "avg_duration_ms": round(avg_duration, 2),
                    "severity": "high" if avg_duration > 5000 else "medium"
                })
            
            # High percentage of slow queries
            slow_percentage = slow_queries / len(durations)
            if slow_percentage > 0.1:  # More than 10% slow
                bottlenecks.append({
                    "type": "frequent_slow_queries",
                    "operation": operation,
                    "slow_percentage": round(slow_percentage * 100, 1),
                    "severity": "medium"
                })
        
        return bottlenecks
    
    def _get_performance_recommendations(self) -> List[Dict[str, str]]:
        """Get performance optimization recommendations."""
        recommendations = []
        
        # Cache hit rate recommendations
        cache_stats = self.cache.stats()
        if cache_stats["hit_rate"] < 0.5:
            recommendations.append({
                "type": "caching",
                "message": f"Low cache hit rate ({cache_stats['hit_rate']:.1%}). Consider increasing cache TTL or size.",
                "priority": "medium"
            })
        
        # Batch size recommendations
        for operation, durations in self.operation_stats.items():
            if "batch" in operation.lower() and durations:
                avg_duration = sum(durations) / len(durations)
                if avg_duration > 2000:  # 2 seconds
                    recommendations.append({
                        "type": "batch_optimization",
                        "message": f"Consider reducing batch size for {operation} (avg: {avg_duration:.0f}ms)",
                        "priority": "medium"
                    })
        
        # Memory usage recommendations
        if len(self.metrics) > 800:  # Near capacity
            recommendations.append({
                "type": "memory_management",
                "message": "Performance metrics buffer is near capacity. Consider increasing retention or implementing archiving.",
                "priority": "low"
            })
        
        return recommendations
    
    async def optimize_batch_processing(self, items: List[Any], 
                                      process_func: Callable, 
                                      initial_batch_size: int = 10,
                                      max_batch_size: int = 100) -> List[Any]:
        """Dynamically optimize batch processing based on performance."""
        if not items:
            return []
        
        results = []
        current_batch_size = initial_batch_size
        
        # Process in adaptive batches
        for i in range(0, len(items), current_batch_size):
            batch = items[i:i + current_batch_size]
            
            start_time = time.time()
            
            try:
                if asyncio.iscoroutinefunction(process_func):
                    batch_results = await process_func(batch)
                else:
                    batch_results = process_func(batch)
                
                results.extend(batch_results)
                
                # Measure performance and adjust batch size
                duration_ms = (time.time() - start_time) * 1000
                
                # Optimize batch size based on performance
                if duration_ms < 500:  # Very fast, increase batch size
                    current_batch_size = min(current_batch_size * 2, max_batch_size)
                elif duration_ms > 2000:  # Too slow, decrease batch size
                    current_batch_size = max(current_batch_size // 2, 1)
                
                logger.debug(f"Batch processed: {len(batch)} items in {duration_ms:.0f}ms, next batch size: {current_batch_size}")
                
            except Exception as e:
                logger.error(f"Batch processing failed: {e}")
                # Reduce batch size on error
                current_batch_size = max(current_batch_size // 2, 1)
                raise
        
        return results
    
    def get_cached_customer_data(self, customer_id: str) -> Optional[Dict[str, Any]]:
        """Get cached customer data with performance tracking."""
        cache_key = f"customer_{customer_id}"
        
        # Track cache access
        start_time = time.time()
        result = self.cache.get(cache_key)
        duration_ms = (time.time() - start_time) * 1000
        
        # Record cache performance
        metric = PerformanceMetric(
            operation="cache_get_customer",
            duration_ms=duration_ms,
            timestamp=datetime.now(),
            success=result is not None,
            details={"cache_hit": result is not None}
        )
        self.record_metric(metric)
        
        return result
    
    def cache_customer_data(self, customer_id: str, data: Dict[str, Any]) -> None:
        """Cache customer data with performance tracking."""
        cache_key = f"customer_{customer_id}"
        
        start_time = time.time()
        self.cache.set(cache_key, data)
        duration_ms = (time.time() - start_time) * 1000
        
        # Record cache performance
        metric = PerformanceMetric(
            operation="cache_set_customer",
            duration_ms=duration_ms,
            timestamp=datetime.now(),
            success=True
        )
        self.record_metric(metric)
    
    def clear_performance_data(self) -> None:
        """Clear all performance data."""
        self.metrics.clear()
        self.operation_stats.clear()
        self.cache.clear()
        logger.info("Performance data cleared")
    
    def export_performance_report(self) -> Dict[str, Any]:
        """Export detailed performance report."""
        return {
            "report_timestamp": datetime.now().isoformat(),
            "summary": self.get_performance_summary(),
            "recent_metrics": [
                {
                    "operation": m.operation,
                    "duration_ms": m.duration_ms,
                    "timestamp": m.timestamp.isoformat(),
                    "success": m.success
                }
                for m in list(self.metrics)[-50:]  # Last 50 metrics
            ],
            "slow_operations": [
                {
                    "operation": m.operation,
                    "duration_ms": m.duration_ms,
                    "timestamp": m.timestamp.isoformat()
                }
                for m in self.metrics 
                if m.duration_ms > self.slow_query_threshold_ms
            ][-20:]  # Last 20 slow operations
        }