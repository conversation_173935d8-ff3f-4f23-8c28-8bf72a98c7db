{"email_metadata": {"subject": "PURCHASE ORDER - 1890990 for TEASYS from IMPORT WAREHOUSE VIC", "from": "<EMAIL>", "date": null, "body": "Please find attached PURCHASE ORDER - 1890990 for TEASYS from IMPORT WAREHOUSE VIC"}, "order_information": {"purchase_order_number": "1890990", "supplier": "Team Systems", "customer": "RSEA Pty Ltd", "order_date": "2025-07-22", "shipping_address": "Door 3, Building 2 - 207 Sunshine Road, Tottenham, VIC 3012", "shipping_method": "Direct Delivery", "payment_terms": null, "total_amount": "1341.52", "items": [{"quantity": 3, "item_number": "345081", "description": "TEASYS MONLM3 - LITTLE MONSTAR 3 Step Ladder", "price": "228.65"}, {"quantity": 1, "item_number": "684122", "description": "Team Systems 4 Step + Platform (5 Step) Ladder", "price": "431.85"}, {"quantity": 1, "item_number": "645048", "description": "Team Systems Castor Wheel Kit For PROP 11/8 Ladder PROPWK", "price": "101.77"}]}, "important_details": {"special_instructions": ["If you cannot fulfil this order, please contact the <NAME_EMAIL>", "Invoice must quote the correct Purchase Order Number, 1890990, otherwise payment for this delivery may be delayed", "DO NOT SEND INVOICE WITH GOODS", "SEND TO: <EMAIL>"], "attachments": [{"name": "PURCHASE ORDER-1890990-TEASYS.pdf", "relevance": "Purchase order details"}]}, "action_items": ["Fulfill the order as per the purchase order details", "Ensure the invoice quotes the correct Purchase Order Number: 1890990", "Do not send the invoice with the goods", "Send the <NAME_EMAIL>"]}