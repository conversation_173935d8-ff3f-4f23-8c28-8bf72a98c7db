"""
System monitoring and health check service for TeamsysV0.1.
"""
import asyncio
import logging
import psutil
import time
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict

from gmail_service import GmailService
from llm_service.main import LLMService
from supabase_database_service import SupabaseService

logger = logging.getLogger(__name__)


@dataclass
class HealthCheckResult:
    """Health check result for a service."""
    service_name: str
    healthy: bool
    response_time_ms: float
    error_message: Optional[str] = None
    details: Optional[Dict[str, Any]] = None
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


@dataclass
class SystemMetrics:
    """System performance metrics."""
    cpu_percent: float
    memory_percent: float
    disk_percent: float
    network_io: Dict[str, int]
    process_count: int
    uptime_seconds: float
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


class SystemMonitor:
    """System monitoring and health check service."""
    
    def __init__(self):
        self.start_time = time.time()
        self.health_history: List[HealthCheckResult] = []
        self.metrics_history: List[SystemMetrics] = []
        self.alert_thresholds = {
            'cpu_percent': 80.0,
            'memory_percent': 90.0,  # Increased from 85% to 90% for this system
            'disk_percent': 90.0,
            'response_time_ms': 15000.0  # Increased from 5s to 15s for LLM services
        }
        
    async def comprehensive_health_check(self) -> Dict[str, HealthCheckResult]:
        """Perform comprehensive health check of all services."""
        logger.info("Starting comprehensive health check")
        
        health_checks = {
            'gmail_service': self._check_gmail_service(),
            'llm_service': self._check_llm_service(),
            'database_service': self._check_database_service(),
            'system_resources': self._check_system_resources(),
            'disk_space': self._check_disk_space()
        }
        
        # Execute all health checks concurrently
        results = {}
        for service_name, check_coro in health_checks.items():
            try:
                result = await check_coro
                results[service_name] = result
                self.health_history.append(result)
            except Exception as e:
                logger.error(f"Health check failed for {service_name}: {e}")
                results[service_name] = HealthCheckResult(
                    service_name=service_name,
                    healthy=False,
                    response_time_ms=0.0,
                    error_message=str(e)
                )
        
        # Trim history to last 100 entries
        self.health_history = self.health_history[-100:]
        
        logger.info(f"Health check completed: {sum(1 for r in results.values() if r.healthy)}/{len(results)} services healthy")
        return results
    
    async def _check_gmail_service(self) -> HealthCheckResult:
        """Check Gmail service health."""
        start_time = time.time()
        
        try:
            gmail_service = GmailService()
            
            if not gmail_service.service:
                return HealthCheckResult(
                    service_name="gmail_service",
                    healthy=False,
                    response_time_ms=0.0,
                    error_message="Gmail service not initialized"
                )
            
            # Test basic API call
            labels = gmail_service.service.users().labels().list(userId='me').execute()
            label_count = len(labels.get('labels', []))
            
            response_time = (time.time() - start_time) * 1000
            
            return HealthCheckResult(
                service_name="gmail_service",
                healthy=True,
                response_time_ms=response_time,
                details={"label_count": label_count}
            )
            
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            return HealthCheckResult(
                service_name="gmail_service",
                healthy=False,
                response_time_ms=response_time,
                error_message=str(e)
            )
    
    async def _check_llm_service(self) -> HealthCheckResult:
        """Check LLM service health."""
        start_time = time.time()
        
        try:
            llm_service = LLMService()
            
            # Test simple LLM call using flexible content generation
            test_prompt = "Respond with 'OK' if you can process this message."
            response = await llm_service.mistral_service.generate_content_flexible(test_prompt)
            
            response_time = (time.time() - start_time) * 1000
            
            healthy = "ok" in response.lower()
            
            return HealthCheckResult(
                service_name="llm_service",
                healthy=healthy,
                response_time_ms=response_time,
                details={"test_response": response[:50]}
            )
            
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            return HealthCheckResult(
                service_name="llm_service",
                healthy=False,
                response_time_ms=response_time,
                error_message=str(e)
            )
    
    async def _check_database_service(self) -> HealthCheckResult:
        """Check database service health."""
        start_time = time.time()
        
        try:
            db_service = SupabaseService()
            
            # Test database connection with simple query on existing table
            result = db_service.supabase.table('customers').select('debtor_id').limit(1).execute()
            
            response_time = (time.time() - start_time) * 1000
            
            # Check if we got data back
            customer_count = len(result.data) if result.data else 0
            
            return HealthCheckResult(
                service_name="database_service",
                healthy=True,
                response_time_ms=response_time,
                details={
                    "connection_test": "success",
                    "customer_count": customer_count
                }
            )
            
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            return HealthCheckResult(
                service_name="database_service",
                healthy=False,
                response_time_ms=response_time,
                error_message=str(e)
            )
    
    async def _check_system_resources(self) -> HealthCheckResult:
        """Check system resource usage."""
        start_time = time.time()
        
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            
            # Check if resources are within acceptable limits
            healthy = (
                cpu_percent < self.alert_thresholds['cpu_percent'] and
                memory.percent < self.alert_thresholds['memory_percent']
            )
            
            response_time = (time.time() - start_time) * 1000
            
            return HealthCheckResult(
                service_name="system_resources",
                healthy=healthy,
                response_time_ms=response_time,
                details={
                    "cpu_percent": cpu_percent,
                    "memory_percent": memory.percent,
                    "memory_available_gb": round(memory.available / (1024**3), 2)
                }
            )
            
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            return HealthCheckResult(
                service_name="system_resources",
                healthy=False,
                response_time_ms=response_time,
                error_message=str(e)
            )
    
    async def _check_disk_space(self) -> HealthCheckResult:
        """Check disk space availability."""
        start_time = time.time()
        
        try:
            disk_usage = psutil.disk_usage('.')
            disk_percent = (disk_usage.used / disk_usage.total) * 100
            
            healthy = disk_percent < self.alert_thresholds['disk_percent']
            
            response_time = (time.time() - start_time) * 1000
            
            return HealthCheckResult(
                service_name="disk_space",
                healthy=healthy,
                response_time_ms=response_time,
                details={
                    "disk_percent": round(disk_percent, 2),
                    "free_gb": round(disk_usage.free / (1024**3), 2),
                    "total_gb": round(disk_usage.total / (1024**3), 2)
                }
            )
            
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            return HealthCheckResult(
                service_name="disk_space",
                healthy=False,
                response_time_ms=response_time,
                error_message=str(e)
            )
    
    def get_system_metrics(self) -> SystemMetrics:
        """Get current system performance metrics."""
        try:
            # CPU and memory
            cpu_percent = psutil.cpu_percent(interval=0.1)
            memory = psutil.virtual_memory()
            
            # Disk usage
            disk_usage = psutil.disk_usage('.')
            disk_percent = (disk_usage.used / disk_usage.total) * 100
            
            # Network I/O
            network_io = psutil.net_io_counters()
            network_stats = {
                'bytes_sent': network_io.bytes_sent,
                'bytes_recv': network_io.bytes_recv,
                'packets_sent': network_io.packets_sent,
                'packets_recv': network_io.packets_recv
            }
            
            # Process count
            process_count = len(psutil.pids())
            
            # System uptime
            uptime_seconds = time.time() - self.start_time
            
            metrics = SystemMetrics(
                cpu_percent=cpu_percent,
                memory_percent=memory.percent,
                disk_percent=disk_percent,
                network_io=network_stats,
                process_count=process_count,
                uptime_seconds=uptime_seconds
            )
            
            # Store in history
            self.metrics_history.append(metrics)
            self.metrics_history = self.metrics_history[-100:]  # Keep last 100
            
            return metrics
            
        except Exception as e:
            logger.error(f"Failed to get system metrics: {e}")
            return SystemMetrics(
                cpu_percent=0.0,
                memory_percent=0.0,
                disk_percent=0.0,
                network_io={},
                process_count=0,
                uptime_seconds=0.0
            )
    
    def get_health_summary(self) -> Dict[str, Any]:
        """Get summary of recent health checks."""
        if not self.health_history:
            return {"status": "no_data", "message": "No health check data available"}
        
        # Get latest results for each service
        latest_results = {}
        for result in reversed(self.health_history):
            if result.service_name not in latest_results:
                latest_results[result.service_name] = result
        
        # Calculate overall health
        healthy_services = sum(1 for r in latest_results.values() if r.healthy)
        total_services = len(latest_results)
        overall_healthy = healthy_services == total_services
        
        # Get average response times
        avg_response_times = {}
        for service_name, result in latest_results.items():
            avg_response_times[service_name] = result.response_time_ms
        
        return {
            "status": "healthy" if overall_healthy else "degraded",
            "healthy_services": healthy_services,
            "total_services": total_services,
            "last_check": max(r.timestamp for r in latest_results.values()),
            "service_status": {name: r.healthy for name, r in latest_results.items()},
            "response_times": avg_response_times,
            "alerts": self._get_active_alerts(latest_results)
        }
    
    def _get_active_alerts(self, latest_results: Dict[str, HealthCheckResult]) -> List[Dict[str, str]]:
        """Get list of active alerts based on health check results."""
        alerts = []
        
        for service_name, result in latest_results.items():
            if not result.healthy:
                alerts.append({
                    "type": "service_down",
                    "service": service_name,
                    "message": f"{service_name} is unhealthy: {result.error_message or 'Unknown error'}",
                    "severity": "high"
                })
            
            elif result.response_time_ms > self.alert_thresholds['response_time_ms']:
                alerts.append({
                    "type": "slow_response",
                    "service": service_name,
                    "message": f"{service_name} response time is high: {result.response_time_ms:.0f}ms",
                    "severity": "medium"
                })
            
            # Check resource-specific alerts
            if service_name == "system_resources" and result.details:
                details = result.details
                
                if details.get('cpu_percent', 0) > self.alert_thresholds['cpu_percent']:
                    alerts.append({
                        "type": "high_cpu",
                        "service": "system",
                        "message": f"High CPU usage: {details['cpu_percent']:.1f}%",
                        "severity": "medium"
                    })
                
                if details.get('memory_percent', 0) > self.alert_thresholds['memory_percent']:
                    alerts.append({
                        "type": "high_memory",
                        "service": "system",
                        "message": f"High memory usage: {details['memory_percent']:.1f}%",
                        "severity": "medium"
                    })
            
            elif service_name == "disk_space" and result.details:
                details = result.details
                
                if details.get('disk_percent', 0) > self.alert_thresholds['disk_percent']:
                    alerts.append({
                        "type": "low_disk_space",
                        "service": "system",
                        "message": f"Low disk space: {details['disk_percent']:.1f}% used",
                        "severity": "high"
                    })
        
        return alerts
    
    def export_health_report(self) -> Dict[str, Any]:
        """Export comprehensive health report."""
        return {
            "report_timestamp": datetime.now().isoformat(),
            "system_uptime_hours": round((time.time() - self.start_time) / 3600, 2),
            "health_summary": self.get_health_summary(),
            "current_metrics": asdict(self.get_system_metrics()),
            "recent_health_checks": [asdict(r) for r in self.health_history[-10:]],
            "alert_thresholds": self.alert_thresholds
        }