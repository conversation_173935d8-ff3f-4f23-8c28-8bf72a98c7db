---
url: "https://developer.myob.com/api/exo/endpoints/salesorder/"
title: "Sales Order"
---

[![](https://developer.myob.com/media/cms_page_media/23/myob_logo_small.png)](http://www.myob.com/)

# Sales Order

Return information on a Sales Order.

**Date Released:** October 25th 2014 **Date Updated:** November 14th 2014

| URL | Supports |
| --- | --- |
| {URI}/salesorder/<br>{URI}/salesorder/search?q={query}<br>{URI}/salesorder/validate/{salesorderid}<br>{URI}/salesorder/{salesorderid}?overridelock=true<br>{URI}/salesorder/convertquotetoorder/ | [GET](https://developer.myob.com/api/exo/endpoints/salesorder/#GET) \| <br>[PUT](https://developer.myob.com/api/exo/endpoints/salesorder/#PUT) \| <br>[POST](https://developer.myob.com/api/exo/endpoints/salesorder/#POST)<br>[GET](https://developer.myob.com/api/exo/endpoints/salesorder/#GET) \| <br>[PUT](https://developer.myob.com/api/exo/endpoints/salesorder/#PUT) \| <br>[POST](https://developer.myob.com/api/exo/endpoints/salesorder/#POST)<br>[GET](https://developer.myob.com/api/exo/endpoints/salesorder/#GET) \| <br>[PUT](https://developer.myob.com/api/exo/endpoints/salesorder/#PUT) \| <br>[POST](https://developer.myob.com/api/exo/endpoints/salesorder/#POST)<br>[GET](https://developer.myob.com/api/exo/endpoints/salesorder/#GET) \| <br>[PUT](https://developer.myob.com/api/exo/endpoints/salesorder/#PUT) \| <br>[POST](https://developer.myob.com/api/exo/endpoints/salesorder/#POST)<br>[GET](https://developer.myob.com/api/exo/endpoints/salesorder/#GET) \| <br>[PUT](https://developer.myob.com/api/exo/endpoints/salesorder/#PUT) \| <br>[POST](https://developer.myob.com/api/exo/endpoints/salesorder/#POST) |
| {URI} is exo.api.myob.com when connecting to the cloud or the address of the machine hosting the API when connecting on premise. |

`search` returns sales orders that match the specified search string.

`validate` validates a sales order, ideally before performing a PUT or POST - see below.

`overridelock` overrides object locks (if any) on a sales order that needs to be updated.

`convertquotetoorder` converts the specified sales order quote to a full sales order. The endpoint is called with the Sales Order ID as the paylod, e.g.

```
{
"id": 12345
}
```

If successful, it returns a new sales order object. If the status of the specified sales order isn’t 3 (Quotation), the endpoint will return an error.

### Validating Sales Orders

The `salesorder/validate` endpoint validates a sales order, ideally before performing a PUT or POST. The minimum attributes required for a valid VALIDATE request are:

- Debtorid
- Status
- Stockcode - applies to order lines and if the stock item is not a BOM
- Bomcode - applies to order lines and if the item is a BOM

A VALIDATE request would be used in two scenarios:

- Updating a record - In such a case, it returns a list of errors, if any, that would prevent a successful PUT.
- Creating a new Sales Order - VALIDATE has been designed primarily for this use. It accepts a minimum set of attributes, i.e. debtor and status. The response returns all the other attributes, which would then be the payload for a POST. The response attributes adhere to the various user and computer profiles that apply to an order in EXO Business.

VALIDATE requests are also particularly useful when adding Bill of Materials items to the order. Passing in just the bomcode in the VALIDATE request would return the entire BOM set of header and components.

POST has more required fields than a VALIDATE. Developers are recommended to POST/PUT a response returned by VALIDATE. To verify what fields are required for a PUT and POST, refer to the [API schemas](https://developer.myob.com/api/exo/exo-api-overview/apischema/).

![](https://developer.myob.com/media/cms_page_media/473/validate.png)

The elements list below details information for Sales Order. To view the descriptions for the elements you can either hover any attribute to reveal details [or click here to show all details inline.](https://developer.myob.com/api/exo/endpoints/salesorder/#reveal)

#### Attribute Details

- debtorid integer
- Type: Required on PUT, POST. ACCNO from Salesord\_Hdr, defines the Debtor Account making the order. (Relates to DR\_ACCS ACCNO)
- lines array,null
- Type: array,null. Optional, but orders without lines don't have much purpose.
  - stockcode string
  - Type: string. Length defined by database schema, default 23.
  - isoriginatedfromtemplate boolean,null
  - Type: boolean. Optional.
  - orderquantity number
  - Type: number. Decimal Precision defined by profile QTYDECIMALS in Exo system.
  - discount number,null
  - Type: number. Optional. Decimal Precision defined by profile DISCOUNTDECIMALS in Exo system. Note that discounts may have additional restrictions on them in system.
  - unitprice number,null
  - Type: number. Optional. Decimal Precision defined by profile SELLPRDECIMALS in Exo system. Tax exclusive price. Failure to specify will return the default price on order validation.
  - ispriceoverridden boolean,null
  - Type: boolean. Optional. Specifies if the price has been overridden. Overridden prices are not recalculated if an order is edited in Exo Business.
  - locationid integer,null
  - Type: integer. Optional. Stock Location that the line should be supplied from. If not specified will be defaulted based on API User.
  - taxrateid integer,null
  - Type: integer. Optional. TaxrateID for the line. If unspecified will default based on Debtor/Stock Item hierarchy from Exo.
  - listprice number,null
  - Type: number. Optional. Defines the RRP of the item at time of ordering.
  - pricepolicyid integer,null
  - Type: integer. Optional. Should be 0 or refer to a price policy that this price is taken from.
  - linetype integer,enum,null
  - Type: integer. Optional. Linetypes can be (0 = Normal stockline,1 = BOM header line,2 = BOM component line,4 = Comment/Narrative line). Default is 0.
  - uninvoicedquantity number,calculated
  - Type: number. DO NOT SPECIFY. Always 0 for new Orders/Quotes. Do not change value as Supply via API is not enabled.
  - releasequantity number,calculated
  - Type: number. DO NOT SPECIFY. Always 0 for new Orders/Quotes. Do not change value as Supply via API is not enabled.
  - releasenowquantity number,calculated
  - Type: number. DO NOT SPECIFY. Always the full value of the line for new Orders/Quotes. Do not change value as Supply via API is not enabled.
  - lastreleasedquantity number,calculated
  - Type: number. DO NOT SPECIFY. Always 0 for new Orders/Quotes. Do not change value as Supply via API is not enabled.
  - pickedquantity number,calculated
  - Type: number. DO NOT SPECIFY. Always 0 for new Orders/Quotes. Do not change value as Supply via API is not enabled.
  - picknowquantity number,calculated
  - Type: number. DO NOT SPECIFY. Always 0 for new Orders/Quotes. Do not change value as Supply via API is not enabled.
  - lastpickedquantity number,calculated
  - Type: number. DO NOT SPECIFY. Always 0 for new Orders/Quotes. Do not change value as Supply via API is not enabled.
  - stocktype enum,calculated
  - Type: enum. DO NOT SPECIFY. Will be either PhysicalItem or Lookup Item. (Defines if Item has stock, retrieved from stock item record)
  - id integer,null
  - Type: integer. Required on PUT. Do not specify on POST, only on PUT. Database ID number for Order line. For POST either leave blank or specify a negative number. SOLINEID value from SALESORD\_LINES.
  - branchid integer,null
  - Type: integer. Optional. Branch the line's profit should be reported to. If not specified is inherited from Order branch or User.
  - taxratevalue number,null
  - Type: number. Optional. (Recommended DO NOT SPECIFY). The taxrate for the line. (Percentage). To correctly specify taxrates use taxrateid
  - istaxoverridden boolean,null
  - Type: boolean. Optional. Only specify if you have overridden the tax calculation so it no longer matches the database default. Note that if you overwrite the tax value for a line, this prevents Exo from recalculating prices on Order edits/processing.
  - taxoverridevalue number,null
  - Type: number. Optional. If istaxoverridden is true then must specify the exact tax amount for the line.
  - narrative string,null
  - Type: string. Optional. Additional narrative (nvarchar(Max)) for recording additional information.
  - backorderquantity number,null
  - Type: number. Optional. Specifies the backorder quantity for a line (which affects stock availability and supply processes in Exo Business.)
  - description string,null
  - Type: string. Optional. If not specified will be the standard item description from the stock item. String truncation errors can occur if you don't obey database schema. (Default schema length 100 on new databases)
  - batchcode string,null
  - Type: string. Optional. Specify a batchcode for batch tracking purposes, note batch tracking is not fully supported via the API. Schema bound with default length of 20 characters.
  - duedate date,null
  - Type: date. Optional, line specific duedate. Should be in format YYYY-MM-DD when set.
  - linetotal number,calculated
  - Type: number. DO NOT SPECIFY. Calculated based on unitprice \* orderquantity less discount. Tax Exclusive line-total.
  - invoicedquantity number,calculated
  - Type: number. DO NOT SPECIFY. Always 0 for new Orders/Quotes. Do not change value as Supply via API is not enabled.
  - invoicenowquantity number,calculated
  - Type: number. DO NOT SPECIFY. Always 0 for new Orders/Quotes. Do not change value as Supply via API is not enabled.
  - lastinvoicedquantity number,calculated
  - Type: number. DO NOT SPECIFY. Always 0 for new Orders/Quotes. Do not change value as Supply via API is not enabled.
  - suppliedquantity number,calculated
  - Type: number. DO NOT SPECIFY. Always 0 for new Orders/Quotes. Do not change value as Supply via API is not enabled.
  - supplynowquantity number,calculated
  - Type: number. DO NOT SPECIFY. Always the full value of the line for new Orders/Quotes. Do not change value as Supply via API is not enabled.
  - lastsuppliedquantity number,calculated
  - Type: number. DO NOT SPECIFY. Always 0 for new Orders/Quotes. Do not change value as Supply via API is not enabled.
  - restrictedline boolean, calculated
  - Type: boolean. DO NOT SPECIFY. Specifies whether the line has restricted stock on it.
  - rankorder integer,null
  - Type: integer. For determining line-position when printing.
  - extrafields array,null
  - Type: array. Optional. Defined in individual user databases against SALESORD\_LINES level. For assistance speak to database's implementation partner.
- taxrounding number,null
- Type: number. Defines the tax rounding override for the order.
- deliverycount integer,calculated
- Type: integer. DO NOT SPECIFY. Count of past delivery events.
- invoicecount integer,calculated
- Type: integer. DO NOT SPECIFY. Count of past invoice events.
- pickedcount integer,calculated
- Type: integer. DO NOT SPECIFY. Count of past picking events.
- releasecount integer,calculated
- Type: integer. DO NOT SPECIFY. Count of past release events.
- hasuninvoiced boolean,calculated
- Type: boolean. DO NOT SPECIFY. Whether order has uninvoiced lines.
- hasunpicked boolean,calculated
- Type: boolean. DO NOT SPECIFY. Whether order has unpicked lines.
- hasunreleased boolean,calculated
- Type: boolean. DO NOT SPECIFY. Whether order has unreleased lines.
- hasunsupplied boolean,calculated
- Type: boolean. DO NOT SPECIFY. Whether order has unsupplied lines.
- allowcustomerordernumber boolean
- Type: boolean. Related to order number validation.
- customerordernumber string,null
- Type: string. Optional. Linked to the CUSTORDERNO field (which has to be added as an Extra Field to be visible in the front end UI). Default schema definition is NVARCHAR(20)
- accountname string,null
- Type: string. Optional. Always retrieves the Accountname value from the Debtor account.
- istaxinclusive boolean,null
- Type: boolean, defaults false. (Tax Exclusive). Defines if pricing on the order is tax inclusive or tax exclusive.
- hasbackorders boolean,calculated
- Type: boolean. DO NOT SPECIFY. Whether order has backordered lines.
- branchid integer,null
- Type: integer. Default branch for the order. If unspecified will be drawn based on user profile. BRANCHNO field.
- defaultlocationid integer, null
- Type: integer. Default stock location for order. If unspecified will default based on user. DEFLOCNO field.
- narrative string,null
- Type: string. Optional. Additional narrative (nvarchar(Max)) for recording information at a Sales Order Header level.
- currencyid integer,null
- Type: integer. Optional. Defines the currency the order is in. Should always be the same as the related Debtor account. If not specified will default to Debtor account Currrrency. CURRENCYNO
- contactid integer,null
- Type: integer. Optional. Contact associated for the sales order. Note: Contacts are an optional feature controlled by profiles, and may not be visible to all systems depending on configuration.
- salespersonid integer,null
- Type: integer. Optional. Salesperson for the Order, is an integer specifying the staff member from the STAFF table.
- exchangerate number,null
- Type: number. Optional. Exchange rate order was received at. Must be 1 for currencyno 0.
- taxtotal number,calculated
- Type: number. DO NOT SPECIFY. The total tax value of the order.
- subtotal number,calculated
- Type: number. DO NOT SPECIFY. The subtotal of all lines without tax.
- reference string,null
- Type: string. Optional, reference field for order. Default definition NVARCHAR(20)
- instructions string,null
- Type: string. Optional, delivery instruction field for order. Default database definition NVARCHAR(255)
- createdate date,calculated
- Type: date. DO NOT SPECIFY. Timestamp of order being created.
- orderdate date,null
- Type: date. Optional, date of order. Should be in format YYYY-MM-DD when set. Defaults to today's date.
- duedate date,null
- Type: date. Optional, overall order duedate. Should be in format YYYY-MM-DD when set.
- finalisationdate date,null
- Type: date. Optional, date order was finalised. Should be in format YYYY-MM-DD when set. Should be left alone.
- activationdate datetime,null
- Type: date. Optional. Date order was activated. Full timestamp value. Defaults to current time. Returns string.
- deliveryaddress array,null
- Type: array. Delivery address for order, if unspecified will default to Debtor account delivery address.
  - line1 string,null
  - Type: string. Delivery address line 1. Display definition defined as part of Exo Profiles. Default length is NVARCHAR(30). If longer lengths are required speak to implementer.
  - line2 string,null
  - Type: string. Delivery address line 2. Display definition defined as part of Exo Profiles. Default length is NVARCHAR(30). If longer lengths are required speak to implementer.
  - line3 string,null
  - Type: string. Delivery address line 3. Display definition defined as part of Exo Profiles. Default length is NVARCHAR(30). If longer lengths are required speak to implementer.
  - line4 string,null
  - Type: string. Delivery address line 4. Display definition defined as part of Exo Profiles. Default length is NVARCHAR(30). If longer lengths are required speak to implementer.
  - line5 string,null
  - Type: string. Delivery address line 5. Display definition defined as part of Exo Profiles. Default length is NVARCHAR(30). If longer lengths are required speak to implementer. OPTIONAL FIELD - may not be visible to end users in all systems.
  - line6 string,null
  - Type: string. Delivery address line 6. Display definition defined as part of Exo Profiles. Default length is NVARCHAR(30). If longer lengths are required speak to implementer. OPTIONAL FIELD - may not be visible to end users in all systems.
- status integer,enum
- Type: integer. Required on POST. 0 = "Not Processed", 3 = "Quotation", 4 = "Standing Order", 5 - "Layby". Note that not all statuses are available if Extended Order types is off. (1 = Partially Processed, 2 = Fully Processed. These will be set by system users on processing orders.)
- statusdescription string calculated
- Type: string. DO NOT SPECIFY. Description of the Status integer.
- finalised integer,null
- Type: integer. Optional. Specifies the Finalisation status of order. 0 = Normal, 1 = Deleted, 2 = Forced Fully Processed, 3 = Lost Quote
- ordertotal number,calculated
- Type: number. DO NOT SPECIFY. Calculated total of the Order.
- localvalue number,calculated
- Type: number. DO NOT SPECIFY. The converted value of the order in the system denomination currency. (Ordertotal \* exchangerate)
- hasrestrictedline boolean,calculated
- Type: boolean. Specifies whether the order has restricted stock on it.
- lastupdated datetime,calculated
- Type: date. DO NOT SPECIFY. Date order was last edited. Full timestamp value. Defaults to current time on order update. Returns string.
- extrafields array,null
- Type: array. Optional. Defined in individual user databases against SALESORD\_HDR level. For assistance speak to database's implementation partner.
- id integer,null
- Type: integer. Must be specified for PUT. For POST must be blank or negative value. ID value specified is the SEQNO from SALESORD\_HDR
- href string,null
- Type: string. DO NOT SPECIFY. URI location for retrieving sales order.

#### Example json GET response

- {
  - debtorid : 2
  - lines
    - {
      - 0
        - {
          - stockcode : DIFF01
          - isoriginatedfromtemplate : false
          - orderquantity : 1
          - discount : 0
          - unitprice : 2386.62
          - ispriceoverridden : false
          - locationid : 1
          - taxrateid : 0
          - listprice : 2386.62
          - pricepolicyid : -1
          - linetype : 0
          - uninvoicedquantity : 0
          - releasequantity : 1
          - releasenowquantity : 0
          - lastreleasedquantity : 0
          - pickedquantity : 0
          - picknowquantity : 0
          - lastpickedquantity : 0
          - stocktype : PhysicalItem
          - id : 66
          - branchid : 0
          - taxratevalue : 12.5
          - istaxoverridden : false
          - taxoverridevalue : 0
          - narrative
          - backorderquantity : 0
          - description : Hi-Performance Differential
          - batchcode
          - duedate : 2014-10-10
          - linetotal : 2386.62
          - invoicedquantity : 0
          - invoicenowquantity : 0
          - lastinvoicedquantity : 0
          - suppliedquantity : 0
          - supplynowquantity : 0
          - lastsuppliedquantity : 0
          - restrictedline : false
          - rankorder : 1
          - extrafields
            - {
            - }
        - }
    - }
  - taxrounding : 0
  - deliverycount : 0
  - invoicecount : 0
  - pickedcount : 0
  - releasecount : 0
  - hasuninvoiced : false
  - hasunpicked : false
  - hasunreleased : false
  - hasunsupplied : false
  - allowcustomerordernumber : false
  - customerordernumber
  - accountname : MARKER METRO
  - istaxinclusive : false
  - hasbackorders : false
  - branchid : 0
  - defaultlocationid : -1
  - narrative
  - currencyid : 0
  - contactid : -1
  - salespersonid : 2
  - exchangerate : 1
  - taxtotal : 298.33
  - subtotal : 2386.62
  - reference
  - instructions
  - createdate : 2014-09-13T00:00:00+10:00
  - orderdate : 2014-09-13
  - duedate : 2014-10-10
  - finalisationdate
  - activationdate
  - deliveryaddress
    - {
      - line1 : 4238 Anzac Rd
      - line2 : Kingsford
      - line3 : NSW
      - line4 : Australia
      - line5 :
      - line6 :
    - }
  - status : 3
  - statusdescription : Quotation
  - finalised : 0
  - ordertotal : 2684.95
  - localvalue : 2386.62
  - hasrestrictedline : false
  - lastupdated : 2013-11-12T11:55:32+11:00
  - extrafields
    - {
      - 0
        - {
          - key : X\_CHECKEDBY
          - value
        - }
    - }
  - id : 10009
  - href : {URI}https://exo-stage.api.myob.com/salesorder/10009
- }

{URI} is defined as: http://exo.api.myob.com/

|     |     |
| --- | --- |
|  |  |