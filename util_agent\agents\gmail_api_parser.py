"""
Gmail API discovery document parser for LLM consumption.
"""
import json
import logging
from typing import Dict, List, Optional
from pathlib import Path

logger = logging.getLogger(__name__)


class GmailAPIParser:
    """Parse Gmail API discovery document for LLM consumption."""
    
    def __init__(self, discovery_doc_path: str = 'gmail_api_discovery.json'):
        self.discovery_doc_path = discovery_doc_path
        self.discovery_doc = self._load_discovery_doc()
        self.endpoints = self._parse_endpoints() if self.discovery_doc else []
    
    def _load_discovery_doc(self) -> Optional[Dict]:
        """Load Gmail API discovery document."""
        try:
            doc_path = Path(self.discovery_doc_path)
            if not doc_path.exists():
                logger.error(f"Gmail discovery document not found: {doc_path}")
                return None
            
            with open(doc_path, 'r') as f:
                discovery_doc = json.load(f)
            
            logger.info(f"Loaded Gmail API discovery document from: {doc_path}")
            return discovery_doc
            
        except Exception as e:
            logger.error(f"Failed to load Gmail discovery document: {e}")
            return None
    
    def _parse_endpoints(self) -> List[Dict]:
        """Extract all Gmail API endpoints with descriptions."""
        endpoints = []
        
        try:
            resources = self.discovery_doc.get('resources', {})
            
            for resource_name, resource_data in resources.items():
                methods = resource_data.get('methods', {})
                
                for method_name, method_data in methods.items():
                    endpoint = {
                        "resource": resource_name,
                        "method": method_name,
                        "operation_id": f"{resource_name}.{method_name}",
                        "http_method": method_data.get('httpMethod', 'GET'),
                        "path": method_data.get('path', ''),
                        "description": method_data.get('description', ''),
                        "parameters": method_data.get('parameters', {}),
                        "scopes": method_data.get('scopes', []),
                        "supports_media_download": method_data.get('supportsMediaDownload', False),
                        "supports_media_upload": method_data.get('supportsMediaUpload', False)
                    }
                    endpoints.append(endpoint)
            
            logger.info(f"Parsed {len(endpoints)} Gmail API endpoints")
            return endpoints
            
        except Exception as e:
            logger.error(f"Failed to parse Gmail API endpoints: {e}")
            return []
    
    def get_endpoints_by_category(self) -> Dict[str, List[Dict]]:
        """Categorize endpoints for LLM understanding."""
        categories = {
            "message_operations": [],
            "thread_operations": [],
            "label_operations": [],
            "search_operations": [],
            "draft_operations": [],
            "settings_operations": [],
            "attachment_operations": [],
            "history_operations": []
        }
        
        for endpoint in self.endpoints:
            resource = endpoint["resource"]
            method = endpoint["method"]
            
            # Categorize based on resource and method
            if "messages" in resource:
                if "search" in method.lower() or "list" in method.lower():
                    categories["search_operations"].append(endpoint)
                elif "attachments" in resource:
                    categories["attachment_operations"].append(endpoint)
                else:
                    categories["message_operations"].append(endpoint)
            elif "threads" in resource:
                categories["thread_operations"].append(endpoint)
            elif "labels" in resource:
                categories["label_operations"].append(endpoint)
            elif "drafts" in resource:
                categories["draft_operations"].append(endpoint)
            elif "settings" in resource:
                categories["settings_operations"].append(endpoint)
            elif "history" in resource:
                categories["history_operations"].append(endpoint)
            else:
                # Default to message operations
                categories["message_operations"].append(endpoint)
        
        return categories
    
    def get_search_capabilities(self) -> Dict[str, str]:
        """Get Gmail search query capabilities."""
        return {
            "from": "from:<EMAIL> - emails from specific sender",
            "to": "to:<EMAIL> - emails to specific recipient", 
            "subject": "subject:\"text\" - emails with specific subject text",
            "has_attachment": "has:attachment - emails with attachments",
            "filename": "filename:pdf - emails with specific file types",
            "after": "after:2024/01/01 - emails after specific date",
            "before": "before:2024/12/31 - emails before specific date",
            "is_unread": "is:unread - unread emails",
            "is_read": "is:read - read emails",
            "is_starred": "is:starred - starred emails",
            "label": "label:Important - emails with specific label",
            "in": "in:inbox - emails in specific folder",
            "larger": "larger:10M - emails larger than size",
            "smaller": "smaller:1M - emails smaller than size",
            "has_words": "\"exact phrase\" - emails with exact phrase",
            "exclude": "-word - exclude emails with word",
            "or_operator": "word1 OR word2 - emails with either word"
        }
    
    def get_endpoint_by_operation(self, operation_id: str) -> Optional[Dict]:
        """Get specific endpoint by operation ID."""
        for endpoint in self.endpoints:
            if endpoint["operation_id"] == operation_id:
                return endpoint
        return None
    
    def get_common_operations(self) -> List[Dict]:
        """Get most commonly used Gmail operations."""
        common_ops = [
            "users.messages.list",
            "users.messages.get", 
            "users.messages.modify",
            "users.threads.list",
            "users.threads.get",
            "users.labels.list",
            "users.labels.create",
            "users.messages.attachments.get"
        ]
        
        return [self.get_endpoint_by_operation(op) for op in common_ops if self.get_endpoint_by_operation(op)]