"""Main refactored LLM service with MistralAI integration."""


import logging

from typing import Optional, Dict, Any

from contextlib import asynccontextmanager


from .services.mistral_service import MistralService

from .services.memory_service import MemoryService

from .processors.order_processor import OrderProcessor

from .processors.summary_processor import SummaryProcessor

from .processors.payload_generator import PayloadGenerator

from models import EmailData, ExtractedOrder, MYOBPayload

from .core.exceptions import LLMServiceException


logger = logging.getLogger(__name__)



class LLMService:

    """Refactored LLM service with MistralAI integration."""
    

    def __init__(self, mistral_api_key: Optional[str] = None, supabase_service=None):

        """Initialize the LLM service."""

        self.mistral_service = MistralService()

        self.memory_service = MemoryService()

        self.order_processor = OrderProcessor(self.mistral_service, self.memory_service, supabase_service)

        self.summary_processor = SummaryProcessor(self.mistral_service, self.memory_service)

        self.payload_generator = PayloadGenerator()
        

        logger.info("Initialized refactored LLM service with MistralAI")
    

    async def __aenter__(self):

        """Async context manager entry."""

        await self.mistral_service.initialize()

        return self
    

    async def __aexit__(self, exc_type, exc_val, exc_tb):

        """Async context manager exit."""

        await self.close()
    

    async def close(self):

        logger.info("Closing LLM service")
    

    async def health_check(self) -> Dict[str, bool]:

        """Check health of all services."""

        return {

            "mistral": await self.mistral_service.health_check(),

            "memory": await self.memory_service.health_check()

        }
    

    async def process_email(

        self,

        email_body: str,

        subject: str,

        sender: str,

        email_id: Optional[str] = None

    ) -> Dict[str, Any]:

        """Process an email and extract order data with fuzzy matching."""

        try:

            # Generate summary

            summary = await self.summary_processor.generate_email_summary(email_body, subject)
            

            # Extract order data with sender email for fuzzy matching

            order_data = await self.order_processor.extract_order_data(email_body, sender_email=sender)
            

            if order_data:

                # Validate and create order with sender information for fuzzy matching

                await self.order_processor.validate_extracted_order(order_data, sender_email=sender)

                extracted_order = ExtractedOrder(**order_data)
                

                # Generate MYOB payload

                myob_payload = self.payload_generator.create_myob_payload(extracted_order)
                

                # Store in memory

                self.memory_service.store_order_in_memory(

                    order_data=extracted_order.model_dump(),

                    email_id=email_id

                )
                

                return {

                    "summary": summary.dict(),

                    "order": extracted_order.dict(),

                    "myob_payload": myob_payload.dict()

                }

            else:

                return {

                    "summary": summary.dict(),

                    "order": None,

                    "myob_payload": None

                }
                

        except Exception as e:

            logger.error(f"Error processing email: {str(e)}")

            raise LLMServiceException(f"Failed to process email: {str(e)}")
    

    async def generate_markdown_summary(

        self,

        email_body: str,

        subject: str,

        sender: str,

        pdf_content: str = ""

    ) -> str:

        """Generate markdown summary for email and PDF content."""
        return await self.summary_processor.generate_markdown_summary(
            EmailData(
                gmail_id="temp",  # Temporary ID for processing
                subject=subject,
                sender=sender,
                body=email_body
            ),
            pdf_content
        )
    

    async def analyze_email_content(self, content: str, sender_email: str = None) -> Dict[str, Any]:
        """Analyze email content with unstructured LLM reasoning - no validation."""
        return await self.order_processor.analyze_email_content(content, sender_email=sender_email)
    
    async def extract_order_data_for_myob(self, analysis_result: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Extract structured order data from analysis result for MYOB processing."""
        return await self.order_processor.extract_order_data_for_myob(analysis_result)
    
    async def extract_order_from_content(self, content: str, sender_email: str = None) -> Optional[Dict[str, Any]]:
        """Legacy method - now redirects to unstructured analysis."""
        analysis = await self.analyze_email_content(content, sender_email)
        return await self.extract_order_data_for_myob(analysis)
    

    def generate_myob_payload(self, extracted_order: Dict[str, Any]) -> Dict[str, Any]:

        """Generate MYOB payload from extracted order."""

        order = ExtractedOrder(**extracted_order)

        payload = self.payload_generator.create_myob_payload(order)

        return payload.dict()



# Legacy compatibility wrapper

class LegacyLLMService:

    """Legacy wrapper for backward compatibility."""
    

    def __init__(self, mistral_api_key: str, supabase_service=None):

        self._service = LLMService(mistral_api_key, supabase_service)
    

    async def __aenter__(self):

        return self
    

    async def __aexit__(self, exc_type, exc_val, exc_tb):

        await self._service.close()
    

    async def generate_email_summary(self, email_body: str, subject: str) -> Dict[str, str]:

        """Legacy method for email summary."""

        summary = await self._service.summary_processor.generate_email_summary(email_body, subject)

        return summary.dict()
    

    async def generate_markdown_summary(self, email_data, pdf_content: str = "") -> str:

        """Legacy method for markdown summary."""

        email_data_obj = EmailData(

            subject=email_data.subject,

            sender=email_data.sender,

            body=email_data.body

        )

        return await self._service.summary_processor.generate_markdown_summary(

            email_data_obj,
            pdf_content

        )
    

    async def extract_order_data(self, content: str, sender_email: str = None) -> Optional[Dict[str, Any]]:

        """Legacy method for order extraction with fuzzy matching support."""

        return await self._service.extract_order_from_content(content, sender_email=sender_email)
    

    def generate_myob_payload_direct(self, extracted_order: Dict[str, Any]) -> Dict[str, Any]:

        """Legacy method for MYOB payload generation."""

        return self._service.generate_myob_payload(extracted_order)