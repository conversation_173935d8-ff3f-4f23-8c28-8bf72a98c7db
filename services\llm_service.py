"""
LLM service for parsing emails and generating structured data.
Updated to use Google Gen AI SDK (2025) with OpenAI compatibility.
Enhanced with CRUD operations for emails and PDF parsing capabilities.
"""

import json
import logging
import base64
import fitz  # PyMuPDF for PDF processing
from datetime import datetime
from typing import Optional, Dict, Any, List, Union
from openai import OpenAI
from config import config
from models import ExtractedOrder
from memory_client import PersistentMemoryClient

logger = logging.getLogger(__name__)


class LLMService:
    """Service for LLM operations using Google Gemini via OpenAI Compatibility Layer."""

    def __init__(self):
        # Configure OpenAI client for Gemini API using OpenAI compatibility layer
        self.client = OpenAI(
            api_key=config.GEMINI_API_KEY,
            base_url="https://generativelanguage.googleapis.com/v1beta/openai/",
        )

        # Safety settings are handled by Gemini backend, not needed for OpenAI client
        self.safety_settings = {}

        # Generation config (OpenAI-style)
        self.generation_config = {
            "temperature": 0.6,  # Low temperature for consistent extraction
            "top_p": 0.8,
            "max_tokens": 4096,
        }

        self.model = (
            config.GEMINI_MODEL or "gemini-2.5-flash"
        )  # Default to Gemini 2.5 Flash
        self.memory_client = PersistentMemoryClient()

        logger.info(f"Initialized LLM service with model: {self.model}")
        logger.info("Connected to Chroma database for memory retrieval")

    def _convert_response_value(self, value):
        """Convert OpenAI response values to Python native types."""
        if isinstance(value, dict):
            return {k: self._convert_response_value(v) for k, v in value.items()}
        elif isinstance(value, list):
            return [self._convert_response_value(item) for item in value]
        elif isinstance(value, (str, int, float, bool)) or value is None:
            return value
        else:
            logger.warning(f"Unknown response value type: {type(value)}")
            return value

    def get_relevant_context(self, query: str, n_results: int = 5) -> List[str]:
        """Query the memory database for relevant context."""
        try:
            results = self.memory_client.query_memory(
                query_texts=[query], n_results=n_results
            )

            contexts = []
            documents_list = results.get("documents")
            if (
                results
                and documents_list
                and isinstance(documents_list, list)
                and documents_list[0]
            ):
                documents = documents_list[0]
                metadatas_list = results.get("metadatas", [[]])
                metadatas = (
                    metadatas_list[0]
                    if metadatas_list and metadatas_list[0] is not None
                    else []
                )

                for i, doc in enumerate(documents):
                    meta = metadatas[i] if i < len(metadatas) else {}
                    context = f"Document: {doc}"
                    if meta:
                        context += f"\nMetadata: {meta}"
                    contexts.append(context)

            logger.info(
                f"Retrieved {len(contexts)} relevant contexts for query: {query[:50]}..."
            )
            return contexts

        except Exception as e:
            logger.error(f"Error querying memory database: {e}")
            return []

    def store_order_in_memory(
        self, email_data, extracted_order: ExtractedOrder, order_id: str
    ) -> None:
        """Store processed order data in memory for future reference."""
        try:
            document = f"""Order ID: {order_id}
Customer: {extracted_order.customer_details.debtor_id}
Purchase Order: {extracted_order.customer_details.customer_order_number}
Email Subject: {email_data.subject}
Email Sender: {email_data.sender}
Products:
{chr(10).join([f"- {line.stockcode}: {line.orderquantity}" for line in extracted_order.order_lines])}
Shipping: {extracted_order.X_SHIPVIA}
Delivery Address:
{extracted_order.delivery_address.dict() if extracted_order.delivery_address else "None"}"""

            metadata = {
                "order_id": order_id,
                "debtor_id": str(extracted_order.customer_details.debtor_id),
                "po_number": extracted_order.customer_details.customer_order_number
                or "",
                "sender": email_data.sender,
                "subject": email_data.subject,
                "timestamp": (
                    email_data.timestamp if hasattr(email_data, "timestamp") else ""
                ),
                "shipping": extracted_order.X_SHIPVIA or "",
            }

            self.memory_client.add_memory(
                documents=[document], metadatas=[metadata], ids=[f"order_{order_id}"]
            )

            logger.info(f"Stored order {order_id} in memory database")

        except Exception as e:
            logger.error(f"Error storing order in memory: {e}")

    def generate_markdown_summary(self, email_data, pdf_content: str = "") -> str:
        """Generate a structured markdown summary of the email and PDF content."""

        full_content = f"""Email Subject: {email_data.subject}
Email Sender: {email_data.sender}
Email Body:
{email_data.body}

PDF Attachments Content:
{pdf_content}"""

        memory_contexts = self.get_relevant_context(
            f"{email_data.subject} {email_data.sender}", n_results=2
        )
        context_text = ""
        if memory_contexts:
            context_text = (
                f"\n\nSimilar previous orders for reference:\n"
                + "\n---\n".join(memory_contexts)
            )

        prompt = f"""Analyze the following email and PDF content to create a structured markdown summary of important order information.

Focus on extracting:
- Customer details (company name, contact info)
- Order/Purchase order numbers
- Delivery addresses
- Product details (codes, quantities, descriptions)
- Shipping information
- Any special instructions or notes

Present the information in a clear, structured markdown format with appropriate headers and bullet points.

{context_text}

Content to analyze:
{full_content}

Please provide a comprehensive markdown summary:"""

        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": prompt}],
                temperature=0.3,  # Slightly higher for creative markdown formatting
                max_tokens=2048,
                **self.safety_settings,
            )

            if response.choices and response.choices[0].message.content:
                markdown_summary = response.choices[0].message.content
                logger.info("Generated markdown summary successfully")
                return markdown_summary
            else:
                logger.warning(
                    "No text content in LLM response for markdown generation"
                )
                return "Failed to generate summary - no content returned"

        except Exception as e:
            logger.error(f"Error generating markdown summary: {e}")
            return f"Error generating summary: {str(e)}"

    def extract_order_data(self, content: str) -> Optional[Dict[str, Any]]:
        """Extract structured order data from content using LLM with memory context."""

        memory_contexts = self.get_relevant_context(content[:500], n_results=3)
        context_text = ""
        if memory_contexts:
            context_text = "\nRelevant previous examples:\n" + "\n---\n".join(
                memory_contexts
            )

        extract_tool = {
            "type": "function",
            "function": {
                "name": "extract_sales_order_data",
                "description": "Extracts sales order details from text content into a structured JSON format.",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "customer_details": {
                            "type": "object",
                            "properties": {
                                "debtor_id": {
                                    "type": "integer",
                                    "description": "The unique numerical ID of the customer based on company name.",
                                },
                                "customer_order_number": {
                                    "type": "string",
                                    "description": "The customer's full purchase order (PO) number. Examples: 'LVA4401196688', 'DTS4401193941', 'PO12345'.",
                                },
                            },
                            "required": ["debtor_id"],
                        },
                        "order_status": {
                            "type": "integer",
                            "description": "Order status: 0=Not Processed, 3=Quotation. Default to 3.",
                        },
                        "delivery_address": {
                            "type": "object",
                            "properties": {
                                "line1": {"type": "string"},
                                "line2": {"type": "string"},
                                "line3": {"type": "string"},
                                "line4": {"type": "string"},
                                "line5": {"type": "string"},
                                "line6": {"type": "string"},
                            },
                        },
                        "order_lines": {
                            "type": "array",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "stockcode": {
                                        "type": "string",
                                        "description": "The product's stock code. IMPORTANT: If the input text mentions a SKU code starting with 'EQL' (often used by Woolworths or Endeavour Group), you MUST return 'TSSU-ORA' as the stockcode. For other stock codes, return them as found.",
                                    },
                                    "orderquantity": {
                                        "type": "number",
                                        "description": "Quantity ordered (numeric value).",
                                    },
                                },
                                "required": ["stockcode", "orderquantity"],
                            },
                        },
                        "X_SHIPVIA": {
                            "type": "string",
                            "description": "The shipping method. Examples: 'DELTA', 'CAPITAL', 'BEST WAY', 'RING WHEN READY', 'DIRECT FREIGHT'.",
                        },
                    },
                    "required": ["customer_details", "order_lines"],
                },
            },
        }

        prompt = f"""You are an intelligent assistant working for Team Systems, specialized in extracting sales order information from raw text.
Your task is to identify key details and structure them by calling the 'extract_sales_order_data' tool.

Rules:
1. Account name will never be Team Systems; we are the supplier, and any other name is the customer/account name.
2. If a field is missing or unclear, leave it as an empty string or default value (e.g., quantity = 0).
3. Ensure the SKU field has no whitespace.
4. If the dispatch method is "pickup" or empty, set it to "Ring when ready".
5. If a string like "POFREIGHT" or "Freight" is found in the document but does not have a SKU, set the dispatch method to "BEST WAY" and include the unit value unless pickup or empty.
6. Customers will use different SKU codes, e.g., "FPS09K-PL3GST" is "MONSTAR3" in our system.
7. "Gateway Packaging Pty Ltd" dispatch method is "CUSTOMERS CARRIER".
8. "Sitecraft Pty Ltd" is "EMAIL WHEN READY".
9. "RSEA Pty Ltd" = "DIRECT FREIGHT EXPRESS".
10. "Safety Xpress" = "DELTA".
11. "Items" containing "Freight" in the SKU require the SKU to be "CUSTOMER_FREIGHT" with the unit value added to the JSON object.
12. If the SKU does not match the usual format, provide a description in the description field.
13. "Endeavour Group Limited" will always be dispatch method "CAPITAL".
14. Any SKU that mentions "EQLB8012" or similar via Woolworths or Endeavour group will be "TSSU-ORA" in our system.
15. Any account with "Brady" in the name will usually drop ship to the customer.
16. Brady is the account name/customer name, Ship to will be the drop ship address, PO number will be the order number.
17. Depending on Brady's customer location, the dispatch method will be "BEST WAY" until we can learn patterns.
18. "Brady" will always have "Team Systems" SKU as "Your material number:".
19. "Brady" will always refer to the customer order number as "PO Number".
20. Based on the following warehouse locations, provide Delta for Metro and Direct Freight Express for Regional.
21. Delivery in Metro Australia = "DELTA".
22. Delivery in Regional Australia = "DIRECT FREIGHT EXPRESS".
23. Delivery small items in Metro Australia = "DIRECT FREIGHT EXPRESS".
24. Account preferred carrier is "CUSTOMERS CARRIER".
25. The account name will not always match the delivery address due to drop shipping.

Key extraction rules:
26. Customer Details:
- For 'WOOLWORTHS LIMITED', debtor_id is 10981
- For 'ENDEAVOUR GROUP', debtor_id is 21570
- For 'RSEA', debtor_id is 6207
- For 'BRADY', debtor_id is 5760
- For 'GATEWAY', debtor_id 13924
- For 'BRIERLEY', debtor_id 11139
- For 'REFLEX EQUIP', debtor_id 11197
- For 'SITECRAFT', debtor_id 1365
- For 'BLACKWOODS', debtor_id 5228
- Extract the COMPLETE PO number as provided

2. Order Status: Default to 0=Not Processed, 3=Quotation

3. Product Lines:
- For Woolworths/Endeavour group, the stockcode will be "TSSU-ORA" (safety step)
- For BRADY orders: Look for "Your material number" field for stockcode
- For RSEA orders: Look for "Supplier Item Code" field for stockcode
- Extract quantity as numeric value

4. Shipping:
- Common shipping options: "CAPITAL", "BEST WAY", "RING WHEN READY", "DIRECT FREIGHT", "DELTA", "CUSTOMERS CARRIER"

{context_text}

Content to process:
{content}

Please extract the sales order details using the tool:"""

        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": prompt}],
                tools=[extract_tool],
                tool_choice={
                    "type": "function",
                    "function": {"name": "extract_sales_order_data"},
                },
                **self.generation_config,
                **self.safety_settings,
            )

            if response.choices and response.choices[0].message.tool_calls:
                for tool_call in response.choices[0].message.tool_calls:
                    if tool_call.function.name == "extract_sales_order_data":
                        function_args = json.loads(tool_call.function.arguments)
                        logger.info("Successfully extracted order data with LLM")
                        logger.debug(
                            f"Extracted data: {json.dumps(function_args, indent=2, default=str)}"
                        )
                        return function_args

            logger.warning("LLM did not return expected tool call response")
            return None

        except Exception as e:
            logger.error(f"Error extracting order data with LLM: {e}")
            return None    

    def generate_myob_payload(
        self, extracted_order: ExtractedOrder
    ) -> Optional[Dict[str, Any]]:
        """Generate MYOB-compatible payload from extracted order data."""

        myob_tool = {
            "type": "function",
            "function": {
                "name": "generate_myob_sales_order",
                "description": "Converts extracted order data into MYOB EXO API compatible format",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "debtorid": {
                            "type": "integer",
                            "description": "Customer debtor ID",
                        },
                        "customerordernumber": {
                            "type": "string",
                            "description": "Customer's purchase order number",
                        },
                        "status": {
                            "type": "integer",
                            "description": "Order status (0=Not Processed, 3=Quotation)",
                        },
                        "deliveryaddress": {
                            "type": "object",
                            "properties": {
                                "line1": {"type": "string"},
                                "line2": {"type": "string"},
                                "line3": {"type": "string"},
                                "line4": {"type": "string"},
                                "line5": {"type": "string"},
                                "line6": {"type": "string"},
                            },
                        },
                        "lines": {
                            "type": "array",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "stockcode": {
                                        "type": "string",
                                        "description": "Product stock code",
                                    },
                                    "orderquantity": {
                                        "type": "number",
                                        "description": "Quantity to order",
                                    },
                                },
                                "required": ["stockcode", "orderquantity"],
                            },
                        },
                        "extrafields": {
                            "type": "array",
                            "description": "Array of extra fields for additional data like shipping method",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "key": {
                                        "type": "string",
                                        "description": "Field key (e.g., 'X_SHIPVIA')",
                                    },
                                    "value": {
                                        "type": "string",
                                        "description": "Field value (e.g., shipping method)",
                                    },
                                },
                                "required": ["key", "value"],
                            },
                        },
                    },
                    "required": ["debtorid", "lines"],
                },
            },
        }

        order_json = json.dumps(extracted_order.model_dump(), indent=2, default=str)

        prompt = f"""Convert the following extracted order data into a MYOB EXO API compatible sales order payload.

IMPORTANT REQUIREMENTS:
1. Use EXACT field names: "debtorid", "customerordernumber", "status", "deliveryaddress", "lines", "extrafields"
2. For extrafields, use "key" and "value" format (NOT fieldname/fieldvalue)
3. Put X_SHIPVIA shipping method in extrafields at the TOP LEVEL (not in line items)
4. Only include the minimal required fields for order creation
5. Set status to 3 for quotations

EXAMPLE CORRECT ORDER:
{{
  "debtorid": 6207,
  "customerordernumber": "1874786",
  "status": 3,
  "lines": [
    {{
      "stockcode": "HTS500S",
      "orderquantity": 1.0
    }},
    {{
      "stockcode": "CUSTOMER_FREIGHT",
      "orderquantity": 1.0
    }}
  ],
  "deliveryaddress": {{
    "line1": "SPOTLIGHT JOONDALUP",
    "line2": "3/66 EDDYSTONE AVE",
    "line3": "JOONDALUP",
    "line4": "WA",
    "line5": "6027",
    "line6": null
  }},
  "extrafields": [
    {{
      "key": "X_SHIPVIA",
      "value": "DIRECT FREIGHT EXPRESS"
    }}
  ]
}}

Extracted order data:
{order_json}

Please generate the MYOB payload using the tool:"""

        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": prompt}],
                tools=[myob_tool],
                tool_choice={
                    "type": "function",
                    "function": {"name": "generate_myob_sales_order"},
                },
                **self.generation_config,
                **self.safety_settings,
            )

            if response.choices and response.choices[0].message.tool_calls:
                for tool_call in response.choices[0].message.tool_calls:
                    if tool_call.function.name == "generate_myob_sales_order":
                        function_args = json.loads(tool_call.function.arguments)
                        self._validate_myob_payload(function_args)
                        logger.info("Generated MYOB payload successfully")
                        logger.debug(
                            f"MYOB payload: {json.dumps(function_args, indent=2, default=str)}"
                        )
                        return function_args

            logger.warning("LLM did not return expected MYOB payload tool call")
            return None

        except Exception as e:
            logger.error(f"Error generating MYOB payload: {e}")
            return None

    def generate_myob_payload_direct(
        self, extracted_order: ExtractedOrder
    ) -> Dict[str, Any]:
        """Generate MYOB payload directly from extracted order (no LLM) - more reliable."""

        payload = {
            "debtorid": extracted_order.customer_details.debtor_id,
            "status": extracted_order.order_status or 3,  # Default to quotation
            "defaultlocationid": 1,  # Add defaultlocationid underneath status
            "lines": [],
        }

        if extracted_order.customer_details.customer_order_number:
            payload["customerordernumber"] = (
                extracted_order.customer_details.customer_order_number
            )

        for line in extracted_order.order_lines:
            payload["lines"].append(
                {
                    "stockcode": line.stockcode,
                    "orderquantity": float(line.orderquantity),
                }
            )

        if extracted_order.delivery_address:
            addr = extracted_order.delivery_address
            payload["deliveryaddress"] = {
                "line1": addr.line1 or "",
                "line2": addr.line2 or "",
                "line3": addr.line3 or "",
                "line4": addr.line4 or "",
                "line5": addr.line5 or "",
                "line6": addr.line6 or "",
            }

        extrafields = []
        if extracted_order.X_SHIPVIA:
            extrafields.append({"key": "X_SHIPVIA", "value": extracted_order.X_SHIPVIA})

        if extrafields:
            payload["extrafields"] = extrafields

        logger.info("Generated minimal MYOB payload successfully")
        logger.info(f"Final minimal MYOB payload: {json.dumps(payload, indent=2)}")

        return payload

    def _validate_myob_payload(self, payload: Dict[str, Any]) -> None:
        """Validate MYOB payload structure."""
        required_fields = ["debtorid", "lines"]

        for field in required_fields:
            if field not in payload:
                raise ValueError(f"Missing required field: {field}")

        if not isinstance(payload["lines"], list) or len(payload["lines"]) == 0:
            raise ValueError("Lines must be a non-empty array")

        for i, line in enumerate(payload["lines"]):
            if "stockcode" not in line or "orderquantity" not in line:
                raise ValueError(
                    f"Line {i+1} missing required fields: stockcode, orderquantity"
                )

        if "extrafields" in payload:
            if not isinstance(payload["extrafields"], list):
                raise ValueError("extrafields must be an array")

            for i, field in enumerate(payload["extrafields"]):
                if not isinstance(field, dict):
                    raise ValueError(f"extrafields[{i}] must be an object")
                if "key" not in field or "value" not in field:
                    raise ValueError(
                        f"extrafields[{i}] must have 'key' and 'value' properties"
                    )

        logger.debug("MYOB payload validation passed")

    # ==================== EMAIL CRUD OPERATIONS ====================

    def create_email_record(self, email_data: Dict[str, Any]) -> str:
        """Create a new email record in memory with AI analysis."""
        try:
            email_id = email_data.get('id', f"email_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
            
            # AI analysis of email content
            analysis = self.analyze_email_content(email_data)
            
            # Create comprehensive document for storage
            document = f"""Email ID: {email_id}
Subject: {email_data.get('subject', 'No Subject')}
Sender: {email_data.get('sender', 'Unknown')}
Timestamp: {email_data.get('timestamp', datetime.now().isoformat())}
Body: {email_data.get('body', '')[:1000]}...

AI Analysis:
Category: {analysis.get('category', 'unknown')}
Priority: {analysis.get('priority', 'medium')}
Contains Order: {analysis.get('contains_order', False)}
Key Entities: {', '.join(analysis.get('entities', []))}
Summary: {analysis.get('summary', 'No summary available')}

Attachments: {len(email_data.get('attachments', []))} files"""

            metadata = {
                "email_id": email_id,
                "subject": email_data.get('subject', ''),
                "sender": email_data.get('sender', ''),
                "timestamp": email_data.get('timestamp', ''),
                "category": analysis.get('category', 'unknown'),
                "priority": analysis.get('priority', 'medium'),
                "contains_order": str(analysis.get('contains_order', False)),
                "attachment_count": str(len(email_data.get('attachments', []))),
                "type": "email"
            }

            self.memory_client.add_memory(
                documents=[document],
                metadatas=[metadata],
                ids=[f"email_{email_id}"]
            )

            logger.info(f"Created email record {email_id} with AI analysis")
            return email_id

        except Exception as e:
            logger.error(f"Error creating email record: {e}")
            return ""

    def read_email_record(self, email_id: str) -> Optional[Dict[str, Any]]:
        """Read an email record from memory."""
        try:
            results = self.memory_client.query_memory(
                query_texts=[f"email_id:{email_id}"],
                n_results=1,
                where={"email_id": email_id}
            )

            if results and results.get("documents") and results["documents"][0]:
                document = results["documents"][0][0]
                metadata = results.get("metadatas", [[]])[0][0] if results.get("metadatas") else {}
                
                logger.info(f"Retrieved email record {email_id}")
                return {
                    "document": document,
                    "metadata": metadata
                }

            logger.warning(f"Email record {email_id} not found")
            return None

        except Exception as e:
            logger.error(f"Error reading email record {email_id}: {e}")
            return None

    def update_email_record(self, email_id: str, updates: Dict[str, Any]) -> bool:
        """Update an email record with new information."""
        try:
            # First, read the existing record
            existing = self.read_email_record(email_id)
            if not existing:
                logger.warning(f"Cannot update non-existent email record {email_id}")
                return False

            # Update metadata
            updated_metadata = existing["metadata"].copy()
            updated_metadata.update(updates.get("metadata", {}))
            updated_metadata["last_updated"] = datetime.now().isoformat()

            # Update document content if provided
            document = updates.get("document", existing["document"])
            if "analysis" in updates:
                document += f"\n\nUpdated Analysis: {updates['analysis']}"

            # Store updated record
            self.memory_client.add_memory(
                documents=[document],
                metadatas=[updated_metadata],
                ids=[f"email_{email_id}"]
            )

            logger.info(f"Updated email record {email_id}")
            return True

        except Exception as e:
            logger.error(f"Error updating email record {email_id}: {e}")
            return False

    def delete_email_record(self, email_id: str) -> bool:
        """Delete an email record from memory."""
        try:
            # Note: ChromaDB doesn't have a direct delete method in the basic client
            # We'll mark it as deleted in metadata instead
            return self.update_email_record(email_id, {
                "metadata": {"deleted": "true", "deleted_at": datetime.now().isoformat()}
            })

        except Exception as e:
            logger.error(f"Error deleting email record {email_id}: {e}")
            return False

    def search_emails(self, query: str, filters: Optional[Dict[str, Any]] = None, limit: int = 10) -> List[Dict[str, Any]]:
        """Search emails using AI-powered semantic search."""
        try:
            # Build where clause for filtering
            where_clause = {"type": "email"}
            if filters:
                if "category" in filters:
                    where_clause["category"] = filters["category"]
                if "sender" in filters:
                    where_clause["sender"] = filters["sender"]
                if "contains_order" in filters:
                    where_clause["contains_order"] = str(filters["contains_order"])

            results = self.memory_client.query_memory(
                query_texts=[query],
                n_results=limit,
                where=where_clause
            )

            emails = []
            if results and results.get("documents") and results["documents"][0]:
                documents = results["documents"][0]
                metadatas = results.get("metadatas", [[]])[0] if results.get("metadatas") else []
                distances = results.get("distances", [[]])[0] if results.get("distances") else []

                for i, doc in enumerate(documents):
                    metadata = metadatas[i] if i < len(metadatas) else {}
                    distance = distances[i] if i < len(distances) else 1.0
                    
                    # Skip deleted emails
                    if metadata.get("deleted") == "true":
                        continue

                    emails.append({
                        "document": doc,
                        "metadata": metadata,
                        "relevance_score": 1.0 - distance  # Convert distance to relevance
                    })

            logger.info(f"Found {len(emails)} emails matching query: {query[:50]}...")
            return emails

        except Exception as e:
            logger.error(f"Error searching emails: {e}")
            return []

    # ==================== EMAIL ANALYSIS METHODS ====================

    def analyze_email_content(self, email_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze email content using AI to extract insights."""
        
        email_content = f"""Subject: {email_data.get('subject', '')}
From: {email_data.get('sender', '')}
Body: {email_data.get('body', '')}
Attachments: {len(email_data.get('attachments', []))} files"""

        analysis_tool = {
            "type": "function",
            "function": {
                "name": "analyze_email",
                "description": "Analyze email content to extract category, priority, entities, and insights",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "category": {
                            "type": "string",
                            "enum": ["purchase_order", "invoice", "packing_slip", "quote", "general_business", "spam", "personal"],
                            "description": "Primary category of the email"
                        },
                        "priority": {
                            "type": "string",
                            "enum": ["critical", "high", "medium", "low"],
                            "description": "Priority level based on content urgency"
                        },
                        "contains_order": {
                            "type": "boolean",
                            "description": "Whether the email contains order information"
                        },
                        "entities": {
                            "type": "array",
                            "items": {"type": "string"},
                            "description": "Key entities found (company names, PO numbers, product codes, etc.)"
                        },
                        "summary": {
                            "type": "string",
                            "description": "Brief summary of the email content"
                        },
                        "action_required": {
                            "type": "boolean",
                            "description": "Whether the email requires action"
                        },
                        "confidence_score": {
                            "type": "number",
                            "minimum": 0,
                            "maximum": 1,
                            "description": "Confidence in the analysis (0-1)"
                        }
                    },
                    "required": ["category", "priority", "contains_order", "summary"]
                }
            }
        }

        prompt = f"""Analyze the following email content and provide structured insights:

{email_content}

Please analyze this email and provide:
1. Category classification
2. Priority assessment
3. Whether it contains order information
4. Key entities (companies, PO numbers, products, etc.)
5. Brief summary
6. Whether action is required
7. Confidence score for your analysis

Focus on business context and order processing relevance."""

        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": prompt}],
                tools=[analysis_tool],
                tool_choice={"type": "function", "function": {"name": "analyze_email"}},
                temperature=0.3,
                max_tokens=1024
            )

            if response.choices and response.choices[0].message.tool_calls:
                for tool_call in response.choices[0].message.tool_calls:
                    if tool_call.function.name == "analyze_email":
                        analysis = json.loads(tool_call.function.arguments)
                        logger.info(f"Analyzed email: category={analysis.get('category')}, priority={analysis.get('priority')}")
                        return analysis

            logger.warning("No analysis tool call returned")
            return self._default_email_analysis()

        except Exception as e:
            logger.error(f"Error analyzing email content: {e}")
            return self._default_email_analysis()

    def _default_email_analysis(self) -> Dict[str, Any]:
        """Return default analysis when AI analysis fails."""
        return {
            "category": "general_business",
            "priority": "medium",
            "contains_order": False,
            "entities": [],
            "summary": "Analysis unavailable",
            "action_required": False,
            "confidence_score": 0.0
        }

    # ==================== PDF PARSING METHODS ====================

    def extract_text_from_pdf(self, pdf_data: bytes) -> str:
        """Extract text content from PDF bytes using PyMuPDF."""
        try:
            # Open PDF from bytes
            pdf_document = fitz.open(stream=pdf_data, filetype="pdf")
            
            text_content = ""
            for page_num in range(pdf_document.page_count):
                page = pdf_document[page_num]
                text_content += page.get_text()
                text_content += "\n\n"  # Separate pages
            
            pdf_document.close()
            
            logger.info(f"Extracted {len(text_content)} characters from PDF ({pdf_document.page_count} pages)")
            return text_content.strip()

        except Exception as e:
            logger.error(f"Error extracting text from PDF: {e}")
            return ""

    def parse_pdf_order(self, pdf_data: bytes, filename: str = "") -> Optional[Dict[str, Any]]:
        """Parse PDF content to extract order information."""
        try:
            # Extract text from PDF
            pdf_text = self.extract_text_from_pdf(pdf_data)
            if not pdf_text:
                logger.warning(f"No text extracted from PDF {filename}")
                return None

            # Use existing order extraction method
            order_data = self.extract_order_data(pdf_text)
            
            if order_data:
                # Add PDF-specific metadata
                order_data["source_type"] = "pdf"
                order_data["source_filename"] = filename
                order_data["pdf_text_length"] = len(pdf_text)
                
                logger.info(f"Successfully parsed order from PDF {filename}")
                return order_data
            else:
                logger.warning(f"No order data found in PDF {filename}")
                return None

        except Exception as e:
            logger.error(f"Error parsing PDF order {filename}: {e}")
            return None

    def analyze_pdf_structure(self, pdf_data: bytes) -> Dict[str, Any]:
        """Analyze PDF structure and content using AI."""
        try:
            pdf_text = self.extract_text_from_pdf(pdf_data)
            if not pdf_text:
                return {"error": "No text content found"}

            structure_tool = {
                "type": "function",
                "function": {
                    "name": "analyze_pdf_structure",
                    "description": "Analyze PDF structure and identify document type and key sections",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "document_type": {
                                "type": "string",
                                "enum": ["purchase_order", "invoice", "packing_slip", "quote", "catalog", "manual", "other"],
                                "description": "Type of document identified"
                            },
                            "key_sections": {
                                "type": "array",
                                "items": {"type": "string"},
                                "description": "Main sections found in the document"
                            },
                            "has_tables": {
                                "type": "boolean",
                                "description": "Whether the document contains tabular data"
                            },
                            "has_order_lines": {
                                "type": "boolean",
                                "description": "Whether the document contains order line items"
                            },
                            "complexity": {
                                "type": "string",
                                "enum": ["simple", "moderate", "complex"],
                                "description": "Document complexity level"
                            },
                            "confidence": {
                                "type": "number",
                                "minimum": 0,
                                "maximum": 1,
                                "description": "Confidence in document type identification"
                            }
                        },
                        "required": ["document_type", "key_sections", "has_tables", "has_order_lines"]
                    }
                }
            }

            prompt = f"""Analyze the following PDF text content and identify its structure:

{pdf_text[:2000]}...

Please analyze this document and provide:
1. Document type classification
2. Key sections identified
3. Whether it contains tables
4. Whether it has order line items
5. Complexity assessment
6. Confidence in your analysis

Focus on business document structure and order processing relevance."""

            response = self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": prompt}],
                tools=[structure_tool],
                tool_choice={"type": "function", "function": {"name": "analyze_pdf_structure"}},
                temperature=0.2,
                max_tokens=1024
            )

            if response.choices and response.choices[0].message.tool_calls:
                for tool_call in response.choices[0].message.tool_calls:
                    if tool_call.function.name == "analyze_pdf_structure":
                        analysis = json.loads(tool_call.function.arguments)
                        analysis["text_length"] = len(pdf_text)
                        logger.info(f"Analyzed PDF structure: type={analysis.get('document_type')}")
                        return analysis

            return {"error": "Analysis failed"}

        except Exception as e:
            logger.error(f"Error analyzing PDF structure: {e}")
            return {"error": str(e)}

    def extract_pdf_tables(self, pdf_data: bytes) -> List[Dict[str, Any]]:
        """Extract and structure table data from PDF."""
        try:
            pdf_document = fitz.open(stream=pdf_data, filetype="pdf")
            tables = []

            for page_num in range(pdf_document.page_count):
                page = pdf_document[page_num]
                
                # Try to find tables using text blocks
                blocks = page.get_text("dict")["blocks"]
                
                # Simple table detection based on text alignment
                potential_tables = self._detect_table_structures(blocks)
                
                for table in potential_tables:
                    tables.append({
                        "page": page_num + 1,
                        "rows": table["rows"],
                        "columns": table["columns"],
                        "data": table["data"]
                    })

            pdf_document.close()
            
            logger.info(f"Extracted {len(tables)} tables from PDF")
            return tables

        except Exception as e:
            logger.error(f"Error extracting PDF tables: {e}")
            return []

    def _detect_table_structures(self, blocks: List[Dict]) -> List[Dict[str, Any]]:
        """Simple table detection from text blocks."""
        # This is a simplified implementation
        # In production, you might want to use more sophisticated table detection
        tables = []
        
        try:
            # Look for patterns that suggest tabular data
            text_lines = []
            for block in blocks:
                if "lines" in block:
                    for line in block["lines"]:
                        for span in line["spans"]:
                            text_lines.append({
                                "text": span["text"],
                                "bbox": span["bbox"],
                                "y": span["bbox"][1]  # y-coordinate for sorting
                            })
            
            # Sort by y-coordinate to get reading order
            text_lines.sort(key=lambda x: x["y"])
            
            # Simple heuristic: if we have lines with similar y-coordinates
            # and multiple columns, it might be a table
            current_table = []
            current_y = None
            tolerance = 5  # pixels
            
            for line in text_lines:
                if current_y is None or abs(line["y"] - current_y) < tolerance:
                    # Same row
                    if current_y is None:
                        current_table = [line]
                        current_y = line["y"]
                    else:
                        current_table.append(line)
                else:
                    # New row - process current table if it has multiple columns
                    if len(current_table) > 1:
                        tables.append({
                            "rows": 1,
                            "columns": len(current_table),
                            "data": [[item["text"] for item in current_table]]
                        })
                    
                    current_table = [line]
                    current_y = line["y"]
            
            # Don't forget the last table
            if len(current_table) > 1:
                tables.append({
                    "rows": 1,
                    "columns": len(current_table),
                    "data": [[item["text"] for item in current_table]]
                })
                
        except Exception as e:
            logger.error(f"Error in table detection: {e}")
        
        return tables

    # ==================== ENHANCED ORDER PROCESSING ====================

    def process_email_with_pdfs(self, email_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process email and all PDF attachments for order information."""
        try:
            results = {
                "email_id": email_data.get("id", ""),
                "email_analysis": self.analyze_email_content(email_data),
                "pdf_analyses": [],
                "extracted_orders": [],
                "processing_summary": {}
            }

            # Process each PDF attachment
            for attachment in email_data.get("attachments", []):
                if attachment.get("filename", "").lower().endswith(".pdf") and attachment.get("data"):
                    pdf_data = attachment["data"]
                    filename = attachment["filename"]
                    
                    # Analyze PDF structure
                    pdf_analysis = self.analyze_pdf_structure(pdf_data)
                    pdf_analysis["filename"] = filename
                    results["pdf_analyses"].append(pdf_analysis)
                    
                    # Try to extract order data
                    order_data = self.parse_pdf_order(pdf_data, filename)
                    if order_data:
                        results["extracted_orders"].append({
                            "source": filename,
                            "order_data": order_data
                        })

            # Create processing summary
            results["processing_summary"] = {
                "total_pdfs": len([a for a in email_data.get("attachments", []) if a.get("filename", "").lower().endswith(".pdf")]),
                "analyzed_pdfs": len(results["pdf_analyses"]),
                "orders_found": len(results["extracted_orders"]),
                "email_contains_order": results["email_analysis"].get("contains_order", False),
                "processing_timestamp": datetime.now().isoformat()
            }

            # Store comprehensive record
            email_id = self.create_email_record(email_data)
            results["email_id"] = email_id

            logger.info(f"Processed email with {results['processing_summary']['total_pdfs']} PDFs, found {results['processing_summary']['orders_found']} orders")
            return results

        except Exception as e:
            logger.error(f"Error processing email with PDFs: {e}")
            return {"error": str(e)}