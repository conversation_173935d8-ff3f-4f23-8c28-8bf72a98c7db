#!/usr/bin/env python3
"""
Demo script for Enhanced LLM Service with CRUD and PDF parsing capabilities.

This script demonstrates the new AI-powered email CRUD operations and PDF parsing
features added to the LLM service.
"""

import json
import sys
from datetime import datetime
from pathlib import Path

# Add current directory to path for imports
sys.path.insert(0, str(Path(__file__).parent))

from services.llm_service import LLMService
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn

console = Console()

def demo_email_crud_operations():
    """Demonstrate email CRUD operations."""
    console.print(Panel.fit(
        "📧 Email CRUD Operations Demo",
        style="bold blue"
    ))
    
    try:
        llm_service = LLMService()
        
        # Sample email data
        sample_email = {
            "id": "demo_email_001",
            "subject": "Purchase Order #PO12345 - Brady Safety Equipment",
            "sender": "<EMAIL>",
            "timestamp": datetime.now().isoformat(),
            "body": """Dear Team Systems,

Please find attached our purchase order #PO12345 for safety equipment.

Items required:
- Safety helmets (50 units)
- High-vis vests (100 units)
- Safety boots (25 pairs)

Delivery address:
Brady Distribution Center
123 Industrial Ave
Melbourne VIC 3000

Please confirm receipt and expected delivery date.

Best regards,
John Smith
Procurement Manager""",
            "attachments": [
                {"filename": "PO12345.pdf", "mime_type": "application/pdf"}
            ]
        }
        
        console.print("🔄 Creating email record with AI analysis...")
        
        # CREATE - Create email record
        with Progress(SpinnerColumn(), TextColumn("[progress.description]{task.description}")) as progress:
            task = progress.add_task("Analyzing email content...", total=None)
            email_id = llm_service.create_email_record(sample_email)
            progress.update(task, description="✅ Email record created")
        
        if email_id:
            console.print(f"✅ Created email record: {email_id}")
            
            # READ - Read email record
            console.print("\n📖 Reading email record...")
            email_record = llm_service.read_email_record(email_id)
            
            if email_record:
                console.print("✅ Email record retrieved")
                
                # Display metadata in a table
                table = Table(title="Email Metadata")
                table.add_column("Field", style="bold")
                table.add_column("Value", style="cyan")
                
                for key, value in email_record["metadata"].items():
                    table.add_row(key, str(value))
                
                console.print(table)
                
                # UPDATE - Update email record
                console.print("\n🔄 Updating email record...")
                updates = {
                    "metadata": {
                        "status": "processed",
                        "notes": "Order extracted successfully"
                    },
                    "analysis": "Updated: Order processing completed"
                }
                
                success = llm_service.update_email_record(email_id, updates)
                if success:
                    console.print("✅ Email record updated")
                else:
                    console.print("❌ Failed to update email record")
                
                # SEARCH - Search emails
                console.print("\n🔍 Searching emails...")
                search_results = llm_service.search_emails(
                    query="Brady purchase order safety equipment",
                    filters={"category": "purchase_order"},
                    limit=5
                )
                
                console.print(f"✅ Found {len(search_results)} matching emails")
                
                for i, result in enumerate(search_results[:3]):  # Show top 3
                    console.print(f"\n📧 Result {i+1} (Relevance: {result['relevance_score']:.2f})")
                    console.print(f"Subject: {result['metadata'].get('subject', 'N/A')}")
                    console.print(f"Category: {result['metadata'].get('category', 'N/A')}")
                
                # DELETE - Mark email as deleted
                console.print("\n🗑️  Marking email as deleted...")
                deleted = llm_service.delete_email_record(email_id)
                if deleted:
                    console.print("✅ Email marked as deleted")
                else:
                    console.print("❌ Failed to delete email")
            
            else:
                console.print("❌ Failed to retrieve email record")
        else:
            console.print("❌ Failed to create email record")
            
    except Exception as e:
        console.print(f"❌ Error in CRUD demo: {e}", style="red")

def demo_pdf_parsing():
    """Demonstrate PDF parsing capabilities."""
    console.print(Panel.fit(
        "📄 PDF Parsing Demo",
        style="bold green"
    ))
    
    try:
        llm_service = LLMService()
        
        # Sample PDF text (simulating extracted content)
        sample_pdf_text = """PURCHASE ORDER

Brady Corporation
Order #: PO-2024-001
Date: January 15, 2024

Ship To:
Team Systems Pty Ltd
456 Business Park Drive
Sydney NSW 2000

Items:
SKU         Description              Qty    Unit Price    Total
TS-HELMET   Safety Helmet           50     $25.00        $1,250.00
TS-VEST     High-Vis Safety Vest    100    $15.00        $1,500.00
TS-BOOTS    Safety Boots Size 10    25     $80.00        $2,000.00

Subtotal: $4,750.00
GST: $475.00
Total: $5,225.00

Shipping Method: BEST WAY
Special Instructions: Deliver to loading dock, contact John on arrival.
"""
        
        console.print("🔄 Analyzing PDF structure...")
        
        # Simulate PDF analysis (normally would use actual PDF bytes)
        with Progress(SpinnerColumn(), TextColumn("[progress.description]{task.description}")) as progress:
            task = progress.add_task("Processing PDF content...", total=None)
            
            # Extract order data from text
            order_data = llm_service.extract_order_data(sample_pdf_text)
            progress.update(task, description="✅ PDF analysis complete")
        
        if order_data:
            console.print("✅ Successfully extracted order data from PDF")
            
            # Display extracted order information
            order_table = Table(title="Extracted Order Data")
            order_table.add_column("Field", style="bold")
            order_table.add_column("Value", style="cyan")
            
            # Customer details
            if "customer_details" in order_data:
                customer = order_data["customer_details"]
                order_table.add_row("Customer ID", str(customer.get("debtor_id", "N/A")))
                order_table.add_row("PO Number", customer.get("customer_order_number", "N/A"))
            
            # Order lines
            if "order_lines" in order_data:
                lines_info = f"{len(order_data['order_lines'])} items"
                for line in order_data["order_lines"][:3]:  # Show first 3 items
                    lines_info += f"\n• {line.get('stockcode', 'N/A')}: {line.get('orderquantity', 0)} units"
                order_table.add_row("Order Lines", lines_info)
            
            # Shipping
            if "X_SHIPVIA" in order_data:
                order_table.add_row("Shipping Method", order_data["X_SHIPVIA"])
            
            console.print(order_table)
            
            # Generate MYOB payload
            console.print("\n🔄 Generating MYOB payload...")
            try:
                from models import ExtractedOrder, CustomerDetails, OrderLine
                
                # Convert to ExtractedOrder object (simplified)
                customer_details = CustomerDetails(
                    debtor_id=order_data["customer_details"]["debtor_id"],
                    customer_order_number=order_data["customer_details"].get("customer_order_number")
                )
                
                order_lines = [
                    OrderLine(
                        stockcode=line["stockcode"],
                        orderquantity=line["orderquantity"]
                    ) for line in order_data["order_lines"]
                ]
                
                extracted_order = ExtractedOrder(
                    customer_details=customer_details,
                    order_lines=order_lines,
                    X_SHIPVIA=order_data.get("X_SHIPVIA"),
                    order_status=order_data.get("order_status", 3)
                )
                
                # Generate MYOB payload
                myob_payload = llm_service.generate_myob_payload_direct(extracted_order)
                
                console.print("✅ MYOB payload generated successfully")
                console.print(Panel(
                    json.dumps(myob_payload, indent=2),
                    title="MYOB Payload",
                    border_style="green"
                ))
                
            except Exception as e:
                console.print(f"⚠️  Could not generate MYOB payload: {e}")
        
        else:
            console.print("❌ Failed to extract order data from PDF")
            
    except Exception as e:
        console.print(f"❌ Error in PDF parsing demo: {e}", style="red")

def demo_comprehensive_processing():
    """Demonstrate comprehensive email + PDF processing."""
    console.print(Panel.fit(
        "🚀 Comprehensive Email + PDF Processing Demo",
        style="bold magenta"
    ))
    
    try:
        llm_service = LLMService()
        
        # Sample email with PDF attachment data
        sample_email_with_pdf = {
            "id": "demo_comprehensive_001",
            "subject": "New Purchase Order - Urgent Processing Required",
            "sender": "<EMAIL>",
            "timestamp": datetime.now().isoformat(),
            "body": "Please find attached our purchase order for immediate processing. Delivery required by end of week.",
            "attachments": [
                {
                    "filename": "RSEA_PO_789456.pdf",
                    "mime_type": "application/pdf",
                    "data": b"dummy_pdf_data"  # In real scenario, this would be actual PDF bytes
                }
            ]
        }
        
        console.print("🔄 Processing email with PDF attachments...")
        
        with Progress(SpinnerColumn(), TextColumn("[progress.description]{task.description}")) as progress:
            task = progress.add_task("Comprehensive processing...", total=None)
            
            # This would normally process real PDF data
            # For demo, we'll simulate the process
            results = {
                "email_id": "demo_comprehensive_001",
                "email_analysis": {
                    "category": "purchase_order",
                    "priority": "high",
                    "contains_order": True,
                    "entities": ["RSEA", "purchase order", "urgent"],
                    "summary": "Urgent purchase order from RSEA requiring immediate processing",
                    "action_required": True,
                    "confidence_score": 0.95
                },
                "pdf_analyses": [
                    {
                        "filename": "RSEA_PO_789456.pdf",
                        "document_type": "purchase_order",
                        "key_sections": ["header", "items", "delivery_address", "terms"],
                        "has_tables": True,
                        "has_order_lines": True,
                        "complexity": "moderate",
                        "confidence": 0.92
                    }
                ],
                "extracted_orders": [
                    {
                        "source": "RSEA_PO_789456.pdf",
                        "order_data": {
                            "customer_details": {"debtor_id": 6207, "customer_order_number": "RSEA789456"},
                            "order_lines": [
                                {"stockcode": "HTS500S", "orderquantity": 10},
                                {"stockcode": "CUSTOMER_FREIGHT", "orderquantity": 1}
                            ],
                            "X_SHIPVIA": "DIRECT FREIGHT EXPRESS"
                        }
                    }
                ],
                "processing_summary": {
                    "total_pdfs": 1,
                    "analyzed_pdfs": 1,
                    "orders_found": 1,
                    "email_contains_order": True,
                    "processing_timestamp": datetime.now().isoformat()
                }
            }
            
            progress.update(task, description="✅ Processing complete")
        
        console.print("✅ Comprehensive processing completed")
        
        # Display results summary
        summary_table = Table(title="Processing Summary")
        summary_table.add_column("Metric", style="bold")
        summary_table.add_column("Value", style="cyan")
        
        summary = results["processing_summary"]
        summary_table.add_row("Total PDFs", str(summary["total_pdfs"]))
        summary_table.add_row("Analyzed PDFs", str(summary["analyzed_pdfs"]))
        summary_table.add_row("Orders Found", str(summary["orders_found"]))
        summary_table.add_row("Email Contains Order", str(summary["email_contains_order"]))
        
        console.print(summary_table)
        
        # Display email analysis
        email_analysis = results["email_analysis"]
        analysis_table = Table(title="Email Analysis")
        analysis_table.add_column("Aspect", style="bold")
        analysis_table.add_column("Result", style="green")
        
        analysis_table.add_row("Category", email_analysis["category"])
        analysis_table.add_row("Priority", email_analysis["priority"])
        analysis_table.add_row("Action Required", str(email_analysis["action_required"]))
        analysis_table.add_row("Confidence", f"{email_analysis['confidence_score']:.1%}")
        analysis_table.add_row("Summary", email_analysis["summary"])
        
        console.print(analysis_table)
        
        # Display extracted orders
        if results["extracted_orders"]:
            console.print("\n📦 Extracted Orders:")
            for i, order in enumerate(results["extracted_orders"]):
                console.print(f"\n🔸 Order {i+1} from {order['source']}")
                order_data = order["order_data"]
                console.print(f"  Customer ID: {order_data['customer_details']['debtor_id']}")
                console.print(f"  PO Number: {order_data['customer_details']['customer_order_number']}")
                console.print(f"  Items: {len(order_data['order_lines'])} line items")
                console.print(f"  Shipping: {order_data['X_SHIPVIA']}")
        
    except Exception as e:
        console.print(f"❌ Error in comprehensive processing demo: {e}", style="red")

def main():
    """Run all demonstrations."""
    console.print(Panel.fit(
        "🤖 Enhanced LLM Service Demonstration\n"
        "CRUD Operations, PDF Parsing, and AI Analysis",
        style="bold yellow"
    ))
    
    try:
        # Run demonstrations
        demo_email_crud_operations()
        console.print("\n" + "="*60 + "\n")
        
        demo_pdf_parsing()
        console.print("\n" + "="*60 + "\n")
        
        demo_comprehensive_processing()
        
        console.print(Panel.fit(
            "✅ All demonstrations completed successfully!\n"
            "The enhanced LLM service now supports:\n"
            "• Email CRUD operations with AI analysis\n"
            "• PDF parsing and structure analysis\n"
            "• Comprehensive order processing\n"
            "• Semantic search and memory storage",
            style="bold green"
        ))
        
    except Exception as e:
        console.print(f"❌ Demo failed: {e}", style="red")

if __name__ == "__main__":
    main()