"""
Enhanced Gmail service with AI-powered email processing capabilities.
Extends the base GmailService with intelligent analysis, CRUD operations, and automated processing.
"""

import os
import json
import logging
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any, Union
from dataclasses import dataclass

from services.gmail_service import GmailService
from services.llm_service import LLMService
from utils.config import config
from utils.models import EmailData, ExtractedOrder

logger = logging.getLogger(__name__)


@dataclass
class EmailProcessingResult:
    """Result of processing an email with AI analysis following enhanced LLM workflow."""
    email_id: str
    email_data: EmailData
    ai_analysis: Dict[str, Any]
    extracted_orders: List[Dict[str, Any]]
    pdf_analyses: List[Dict[str, Any]]
    processing_status: str
    error_message: Optional[str] = None
    processing_time: Optional[float] = None
    myob_payloads: Optional[List[Dict[str, Any]]] = None
    processing_summary: Optional[Dict[str, Any]] = None
    email_record_id: Optional[str] = None


@dataclass
class BatchProcessingResult:
    """Result of batch processing multiple emails."""
    total_emails: int
    processed_emails: int
    successful_extractions: int
    failed_extractions: int
    processing_time: float
    results: List[EmailProcessingResult]


class EnhancedGmailService(GmailService):
    """Enhanced Gmail service with AI-powered processing capabilities."""
    
    def __init__(self):
        super().__init__()
        self.llm_service = LLMService()
        self.processing_stats = {
            "emails_processed": 0,
            "orders_extracted": 0,
            "processing_errors": 0,
            "last_processing_time": None
        }
        logger.info("Enhanced Gmail service initialized with AI capabilities")
    
    # ==================== AI-POWERED EMAIL PROCESSING ====================
    
    def process_email_with_ai(self, email_data: EmailData) -> EmailProcessingResult:
        """Process a single email with comprehensive AI analysis following the enhanced LLM workflow."""
        start_time = datetime.now()
        
        try:
            logger.info(f"Processing email {email_data.id} with comprehensive AI analysis")
            
            # Step 1: Create email record with AI analysis (CRUD - CREATE)
            email_dict = {
                "id": email_data.id,
                "subject": email_data.subject,
                "sender": email_data.sender,
                "timestamp": email_data.timestamp,
                "body": email_data.body,
                "attachments": email_data.attachments,
                "source_label": email_data.source_label
            }
            
            # Create email record in memory with AI analysis
            email_record_id = self.llm_service.create_email_record(email_dict)
            logger.info(f"Created email record: {email_record_id}")
            
            # Step 2: Process email with comprehensive PDF analysis
            ai_results = self.llm_service.process_email_with_pdfs(email_dict)
            
            # Step 3: Extract and structure results
            ai_analysis = ai_results.get("email_analysis", {})
            extracted_orders = ai_results.get("extracted_orders", [])
            pdf_analyses = ai_results.get("pdf_analyses", [])
            processing_summary = ai_results.get("processing_summary", {})
            
            # Step 4: Generate MYOB payloads for extracted orders
            myob_payloads = []
            for order_info in extracted_orders:
                try:
                    order_data = order_info.get("order_data", {})
                    if order_data and "customer_details" in order_data and "order_lines" in order_data:
                        
                        # Convert to ExtractedOrder object
                        from models import ExtractedOrder, CustomerDetails, OrderLine
                        
                        customer_details = CustomerDetails(
                            debtor_id=order_data["customer_details"]["debtor_id"],
                            customer_order_number=order_data["customer_details"].get("customer_order_number")
                        )
                        
                        order_lines = [
                            OrderLine(
                                stockcode=line["stockcode"],
                                orderquantity=line["orderquantity"]
                            ) for line in order_data["order_lines"]
                        ]
                        
                        extracted_order = ExtractedOrder(
                            customer_details=customer_details,
                            order_lines=order_lines,
                            X_SHIPVIA=order_data.get("X_SHIPVIA"),
                            order_status=order_data.get("order_status", 3)
                        )
                        
                        # Generate MYOB payload
                        myob_payload = self.llm_service.generate_myob_payload_direct(extracted_order)
                        myob_payloads.append({
                            "source": order_info.get("source", "email"),
                            "payload": myob_payload
                        })
                        
                        logger.info(f"Generated MYOB payload for order from {order_info.get('source', 'email')}")
                        
                except Exception as e:
                    logger.warning(f"Failed to generate MYOB payload for order: {e}")
            
            # Step 5: Update email record with processing results (CRUD - UPDATE)
            if email_record_id:
                updates = {
                    "metadata": {
                        "processing_status": "completed",
                        "orders_extracted": len(extracted_orders),
                        "myob_payloads_generated": len(myob_payloads),
                        "processing_time": (datetime.now() - start_time).total_seconds()
                    },
                    "analysis": f"Comprehensive processing completed. Found {len(extracted_orders)} orders, generated {len(myob_payloads)} MYOB payloads."
                }
                self.llm_service.update_email_record(email_record_id, updates)
            
            # Update processing stats
            self.processing_stats["emails_processed"] += 1
            self.processing_stats["orders_extracted"] += len(extracted_orders)
            self.processing_stats["last_processing_time"] = datetime.now().isoformat()
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            # Create comprehensive result
            result = EmailProcessingResult(
                email_id=email_data.id,
                email_data=email_data,
                ai_analysis=ai_analysis,
                extracted_orders=extracted_orders,
                pdf_analyses=pdf_analyses,
                processing_status="success",
                processing_time=processing_time
            )
            
            # Add MYOB payloads to result
            result.myob_payloads = myob_payloads
            result.processing_summary = processing_summary
            result.email_record_id = email_record_id
            
            logger.info(f"Successfully processed email {email_data.id} in {processing_time:.2f}s - "
                       f"Found {len(extracted_orders)} orders, generated {len(myob_payloads)} MYOB payloads")
            return result
            
        except Exception as e:
            self.processing_stats["processing_errors"] += 1
            processing_time = (datetime.now() - start_time).total_seconds()
            
            logger.error(f"Error processing email {email_data.id}: {e}")
            
            return EmailProcessingResult(
                email_id=email_data.id,
                email_data=email_data,
                ai_analysis={},
                extracted_orders=[],
                pdf_analyses=[],
                processing_status="error",
                error_message=str(e),
                processing_time=processing_time
            )
    
    def batch_process_emails(self, emails: List[EmailData], 
                           filter_criteria: Optional[Dict[str, Any]] = None) -> BatchProcessingResult:
        """Process multiple emails in batch with AI analysis."""
        start_time = datetime.now()
        results = []
        successful_extractions = 0
        failed_extractions = 0
        
        logger.info(f"Starting batch processing of {len(emails)} emails")
        
        for email in emails:
            # Apply filters if specified
            if filter_criteria and not self._matches_filter(email, filter_criteria):
                continue
            
            result = self.process_email_with_ai(email)
            results.append(result)
            
            if result.processing_status == "success" and result.extracted_orders:
                successful_extractions += 1
            elif result.processing_status == "error":
                failed_extractions += 1
        
        processing_time = (datetime.now() - start_time).total_seconds()
        
        batch_result = BatchProcessingResult(
            total_emails=len(emails),
            processed_emails=len(results),
            successful_extractions=successful_extractions,
            failed_extractions=failed_extractions,
            processing_time=processing_time,
            results=results
        )
        
        logger.info(f"Batch processing completed: {successful_extractions} successful extractions, "
                   f"{failed_extractions} failures in {processing_time:.2f}s")
        
        return batch_result
    
    def _matches_filter(self, email: EmailData, criteria: Dict[str, Any]) -> bool:
        """Check if email matches filter criteria."""
        if "sender_contains" in criteria:
            if criteria["sender_contains"].lower() not in email.sender.lower():
                return False
        
        if "subject_contains" in criteria:
            if criteria["subject_contains"].lower() not in email.subject.lower():
                return False
        
        if "has_attachments" in criteria:
            has_attachments = len(email.attachments) > 0
            if criteria["has_attachments"] != has_attachments:
                return False
        
        if "min_attachments" in criteria:
            if len(email.attachments) < criteria["min_attachments"]:
                return False
        
        return True
    
    # ==================== INTELLIGENT EMAIL FETCHING ====================
    
    def fetch_emails_with_ai_filtering(self, 
                                     labels: Optional[List[str]] = None,
                                     days_back: int = 7,
                                     ai_filter: bool = True,
                                     min_confidence: float = 0.7) -> List[EmailData]:
        """Fetch emails with AI-powered filtering and prioritization."""
        
        # Use existing fetch method or default labels
        if labels:
            # Temporarily override config for this fetch
            original_labels = config.GMAIL_LABELS_TO_PROCESS
            config.GMAIL_LABELS_TO_PROCESS = labels
            emails = self.fetch_emails_from_labels()
            config.GMAIL_LABELS_TO_PROCESS = original_labels
        else:
            emails = self.fetch_emails_from_labels()
        
        if not ai_filter:
            return emails
        
        logger.info(f"Applying AI filtering to {len(emails)} emails")
        
        # Apply AI filtering
        filtered_emails = []
        for email in emails:
            try:
                # Quick AI analysis for filtering
                email_dict = {
                    "subject": email.subject,
                    "sender": email.sender,
                    "body": email.body[:500],  # First 500 chars for quick analysis
                    "attachments": email.attachments
                }
                
                analysis = self.llm_service.analyze_email_content(email_dict)
                confidence = analysis.get("confidence_score", 0.0)
                
                if confidence >= min_confidence:
                    filtered_emails.append(email)
                    logger.debug(f"Email {email.id} passed AI filter (confidence: {confidence:.2f})")
                else:
                    logger.debug(f"Email {email.id} filtered out (confidence: {confidence:.2f})")
                    
            except Exception as e:
                logger.warning(f"AI filtering failed for email {email.id}: {e}")
                # Include email if AI filtering fails
                filtered_emails.append(email)
        
        logger.info(f"AI filtering completed: {len(filtered_emails)} emails passed filter")
        return filtered_emails
    
    def fetch_priority_emails(self, priority_levels: List[str] = None) -> List[EmailData]:
        """Fetch emails filtered by AI-determined priority levels."""
        if priority_levels is None:
            priority_levels = ["critical", "high"]
        
        emails = self.fetch_emails_from_labels()
        priority_emails = []
        
        logger.info(f"Filtering {len(emails)} emails by priority levels: {priority_levels}")
        
        for email in emails:
            try:
                email_dict = {
                    "subject": email.subject,
                    "sender": email.sender,
                    "body": email.body[:500],
                    "attachments": email.attachments
                }
                
                analysis = self.llm_service.analyze_email_content(email_dict)
                email_priority = analysis.get("priority", "medium")
                
                if email_priority in priority_levels:
                    priority_emails.append(email)
                    logger.debug(f"Email {email.id} matches priority filter: {email_priority}")
                    
            except Exception as e:
                logger.warning(f"Priority filtering failed for email {email.id}: {e}")
        
        logger.info(f"Priority filtering completed: {len(priority_emails)} emails match criteria")
        return priority_emails
    
    # ==================== AUTOMATED PROCESSING WORKFLOWS ====================
    
    def auto_process_new_orders(self, 
                              auto_approve_threshold: float = 0.9,
                              create_myob_orders: bool = False) -> BatchProcessingResult:
        """Automatically process new order emails with high confidence."""
        
        logger.info("Starting automated order processing workflow")
        
        # Fetch emails that likely contain orders
        emails = self.fetch_emails_with_ai_filtering(
            ai_filter=True,
            min_confidence=0.8
        )
        
        # Filter for order-related emails
        order_emails = []
        for email in emails:
            try:
                email_dict = {
                    "subject": email.subject,
                    "sender": email.sender,
                    "body": email.body[:500],
                    "attachments": email.attachments
                }
                
                analysis = self.llm_service.analyze_email_content(email_dict)
                
                if (analysis.get("category") == "purchase_order" and 
                    analysis.get("contains_order", False) and
                    analysis.get("confidence_score", 0.0) >= auto_approve_threshold):
                    
                    order_emails.append(email)
                    logger.info(f"Email {email.id} qualified for auto-processing")
                    
            except Exception as e:
                logger.warning(f"Auto-processing filter failed for email {email.id}: {e}")
        
        # Process qualified emails
        batch_result = self.batch_process_emails(order_emails)
        
        # Optionally create MYOB orders for high-confidence extractions
        if create_myob_orders:
            self._create_myob_orders_from_results(batch_result.results, auto_approve_threshold)
        
        logger.info(f"Automated processing completed: {len(order_emails)} emails processed")
        return batch_result
    
    def _create_myob_orders_from_results(self, results: List[EmailProcessingResult], 
                                       confidence_threshold: float):
        """Create MYOB orders from high-confidence processing results."""
        from services.myob_service import MyobService
        
        myob_service = MyobService()
        created_orders = 0
        
        for result in results:
            if (result.processing_status == "success" and 
                result.extracted_orders and
                result.ai_analysis.get("confidence_score", 0.0) >= confidence_threshold):
                
                for order_data in result.extracted_orders:
                    try:
                        # Convert to ExtractedOrder object and create MYOB payload
                        # This would need proper conversion logic based on your models
                        logger.info(f"Would create MYOB order for email {result.email_id}")
                        created_orders += 1
                        
                    except Exception as e:
                        logger.error(f"Failed to create MYOB order for email {result.email_id}: {e}")
        
        logger.info(f"Created {created_orders} MYOB orders from automated processing")
    
    # ==================== ANALYTICS AND REPORTING ====================
    
    def get_processing_analytics(self, days_back: int = 30) -> Dict[str, Any]:
        """Get analytics on email processing performance."""
        
        # This would typically query a database of processing history
        # For now, return current session stats plus some mock historical data
        
        analytics = {
            "current_session": self.processing_stats.copy(),
            "historical_summary": {
                "total_emails_processed": self.processing_stats["emails_processed"] + 150,  # Mock data
                "total_orders_extracted": self.processing_stats["orders_extracted"] + 45,
                "average_processing_time": 2.3,
                "success_rate": 0.92,
                "most_common_categories": {
                    "purchase_order": 65,
                    "invoice": 25,
                    "general_business": 35,
                    "packing_slip": 20
                },
                "top_senders": {
                    "<EMAIL>": 30,
                    "<EMAIL>": 15,
                    "<EMAIL>": 12
                }
            },
            "performance_metrics": {
                "emails_per_hour": 25.5,
                "orders_per_hour": 8.2,
                "error_rate": 0.08,
                "ai_confidence_average": 0.87
            }
        }
        
        return analytics
    
    def generate_processing_report(self, results: List[EmailProcessingResult]) -> Dict[str, Any]:
        """Generate a comprehensive processing report."""
        
        if not results:
            return {"error": "No results to report on"}
        
        # Calculate statistics
        total_emails = len(results)
        successful_processing = sum(1 for r in results if r.processing_status == "success")
        total_orders = sum(len(r.extracted_orders) for r in results)
        total_processing_time = sum(r.processing_time or 0 for r in results)
        
        # Category breakdown
        categories = {}
        priorities = {}
        senders = {}
        
        for result in results:
            if result.ai_analysis:
                category = result.ai_analysis.get("category", "unknown")
                priority = result.ai_analysis.get("priority", "unknown")
                sender = result.email_data.sender
                
                categories[category] = categories.get(category, 0) + 1
                priorities[priority] = priorities.get(priority, 0) + 1
                senders[sender] = senders.get(sender, 0) + 1
        
        report = {
            "summary": {
                "total_emails": total_emails,
                "successful_processing": successful_processing,
                "success_rate": successful_processing / total_emails if total_emails > 0 else 0,
                "total_orders_extracted": total_orders,
                "average_processing_time": total_processing_time / total_emails if total_emails > 0 else 0
            },
            "breakdowns": {
                "by_category": categories,
                "by_priority": priorities,
                "by_sender": dict(sorted(senders.items(), key=lambda x: x[1], reverse=True)[:10])
            },
            "processing_details": [
                {
                    "email_id": r.email_id,
                    "subject": r.email_data.subject,
                    "sender": r.email_data.sender,
                    "status": r.processing_status,
                    "orders_found": len(r.extracted_orders),
                    "processing_time": r.processing_time,
                    "ai_category": r.ai_analysis.get("category", "unknown"),
                    "ai_priority": r.ai_analysis.get("priority", "unknown"),
                    "ai_confidence": r.ai_analysis.get("confidence_score", 0.0)
                }
                for r in results
            ]
        }
        
        return report
    
    # ==================== EMAIL MANAGEMENT WITH AI ====================
    
    def smart_label_emails(self, emails: List[EmailData]) -> Dict[str, List[str]]:
        """Automatically apply labels to emails based on AI analysis."""
        
        label_assignments = {
            "High Priority": [],
            "Orders": [],
            "Invoices": [],
            "Needs Review": [],
            "Processed": []
        }
        
        for email in emails:
            try:
                email_dict = {
                    "subject": email.subject,
                    "sender": email.sender,
                    "body": email.body[:500],
                    "attachments": email.attachments
                }
                
                analysis = self.llm_service.analyze_email_content(email_dict)
                
                # Apply labels based on analysis
                if analysis.get("priority") in ["critical", "high"]:
                    label_assignments["High Priority"].append(email.id)
                    self.add_label_to_email(email.id, "High Priority")
                
                if analysis.get("category") == "purchase_order":
                    label_assignments["Orders"].append(email.id)
                    self.add_label_to_email(email.id, "Orders")
                
                if analysis.get("category") == "invoice":
                    label_assignments["Invoices"].append(email.id)
                    self.add_label_to_email(email.id, "Invoices")
                
                if analysis.get("confidence_score", 1.0) < 0.7:
                    label_assignments["Needs Review"].append(email.id)
                    self.add_label_to_email(email.id, "Needs Review")
                
                if analysis.get("contains_order", False):
                    label_assignments["Processed"].append(email.id)
                    self.add_label_to_email(email.id, "Processed")
                
                logger.debug(f"Applied smart labels to email {email.id}")
                
            except Exception as e:
                logger.warning(f"Smart labeling failed for email {email.id}: {e}")
        
        logger.info(f"Smart labeling completed for {len(emails)} emails")
        return label_assignments
    
    def cleanup_processed_emails(self, days_old: int = 30) -> int:
        """Clean up old processed emails by archiving or removing labels."""
        
        # This would typically query for emails older than specified days
        # and remove processing labels or archive them
        
        logger.info(f"Cleaning up emails older than {days_old} days")
        
        # Mock cleanup operation
        cleaned_count = 0
        
        # In a real implementation, you would:
        # 1. Query for emails with "Processed" label older than days_old
        # 2. Remove processing labels
        # 3. Optionally archive emails
        # 4. Update processing statistics
        
        logger.info(f"Cleanup completed: {cleaned_count} emails processed")
        return cleaned_count
    
    # ==================== UTILITY METHODS ====================
    
    def get_service_status(self) -> Dict[str, Any]:
        """Get comprehensive service status including AI capabilities."""
        
        base_status = {
            "gmail_service": "connected" if self.service else "disconnected",
            "llm_service": "connected" if self.llm_service else "disconnected",
            "processing_stats": self.processing_stats,
            "capabilities": [
                "ai_email_analysis",
                "pdf_order_extraction", 
                "batch_processing",
                "smart_labeling",
                "automated_workflows",
                "analytics_reporting"
            ]
        }
        
        return base_status
    
    def reset_processing_stats(self):
        """Reset processing statistics."""
        self.processing_stats = {
            "emails_processed": 0,
            "orders_extracted": 0,
            "processing_errors": 0,
            "last_processing_time": None
        }
        logger.info("Processing statistics reset")