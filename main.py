#!/usr/bin/env python3
"""
Main entry point for TeamsysV0.1
Handles path setup and launches the enhanced CLI
"""
import sys
import os
from pathlib import Path

# Add all agent folders to Python path
root_dir = Path(__file__).parent
agent_folders = [
    'email_agent',
    'myob_agent', 
    'db_agent',
    'util_agent',
    'doc_agent'
]

for folder in agent_folders:
    folder_path = str(root_dir / folder)
    if folder_path not in sys.path:
        sys.path.insert(0, folder_path)

# Also add root directory
if str(root_dir) not in sys.path:
    sys.path.insert(0, str(root_dir))

def main():
    """Main entry point"""
    print("🚀 Starting TeamsysV0.1...")
    print("=" * 50)
    
    try:
        # Import and run the enhanced CLI
        from util_agent.enhanced_cli import main as cli_main
        cli_main()
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("\n🔧 Trying alternative entry points...")
        
        # Try teamsys CLI
        try:
            from util_agent.teamsys_cli import cli_main
            cli_main()
        except ImportError as e2:
            print(f"❌ Teamsys CLI error: {e2}")
            
            # Try simple email processing
            try:
                print("🔧 Trying email processing...")
                from email_agent.run_email_processing import run_email_processing
                import asyncio
                asyncio.run(run_email_processing())
            except ImportError as e3:
                print(f"❌ Email processing error: {e3}")
                print("\n💡 Available options:")
                print("1. Fix import paths in the reorganized code")
                print("2. Run individual components directly")
                print("3. Use Python -m module syntax")
                
                return False
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)