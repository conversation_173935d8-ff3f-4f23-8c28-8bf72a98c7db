#!/usr/bin/env python3
"""
TeamsysV0.1 Enhanced CLI - Unified command-line interface.
Provides email processing, MYOB management, and natural language querying.
"""
import argparse
import asyncio
import logging
import sys
from typing import Optional

from cli_handlers import CLIHandlers


def create_enhanced_parser():
    """Create the enhanced argument parser with subcommands."""
    parser = argparse.ArgumentParser(
        description="TeamsysV0.1 - Unified Email & MYOB Management System",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Launch interactive GUI
  python teamsys_cli.py interactive
  
  # Process emails
  python teamsys_cli.py email --once --labels Brady RSEA --max-emails 10
  
  # List pending MYOB orders
  python teamsys_cli.py myob list --format table
  
  # Process all MYOB orders in batch
  python teamsys_cli.py myob process --batch
  
  # Natural language query
  python teamsys_cli.py query "Show me all Woolworths orders from last week"
  
  # Check system status
  python teamsys_cli.py status
        """
    )
    
    # Global options
    parser.add_argument('--debug', action='store_true', help='Enable debug logging')
    parser.add_argument('--verbose', '-v', action='store_true', help='Enable verbose logging')
    parser.add_argument('--quiet', '-q', action='store_true', help='Suppress output (errors only)')
    parser.add_argument('--log-file', type=str, help='Write logs to specified file')
    
    # Create subparsers for different commands
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Interactive GUI command
    interactive_parser = subparsers.add_parser('interactive', help='Launch interactive GUI')
    interactive_parser.add_argument('--theme', choices=['dark', 'light'], default='dark', help='UI theme')
    
    # Email processing commands
    email_parser = subparsers.add_parser('email', help='Email processing commands')
    email_parser.add_argument('--once', action='store_true', help='Run once instead of continuous polling')
    email_parser.add_argument('--labels', nargs='+', 
                             default=['Brady', 'RSEA', 'Woolworths', 'Brierley', 'Gateway', 'Highgate', 'Sitecraft'],
                             help='Gmail labels to process')
    email_parser.add_argument('--max-emails', type=int, default=5, help='Maximum emails per label')
    email_parser.add_argument('--time-filter', type=str, help='Time filter (e.g., 7d, 2w, 24h)')
    email_parser.add_argument('--unread-only', action='store_true', help='Process only unread emails')
    email_parser.add_argument('--dry-run', action='store_true', help='Preview without processing')
    email_parser.add_argument('--export-csv', action='store_true', help='Export results to CSV')
    email_parser.add_argument('--export-json', action='store_true', help='Export results to JSON')
    
    # MYOB management commands
    myob_parser = subparsers.add_parser('myob', help='MYOB management commands')
    myob_subparsers = myob_parser.add_subparsers(dest='myob_action', help='MYOB actions')
    
    # MYOB list orders
    list_parser = myob_subparsers.add_parser('list', help='List pending orders')
    list_parser.add_argument('--format', choices=['table', 'json', 'csv'], default='table', help='Output format')
    
    # MYOB process orders
    process_parser = myob_subparsers.add_parser('process', help='Process orders')
    process_parser.add_argument('--batch', action='store_true', help='Process all orders')
    process_parser.add_argument('--order-ids', nargs='+', help='Specific order IDs to process')
    process_parser.add_argument('--validate-only', action='store_true', help='Only validate, don\'t post')
    
    # MYOB statistics
    stats_parser = myob_subparsers.add_parser('stats', help='Show MYOB statistics')
    stats_parser.add_argument('--export', action='store_true', help='Export statistics to file')
    
    # Natural language query commands
    query_parser = subparsers.add_parser('query', help='Natural language MYOB queries')
    query_parser.add_argument('query_text', help='Natural language query')
    query_parser.add_argument('--format', choices=['text', 'json', 'table'], default='text', help='Output format')
    query_parser.add_argument('--save-results', help='Save results to file')
    
    # System status commands
    status_parser = subparsers.add_parser('status', help='System status and health')
    status_parser.add_argument('--export', action='store_true', help='Export status to JSON file')
    status_parser.add_argument('--health-check', action='store_true', help='Perform comprehensive health check')
    
    return parser


def setup_logging(args):
    """Setup logging based on CLI arguments."""
    if args.debug:
        level = logging.DEBUG
    elif args.verbose:
        level = logging.INFO
    elif args.quiet:
        level = logging.ERROR
    else:
        level = logging.WARNING
    
    # Configure root logger
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Setup file logging if specified
    if args.log_file:
        file_handler = logging.FileHandler(args.log_file)
        file_handler.setLevel(level)
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)
        logging.getLogger().addHandler(file_handler)
        print(f"Logging to file: {args.log_file}")


async def main():
    """Main entry point for enhanced CLI."""
    parser = create_enhanced_parser()
    args = parser.parse_args()
    
    # Show help if no command specified
    if not args.command:
        parser.print_help()
        return
    
    # Setup logging
    setup_logging(args)
    
    try:
        # Initialize CLI handlers
        handlers = CLIHandlers()
        
        # Route to appropriate handler
        if args.command == 'interactive':
            handlers.handle_interactive_command(args)
            
        elif args.command == 'email':
            await handlers.handle_email_command(args)
            
        elif args.command == 'myob':
            await handlers.handle_myob_command(args)
            
        elif args.command == 'query':
            await handlers.handle_query_command(args)
            
        elif args.command == 'status':
            handlers.handle_status_command(args)
            
        else:
            print(f"Unknown command: {args.command}")
            parser.print_help()
            
    except KeyboardInterrupt:
        print("\n\n👋 Operation cancelled by user")
    except Exception as e:
        if args.debug:
            import traceback
            traceback.print_exc()
        else:
            print(f"❌ Error: {str(e)}")
        sys.exit(1)


def cli_main():
    """Synchronous entry point for CLI."""
    asyncio.run(main())


if __name__ == "__main__":
    cli_main()