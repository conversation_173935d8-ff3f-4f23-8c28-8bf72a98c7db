"""
Test script to verify G<PERSON> and MYOB query agents work with conversational responses.
"""
import asyncio
import logging
import sys
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent))

from agents.gmail_query_agent import GmailQueryAgent
from agents.myob_query_agent import MYOBQueryAgent

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_gmail_agent_conversational():
    """Test Gmail Query Agent with conversational responses."""
    print("\n📧 Testing Gmail Query Agent (Conversational Mode)")
    print("=" * 60)
    
    try:
        gmail_agent = GmailQueryAgent()
        
        # Test queries that should return conversational responses
        test_queries = [
            "Show me my Gmail labels",
            "Find unread emails from today",
            "Who sends me the most emails?"
        ]
        
        for query in test_queries:
            print(f"\n🔍 Query: '{query}'")
            print("📊 Testing intent classification...")
            
            # Test intent classification (should be conversational now)
            intent = await gmail_agent._classify_gmail_intent(query)
            
            if "llm_response" in intent:
                print(f"✅ Conversational response: {intent['llm_response'][:100]}...")
            else:
                print(f"⚠️ No conversational response found")
            
            print(f"📋 Detected intent: {intent.get('primary_intent', 'unknown')}")
        
        print("\n✅ Gmail Agent conversational mode test completed")
        
    except Exception as e:
        print(f"❌ Gmail Agent test failed: {e}")
        logger.error(f"Gmail Agent test failed: {e}")


async def test_myob_agent_conversational():
    """Test MYOB Query Agent with conversational responses."""
    print("\n🏢 Testing MYOB Query Agent (Conversational Mode)")
    print("=" * 60)
    
    try:
        myob_agent = MYOBQueryAgent()
        
        # Test queries that should return conversational responses
        test_queries = [
            "Show me all customers from Woolworths",
            "What orders do we have today?",
            "Find stock levels for product ABC123"
        ]
        
        for query in test_queries:
            print(f"\n🔍 Query: '{query}'")
            print("📊 Testing intent classification...")
            
            # Test intent classification (should be conversational now)
            intent = await myob_agent._classify_query_intent(query)
            
            if "llm_response" in intent:
                print(f"✅ Conversational response: {intent['llm_response'][:100]}...")
            else:
                print(f"⚠️ No conversational response found")
            
            print(f"📋 Detected intent: {intent.get('primary_intent', 'unknown')}")
        
        print("\n✅ MYOB Agent conversational mode test completed")
        
    except Exception as e:
        print(f"❌ MYOB Agent test failed: {e}")
        logger.error(f"MYOB Agent test failed: {e}")


async def test_enhanced_cli_integration():
    """Test that agents integrate properly with Enhanced CLI."""
    print("\n🔧 Testing Enhanced CLI Integration")
    print("=" * 50)
    
    try:
        # Test imports
        from enhanced_cli import TeamsysApp
        from gmail_query_tab import GmailQueryTab
        from enhanced_system_status_tab import EnhancedSystemStatusTab
        
        print("✅ All Enhanced CLI components import successfully")
        
        # Test agent initialization
        gmail_agent = GmailQueryAgent()
        myob_agent = MYOBQueryAgent()
        
        print("✅ Both query agents initialize successfully")
        
        # Test example queries
        gmail_examples = gmail_agent.get_example_queries()
        myob_examples = myob_agent.get_example_queries()
        
        print(f"✅ Gmail Agent: {len(gmail_examples)} example queries")
        print(f"✅ MYOB Agent: {len(myob_examples)} example queries")
        
        print("\n📋 Sample Gmail Examples:")
        for i, example in enumerate(gmail_examples[:3], 1):
            print(f"  {i}. {example}")
        
        print("\n📋 Sample MYOB Examples:")
        for i, example in enumerate(myob_examples[:3], 1):
            print(f"  {i}. {example}")
        
        print("\n✅ Enhanced CLI integration test completed")
        
    except Exception as e:
        print(f"❌ Enhanced CLI integration test failed: {e}")
        logger.error(f"Enhanced CLI integration test failed: {e}")


async def main():
    """Run all conversational agent tests."""
    print("🚀 Testing Conversational Query Agents")
    print("=" * 70)
    
    # Test individual agents
    await test_gmail_agent_conversational()
    await test_myob_agent_conversational()
    await test_enhanced_cli_integration()
    
    print("\n" + "=" * 70)
    print("✅ All conversational agent tests completed!")
    print("\n🎯 Key Changes Made:")
    print("  📧 Gmail Agent: Now uses conversational responses instead of JSON")
    print("  🏢 MYOB Agent: Now uses conversational responses instead of JSON")
    print("  ⚡ Both agents: Use generate_content_flexible() for natural language")
    print("  🔧 Enhanced CLI: Fully integrated with conversational agents")
    print("\n🚀 Ready to use:")
    print("  python enhanced_cli.py")


if __name__ == "__main__":
    asyncio.run(main())