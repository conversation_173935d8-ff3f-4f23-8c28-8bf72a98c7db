"""Constants and configuration for LLM service."""

from typing import Final

# Service configuration
DEFAULT_TIMEOUT: Final[int] = 30
DEFAULT_MAX_RETRIES: Final[int] = 3
DEFAULT_CACHE_TTL: Final[int] = 3600  # 1 hour

# LLM configuration - Updated based on MistralAI documentation
MISTRAL_MODEL_NAME: Final[str] = "mistral-medium-latest"  # Use latest for best performance
MAX_TOKENS: Final[int] = 4000  # Balanced for performance and completeness
TEMPERATURE: Final[float] = 0.1  # Low temperature for deterministic JSON output
MISTRAL_API_BASE_URL: Final[str] = "https://api.mistral.ai/v1"
MISTRAL_REQUEST_TIMEOUT: Final[int] = 90  # Increased timeout for network resilience

# JSON Mode configuration
USE_JSON_MODE: Final[bool] = True  # Enable JSON mode for structured output
JSON_RETRY_ATTEMPTS: Final[int] = 3  # Number of attempts for JSON extraction

# Validation rules
MAX_ORDER_LINES: Final[int] = 100
MAX_CUSTOMER_NAME_LENGTH: Final[int] = 255
MAX_ADDRESS_LENGTH: Final[int] = 500

# Cache keys
CACHE_KEY_PREFIX: Final[str] = "llm_service"
CONTEXT_CACHE_TTL: Final[int] = 1800  # 30 minutes

# Rate limiting
MAX_REQUESTS_PER_MINUTE: Final[int] = 60
RATE_LIMIT_WINDOW: Final[int] = 60  # seconds

# Error messages
ERROR_LLM_PROVIDER_UNAVAILABLE = "LLM provider is unavailable"
ERROR_INVALID_JSON_RESPONSE = "Invalid JSON response from LLM"
ERROR_VALIDATION_FAILED = "Data validation failed"
ERROR_MEMORY_SERVICE_UNAVAILABLE = "Memory service is unavailable"