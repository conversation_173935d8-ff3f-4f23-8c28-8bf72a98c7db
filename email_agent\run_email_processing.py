#!/usr/bin/env python3
"""
Simple script to initialize and run email processing.
"""
import asyncio
import logging
from datetime import datetime

from main_processor import EmailOrderProcessor

async def run_email_processing():
    """Initialize and run email processing."""
    
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    print("🚀 Initializing TeamsysV0.1 Email Processing...")
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    try:
        # Initialize the email processor
        processor = EmailOrderProcessor()
        print("✅ Email processor initialized successfully")
        
        # Configuration
        labels_to_process = ['Brady', 'RSEA', 'Woolworths', 'Brierley', 'Gateway']
        max_emails_per_label = 5
        
        print(f"📋 Processing labels: {', '.join(labels_to_process)}")
        print(f"📊 Max emails per label: {max_emails_per_label}")
        print("=" * 60)
        
        # Run the extraction workflow
        processed_orders = await processor.run_extraction_workflow(
            label_names=labels_to_process,
            max_emails=max_emails_per_label,
            time_filter=None,  # No time filter
            review_mode=False  # Only process emails with PDF attachments
        )
        
        # Display results
        print("\n" + "=" * 60)
        print("📊 PROCESSING RESULTS")
        print("=" * 60)
        
        if processed_orders:
            print(f"✅ Successfully processed {len(processed_orders)} orders")
            
            for i, order in enumerate(processed_orders, 1):
                print(f"\n{i}. Order: {order.order_id}")
                print(f"   Customer: {order.customer_name}")
                print(f"   PO Number: {order.customer_po}")
                print(f"   Lines: {len(order.order_lines)}")
                print(f"   Status: {'✅ Success' if order.success else '❌ Failed'}")
                
                if not order.success and order.error_message:
                    print(f"   Error: {order.error_message}")
        else:
            print("📭 No orders were processed")
            print("💡 This could mean:")
            print("   - No new emails in the specified labels")
            print("   - No emails with PDF attachments")
            print("   - All emails already processed")
        
        print("\n" + "=" * 60)
        print("🎉 Email processing completed!")
        
        return processed_orders
        
    except Exception as e:
        print(f"\n❌ Email processing failed: {str(e)}")
        logging.error(f"Email processing failed: {e}")
        import traceback
        traceback.print_exc()
        return []

def main():
    """Main entry point."""
    try:
        # Run the async email processing
        processed_orders = asyncio.run(run_email_processing())
        
        # Exit with appropriate code
        if processed_orders:
            print(f"\n✅ Completed successfully with {len(processed_orders)} orders processed")
            exit(0)
        else:
            print(f"\n⚠️ Completed with no orders processed")
            exit(0)
            
    except KeyboardInterrupt:
        print("\n\n👋 Processing cancelled by user")
        exit(1)
    except Exception as e:
        print(f"\n❌ Fatal error: {e}")
        exit(1)

if __name__ == "__main__":
    main()