#!/usr/bin/env python3
"""
LLM Service Runner
This script runs the LLM service as a standalone application with multiple modes.
"""

import asyncio
import logging
import json
import sys
from datetime import datetime
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Import the LLM service
from llm_service import LLMService

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('llm_service.log')
    ]
)

logger = logging.getLogger(__name__)

class LLMServiceRunner:
    """Main runner for the LLM service."""
    
    def __init__(self):
        self.service = None
        self.running = False
    
    async def initialize(self):
        """Initialize the LLM service."""
        try:
            self.service = LLMService()
            await self.service.__aenter__()
            logger.info("✅ LLM Service initialized successfully")
            return True
        except Exception as e:
            logger.error(f"❌ Failed to initialize LLM service: {e}")
            return False
    
    async def cleanup(self):
        """Cleanup the LLM service."""
        if self.service:
            await self.service.__aexit__(None, None, None)
            logger.info("🧹 LLM Service cleaned up")
    
    async def health_check(self):
        """Perform health check."""
        if not self.service:
            return {"status": "error", "message": "Service not initialized"}
        
        try:
            health = await self.service.health_check()
            return {
                "status": "healthy" if all(health.values()) else "degraded",
                "services": health,
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            return {"status": "error", "message": str(e)}
    
    async def process_text(self, content: str):
        """Process text content for order extraction."""
        try:
            result = await self.service.extract_order_from_content(content)
            if result:
                # Generate MYOB payload
                myob_payload = self.service.generate_myob_payload(result)
                return {
                    "success": True,
                    "order_data": result,
                    "myob_payload": myob_payload
                }
            else:
                return {
                    "success": False,
                    "message": "No order data found in content"
                }
        except Exception as e:
            logger.error(f"Error processing text: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def process_email(self, email_body: str, subject: str, sender: str):
        """Process email content."""
        try:
            result = await self.service.process_email(
                email_body=email_body,
                subject=subject,
                sender=sender,
                email_id=f"manual-{datetime.now().timestamp()}"
            )
            return {
                "success": True,
                "result": result
            }
        except Exception as e:
            logger.error(f"Error processing email: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def generate_summary(self, email_body: str, subject: str, sender: str):
        """Generate email summary."""
        try:
            summary = await self.service.generate_markdown_summary(
                email_body=email_body,
                subject=subject,
                sender=sender
            )
            return {
                "success": True,
                "summary": summary
            }
        except Exception as e:
            logger.error(f"Error generating summary: {e}")
            return {
                "success": False,
                "error": str(e)
            }

async def interactive_mode():
    """Run in interactive mode."""
    print("🚀 LLM Service - Interactive Mode")
    print("=" * 50)
    
    runner = LLMServiceRunner()
    
    if not await runner.initialize():
        print("❌ Failed to initialize service")
        return
    
    try:
        while True:
            print("\n📋 Available Commands:")
            print("1. Health Check")
            print("2. Process Text (Order Extraction)")
            print("3. Process Email")
            print("4. Generate Summary")
            print("5. Exit")
            
            choice = input("\n👉 Enter your choice (1-5): ").strip()
            
            if choice == "1":
                print("\n🏥 Performing health check...")
                health = await runner.health_check()
                print(json.dumps(health, indent=2))
            
            elif choice == "2":
                print("\n📝 Enter text content (press Enter twice to finish):")
                lines = []
                while True:
                    line = input()
                    if line == "" and lines and lines[-1] == "":
                        break
                    lines.append(line)
                
                content = "\n".join(lines[:-1])  # Remove last empty line
                if content.strip():
                    print("\n🔍 Processing text...")
                    result = await runner.process_text(content)
                    print(json.dumps(result, indent=2, default=str))
                else:
                    print("❌ No content provided")
            
            elif choice == "3":
                subject = input("\n📧 Email Subject: ").strip()
                sender = input("📧 Email Sender: ").strip()
                print("📧 Email Body (press Enter twice to finish):")
                
                lines = []
                while True:
                    line = input()
                    if line == "" and lines and lines[-1] == "":
                        break
                    lines.append(line)
                
                email_body = "\n".join(lines[:-1])
                if email_body.strip():
                    print("\n📨 Processing email...")
                    result = await runner.process_email(email_body, subject, sender)
                    print(json.dumps(result, indent=2, default=str))
                else:
                    print("❌ No email content provided")
            
            elif choice == "4":
                subject = input("\n📧 Email Subject: ").strip()
                sender = input("📧 Email Sender: ").strip()
                print("📧 Email Body (press Enter twice to finish):")
                
                lines = []
                while True:
                    line = input()
                    if line == "" and lines and lines[-1] == "":
                        break
                    lines.append(line)
                
                email_body = "\n".join(lines[:-1])
                if email_body.strip():
                    print("\n📝 Generating summary...")
                    result = await runner.generate_summary(email_body, subject, sender)
                    if result["success"]:
                        print("\n📄 Summary:")
                        print(result["summary"])
                    else:
                        print(f"❌ Error: {result['error']}")
                else:
                    print("❌ No email content provided")
            
            elif choice == "5":
                print("\n👋 Goodbye!")
                break
            
            else:
                print("❌ Invalid choice. Please enter 1-5.")
    
    except KeyboardInterrupt:
        print("\n\n👋 Interrupted by user")
    
    finally:
        await runner.cleanup()

async def batch_mode(input_file: str, output_file: str = None):
    """Run in batch mode processing a file."""
    print(f"🔄 LLM Service - Batch Mode")
    print(f"📁 Input file: {input_file}")
    
    if not Path(input_file).exists():
        print(f"❌ Input file not found: {input_file}")
        return
    
    runner = LLMServiceRunner()
    
    if not await runner.initialize():
        print("❌ Failed to initialize service")
        return
    
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print("🔍 Processing content...")
        result = await runner.process_text(content)
        
        output_data = {
            "timestamp": datetime.now().isoformat(),
            "input_file": input_file,
            "result": result
        }
        
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(output_data, f, indent=2, default=str)
            print(f"✅ Results saved to: {output_file}")
        else:
            print("\n📊 Results:")
            print(json.dumps(output_data, indent=2, default=str))
    
    except Exception as e:
        print(f"❌ Error processing file: {e}")
    
    finally:
        await runner.cleanup()

async def daemon_mode():
    """Run as a daemon service (placeholder for future web API)."""
    print("🔄 LLM Service - Daemon Mode")
    print("⚠️  Web API mode not implemented yet")
    print("💡 Use interactive mode for now: python run_llm_service.py --interactive")

def main():
    """Main entry point."""
    import argparse
    
    parser = argparse.ArgumentParser(description="LLM Service Runner")
    parser.add_argument("--interactive", "-i", action="store_true", 
                       help="Run in interactive mode")
    parser.add_argument("--batch", "-b", metavar="INPUT_FILE",
                       help="Run in batch mode with input file")
    parser.add_argument("--output", "-o", metavar="OUTPUT_FILE",
                       help="Output file for batch mode")
    parser.add_argument("--daemon", "-d", action="store_true",
                       help="Run as daemon service")
    
    args = parser.parse_args()
    
    if args.interactive:
        asyncio.run(interactive_mode())
    elif args.batch:
        asyncio.run(batch_mode(args.batch, args.output))
    elif args.daemon:
        asyncio.run(daemon_mode())
    else:
        print("🎯 LLM Service Runner")
        print("=" * 30)
        print("Usage:")
        print("  python run_llm_service.py --interactive    # Interactive mode")
        print("  python run_llm_service.py --batch file.txt # Batch processing")
        print("  python run_llm_service.py --daemon         # Daemon mode")
        print("\nFor help: python run_llm_service.py --help")

if __name__ == "__main__":
    main()