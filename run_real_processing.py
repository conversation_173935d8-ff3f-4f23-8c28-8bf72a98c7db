#!/usr/bin/env python3
"""
Run REAL Enhanced Gmail Service processing on Brady emails.
No demos, no simulations - actual processing of real emails.
"""

from services.enhanced_gmail_service import EnhancedGmailService
import json

def main():
    print('🚀 Running REAL Enhanced Gmail Service on Brady emails...')
    
    # Initialize the service
    enhanced_gmail = EnhancedGmailService()
    
    # Fetch real Brady emails
    print('📧 Fetching Brady emails...')
    emails = enhanced_gmail.fetch_emails_with_ai_filtering(
        labels=['Brady'],
        days_back=7,
        ai_filter=True,
        min_confidence=0.7
    )
    
    print(f'Found {len(emails)} Brady emails')
    
    if not emails:
        print('❌ No emails found to process')
        return
    
    # Process the first real email
    email = emails[0]
    print(f'\n📧 Processing: {email.subject}')
    print(f'📤 From: {email.sender}')
    print(f'📎 Attachments: {len(email.attachments)}')
    
    # Run comprehensive processing
    print('\n🔄 Running comprehensive processing...')
    result = enhanced_gmail.comprehensive_email_processing(email, display_results=False)
    
    print(f'\n✅ Processing Results:')
    print(f'Status: {result.processing_status}')
    print(f'Processing time: {result.processing_time:.2f}s')
    print(f'Orders extracted: {len(result.extracted_orders)}')
    print(f'MYOB payloads: {len(result.myob_payloads or [])}')
    
    # Show AI analysis
    if result.ai_analysis:
        print(f'\n🤖 AI Analysis:')
        print(f'Category: {result.ai_analysis.get("category", "unknown")}')
        print(f'Priority: {result.ai_analysis.get("priority", "unknown")}')
        print(f'Contains Order: {result.ai_analysis.get("contains_order", "unknown")}')
    
    # Show PDF analyses
    if result.pdf_analyses:
        print(f'\n📄 PDF Analysis:')
        for pdf in result.pdf_analyses:
            print(f'File: {pdf.get("filename", "unknown")}')
            print(f'Type: {pdf.get("document_type", "unknown")}')
            print(f'Has Tables: {pdf.get("has_tables", False)}')
            print(f'Confidence: {pdf.get("confidence", 0):.1%}')
    
    # Show extracted orders
    if result.extracted_orders:
        print(f'\n📦 Extracted Orders:')
        for i, order in enumerate(result.extracted_orders):
            print(f'\nOrder {i+1} from {order.get("source", "unknown")}:')
            order_data = order.get('order_data', {})
            
            if 'customer_details' in order_data:
                customer = order_data['customer_details']
                print(f'  Customer ID: {customer.get("debtor_id", "N/A")}')
                print(f'  PO Number: {customer.get("customer_order_number", "N/A")}')
            
            if 'order_lines' in order_data:
                print(f'  Items: {len(order_data["order_lines"])} line items')
                for line in order_data["order_lines"][:3]:  # Show first 3
                    print(f'    - {line.get("stockcode", "N/A")}: {line.get("orderquantity", 0)} units')
            
            if 'X_SHIPVIA' in order_data:
                print(f'  Shipping: {order_data["X_SHIPVIA"]}')
    
    # Show MYOB payloads
    if result.myob_payloads:
        print(f'\n💼 MYOB Payloads Generated:')
        for i, payload_info in enumerate(result.myob_payloads):
            print(f'\nMYOB Payload {i+1} from {payload_info.get("source", "unknown")}:')
            payload = payload_info.get('payload', {})
            print(f'  Debtor ID: {payload.get("debtorid", "N/A")}')
            print(f'  PO Number: {payload.get("customerordernumber", "N/A")}')
            print(f'  Status: {payload.get("status", "N/A")}')
            print(f'  Lines: {len(payload.get("lines", []))} items')
            
            # Show full payload
            print(f'\nFull MYOB Payload:')
            print(json.dumps(payload, indent=2))
    
    # Show service stats
    stats = enhanced_gmail.processing_stats
    print(f'\n📊 Service Statistics:')
    print(f'Total emails processed: {stats["emails_processed"]}')
    print(f'Total orders extracted: {stats["orders_extracted"]}')
    print(f'Processing errors: {stats["processing_errors"]}')
    
    print('\n✅ Real processing completed!')

if __name__ == "__main__":
    main()