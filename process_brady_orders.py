#!/usr/bin/env python3
"""
Process Brady emails for real order extraction and MYOB payload generation.
Focus on email body text processing when PDF extraction fails.
"""

from services.enhanced_gmail_service import EnhancedGmailService
from services.llm_service import LLMService
import json

def main():
    print('🚀 Processing Brady Orders - REAL EXTRACTION')
    
    # Initialize services
    enhanced_gmail = EnhancedGmailService()
    llm_service = LLMService()
    
    # Fetch Brady emails
    print('📧 Fetching Brady emails...')
    emails = enhanced_gmail.fetch_emails_with_ai_filtering(
        labels=['Brady'],
        days_back=14,  # Look back further
        ai_filter=True,
        min_confidence=0.6  # Lower threshold to get more emails
    )
    
    print(f'Found {len(emails)} Brady emails')
    
    if not emails:
        print('❌ No emails found')
        return
    
    # Process multiple emails to find orders
    orders_found = 0
    payloads_generated = 0
    
    for i, email in enumerate(emails[:5]):  # Process first 5 emails
        print(f'\n📧 Processing Email {i+1}: {email.subject}')
        print(f'From: {email.sender}')
        print(f'Body length: {len(email.body)} characters')
        
        # Try to extract order from email body directly
        print('🔄 Extracting order from email body...')
        
        # Combine email subject and body for better context
        full_content = f"Subject: {email.subject}\n\nFrom: {email.sender}\n\nBody:\n{email.body}"
        
        # Extract order data using LLM
        order_data = llm_service.extract_order_data(full_content)
        
        if order_data:
            orders_found += 1
            print(f'✅ Order extracted from email body!')
            
            # Display extracted order
            print(f'\n📦 Order Details:')
            if 'customer_details' in order_data:
                customer = order_data['customer_details']
                print(f'  Customer ID: {customer.get("debtor_id", "N/A")}')
                print(f'  PO Number: {customer.get("customer_order_number", "N/A")}')
            
            if 'order_lines' in order_data:
                print(f'  Order Lines: {len(order_data["order_lines"])} items')
                for j, line in enumerate(order_data["order_lines"][:3]):
                    print(f'    {j+1}. {line.get("stockcode", "N/A")}: {line.get("orderquantity", 0)} units')
            
            if 'X_SHIPVIA' in order_data:
                print(f'  Shipping: {order_data["X_SHIPVIA"]}')
            
            # Generate MYOB payload
            try:
                from models import ExtractedOrder, CustomerDetails, OrderLine
                
                customer_details = CustomerDetails(
                    debtor_id=order_data["customer_details"]["debtor_id"],
                    customer_order_number=order_data["customer_details"].get("customer_order_number")
                )
                
                order_lines = [
                    OrderLine(
                        stockcode=line["stockcode"],
                        orderquantity=line["orderquantity"]
                    ) for line in order_data["order_lines"]
                ]
                
                extracted_order = ExtractedOrder(
                    customer_details=customer_details,
                    order_lines=order_lines,
                    X_SHIPVIA=order_data.get("X_SHIPVIA"),
                    order_status=order_data.get("order_status", 3)
                )
                
                # Generate MYOB payload
                myob_payload = llm_service.generate_myob_payload_direct(extracted_order)
                payloads_generated += 1
                
                print(f'\n💼 MYOB Payload Generated:')
                print(json.dumps(myob_payload, indent=2))
                
                # Save payload to file
                filename = f"myob/brady_order_{email.id[:8]}_{customer.get('customer_order_number', 'unknown')}.json"
                with open(filename, 'w') as f:
                    json.dump(myob_payload, f, indent=2)
                print(f'💾 Saved to: {filename}')
                
            except Exception as e:
                print(f'⚠️  Failed to generate MYOB payload: {e}')
        
        else:
            print('❌ No order data extracted from email body')
            
            # Show first 200 chars of email body for debugging
            print(f'Email body preview: {email.body[:200]}...')
    
    print(f'\n📊 Processing Summary:')
    print(f'Emails processed: {len(emails[:5])}')
    print(f'Orders found: {orders_found}')
    print(f'MYOB payloads generated: {payloads_generated}')
    
    if orders_found > 0:
        print(f'\n✅ SUCCESS: Found {orders_found} real orders and generated {payloads_generated} MYOB payloads!')
    else:
        print(f'\n⚠️  No orders found. This might be because:')
        print(f'   - Brady emails contain order info in PDFs only')
        print(f'   - Email body text needs different parsing approach')
        print(f'   - Need to fix PDF text extraction issue')

if __name__ == "__main__":
    main()