"""
Test script for the full order extraction and MYOB payload generation workflow.
"""
import asyncio
import logging
import json
from llm_service.main import LLMService
from llm_service.processors.order_processor import OrderProcessor
from llm_service.processors.payload_generator import PayloadGenerator
from llm_service.services.mistral_service import MistralService
from llm_service.services.memory_service import MemoryService
from models import ExtractedOrder

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

async def test_full_workflow():
    """Test the full order extraction and MYOB payload generation workflow."""
    logger.info("Initializing services...")
    
    # Initialize services
    mistral_service = MistralService()
    memory_service = MemoryService()
    order_processor = OrderProcessor(mistral_service, memory_service, None)
    payload_generator = PayloadGenerator()
    
    # Sample content from a purchase order
    sample_content = """
    PURCHASE ORDER
    
    Order Number: 4506049832
    Order Date: 2025-07-20
    Delivery Date: 2025-08-01
    
    Vendor: Team Systems
    Vendor Number: 12345
    
    Bill To:
    Woolworths Limited
    1 Woolworths Way
    Bella Vista NSW 2153
    
    Ship To:
    Woolworths Distribution Center
    120 Industrial Road
    Eastern Creek NSW 2766
    
    Line Items:
    1. Item: TS-WHEEL-100MM, Description: 100mm Castor Wheel, Quantity: 50
    2. Item: TS-HANDLE-SS, Description: Stainless Steel Handle, Quantity: 25
    3. Item: TS-BRACKET-L, Description: L-Bracket Support, Quantity: 100
    
    Shipping Instructions: Standard Delivery
    Special Instructions: Please include test certificates
    """
    
    logger.info("Testing order extraction...")
    
    # Extract order data
    extracted_data = await order_processor.extract_order_data(sample_content, "<EMAIL>")
    
    if extracted_data:
        logger.info("Extraction successful!")
        logger.info(f"Extracted data structure: {list(extracted_data.keys())}")
        
        if "customer_details" in extracted_data:
            logger.info(f"Customer: {extracted_data['customer_details'].get('customer_name', 'Unknown')}")
            logger.info(f"Debtor ID: {extracted_data['customer_details'].get('debtor_id', 'None')}")
        
        if "order_lines" in extracted_data:
            logger.info(f"Order lines: {len(extracted_data['order_lines'])}")
            for line in extracted_data['order_lines']:
                logger.info(f"  - {line.get('stockcode', 'Unknown')}: {line.get('orderquantity', 0)}")
        
        # Validate the extracted order
        logger.info("Validating extracted order...")
        await order_processor.validate_extracted_order(extracted_data, "<EMAIL>")
        
        # Create ExtractedOrder object
        extracted_order = ExtractedOrder(**extracted_data)
        
        # Generate MYOB payload
        logger.info("Generating MYOB payload...")
        myob_payload = payload_generator.create_myob_payload(extracted_order)
        
        # Print the MYOB payload
        logger.info("MYOB Payload:")
        payload_dict = myob_payload.dict()
        logger.info(f"Debtor ID: {payload_dict.get('debtorid', 'None')}")
        logger.info(f"Customer Order Number: {payload_dict.get('customerordernumber', 'None')}")
        logger.info(f"Lines: {len(payload_dict.get('lines', []))}")
        
        # Save the payload to a file
        with open("test_myob_payload.json", "w") as f:
            json.dump(payload_dict, f, indent=2)
        logger.info("Saved MYOB payload to test_myob_payload.json")
        
    else:
        logger.error("Extraction failed!")

async def main():
    """Main entry point."""
    try:
        await test_full_workflow()
    except Exception as e:
        logger.error(f"Test failed: {e}")

if __name__ == "__main__":
    asyncio.run(main())