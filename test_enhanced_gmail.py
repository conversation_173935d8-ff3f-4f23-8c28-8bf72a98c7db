#!/usr/bin/env python3
"""
Simple test for Enhanced Gmail Service with Brady emails.
"""

from services.enhanced_gmail_service import EnhancedGmailService

def main():
    print('🚀 Testing Enhanced Gmail Service with Brady emails...')
    
    # Initialize service
    enhanced_gmail = EnhancedGmailService()
    
    # Test AI-powered email fetching with Brady label
    print('🔄 Fetching Brady emails with AI filtering...')
    filtered_emails = enhanced_gmail.fetch_emails_with_ai_filtering(
        labels=['Brady'],
        days_back=7,
        ai_filter=True,
        min_confidence=0.7
    )
    
    print(f'✅ Found {len(filtered_emails)} Brady emails passing AI filter')
    
    if filtered_emails:
        # Test single email processing
        print('🔄 Processing first Brady email with AI...')
        result = enhanced_gmail.process_email_with_ai(filtered_emails[0])
        
        print(f'✅ Processing completed: Status={result.processing_status}')
        category = result.ai_analysis.get('category', 'unknown')
        print(f'📊 AI Category: {category}')
        print(f'📦 Orders found: {len(result.extracted_orders)}')
        
        # Show service stats
        stats = enhanced_gmail.processing_stats
        print(f'📈 Service Stats: {stats["emails_processed"]} processed, {stats["orders_extracted"]} orders')
    
    print('🎉 Enhanced Gmail Service test completed!')

if __name__ == "__main__":
    main()