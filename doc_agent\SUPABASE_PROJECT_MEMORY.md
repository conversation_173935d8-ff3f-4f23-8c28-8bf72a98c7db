# TeamsysV0.1 Supabase Project Memory

**Generated on:** July 9, 2025  
**Project ID:** `sqtiatvgqwpgwavrzuqz`  
**Project Name:** TeamsysV0.1  

## 🔗 Project Connection Details

- **Project URL:** https://sqtiatvgqwpgwavrzuqz.supabase.co
- **Anonymous Key:** `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.8FdPi4o9RXXLxNHEOEcW8vwSO-X06TNVbDLnkQZFUok`
- **Database Host:** db.sqtiatvgqwpgwavrzuqz.supabase.co
- **Region:** ap-southeast-2 (Asia Pacific - Sydney)
- **Status:** ✅ ACTIVE_HEALTHY
- **PostgreSQL Version:** **********
- **Created:** July 4, 2025

## 📊 Database Schema Overview

### Core Tables (8 tables)

#### 1. `customers` - Central Customer Master Data
- **Size:** 2.1 MB (13,091 records)
- **Primary Key:** `debtor_id` (integer)
- **Purpose:** Central customer master data linked to all interactions
- **Columns:**
  - `debtor_id` (integer, NOT NULL) - Primary key
  - `customer_name` (varchar, NOT NULL)
  - `email` (varchar)
  - `phone` (varchar)
  - `address_line1-4` (varchar)
  - `contact_person` (varchar)
  - `payment_terms` (varchar)
  - `created_at`, `updated_at` (timestamptz)

#### 2. `emails` - Gmail Integration
- **Size:** 56 KB (0 records currently)
- **Primary Key:** `id` (uuid)
- **Purpose:** All processed emails from Gmail Inbox with categorization
- **Key Features:**
  - Links to customers via `debtor_id`
  - Stores both plain text and HTML body
  - Tracks attachments and processing metadata
- **Columns:**
  - `id` (uuid, primary key)
  - `gmail_id` (varchar, unique) - Gmail message ID
  - `subject`, `sender`, `recipient` (varchar/text)
  - `body`, `html_body` (text)
  - `category` (varchar) - Email categorization
  - `debtor_id` (integer, FK to customers)
  - `thread_id`, `label_ids` (varchar, array)
  - `received_date`, `processed_date` (timestamptz)
  - `has_attachments`, `attachment_count` (boolean, integer)
  - `confidence_score` (numeric)

#### 3. `email_attachments` - File Processing
- **Size:** 16 KB (0 records)
- **Purpose:** Track email attachments and extracted content
- **Columns:**
  - `id` (uuid, primary key)
  - `email_id` (uuid, FK to emails)
  - `filename`, `content_type` (varchar)
  - `size_bytes` (integer)
  - `attachment_id` (varchar) - Gmail attachment ID
  - `processed` (boolean)
  - `extracted_text` (text) - PDF/document text extraction

#### 4. `email_categorizations` - AI Processing
- **Size:** 16 KB (0 records)
- **Purpose:** AI-powered email categorization and data extraction
- **Columns:**
  - `id` (uuid, primary key)
  - `email_id` (uuid, FK to emails)
  - `category`, `subcategory` (varchar)
  - `confidence_score` (numeric)
  - `extracted_data` (jsonb) - Structured data from AI
  - `model_used` (varchar)
  - `processing_time_ms` (integer)

#### 5. `email_events` - Audit Trail
- **Size:** 40 KB (0 records)
- **Purpose:** Audit trail of all email processing events
- **Columns:**
  - `id` (uuid, primary key)
  - `email_id` (uuid, FK to emails)
  - `debtor_id` (integer, FK to customers)
  - `event_type` (varchar, NOT NULL)
  - `event_data` (jsonb)
  - `agent_name` (varchar)
  - `status` (varchar, default 'pending')
  - `error_message` (text)

#### 6. `erp_payloads` - MYOB Integration
- **Size:** 32 KB (0 records)
- **Purpose:** MYOB integration payloads with approval workflow
- **Key Features:**
  - Approval workflow: draft → pending_approval → approved → submitted
  - Links orders to customers and emails
- **Columns:**
  - `id` (uuid, primary key)
  - `email_id` (uuid, FK to emails)
  - `debtor_id` (integer, FK to customers, NOT NULL)
  - `payload_type` (varchar, NOT NULL)
  - `payload_data` (jsonb, NOT NULL)
  - `status` (varchar, default 'draft')
  - `approval_required` (boolean, default true)
  - `approved_by`, `approved_at`, `submitted_at` (varchar, timestamptz)
  - `erp_response` (jsonb)
  - `customer_order_number`, `po_number` (varchar)
  - `total_amount` (numeric)

#### 7. `order_line_items` - Order Details
- **Size:** 16 KB (0 records)
- **Purpose:** Individual line items for orders
- **Columns:**
  - `id` (uuid, primary key)
  - `email_id` (uuid, FK to emails)
  - `erp_payload_id` (uuid, FK to erp_payloads)
  - `line_number` (integer)
  - `stock_code` (varchar)
  - `description` (text)
  - `quantity`, `unit_price`, `line_total` (numeric)
  - `unit_of_measure` (varchar)

#### 8. `system_logs` - System Monitoring
- **Size:** 160 KB (8 records, 6 dead rows)
- **Purpose:** Comprehensive system logging
- **Columns:**
  - `id` (uuid, primary key)
  - `level` (varchar, NOT NULL) - Log level
  - `message` (text, NOT NULL)
  - `module`, `function_name` (varchar)
  - `email_id` (uuid, FK to emails)
  - `debtor_id` (integer, FK to customers)
  - `additional_data` (jsonb)

## 🔄 Data Relationships

### Key Foreign Key Relationships:
- **customers.debtor_id** ← Referenced by:
  - emails.debtor_id
  - email_events.debtor_id
  - erp_payloads.debtor_id
  - system_logs.debtor_id

- **emails.id** ← Referenced by:
  - email_attachments.email_id
  - email_categorizations.email_id
  - email_events.email_id
  - erp_payloads.email_id
  - order_line_items.email_id
  - system_logs.email_id

- **erp_payloads.id** ← Referenced by:
  - order_line_items.erp_payload_id

## 🔧 Extensions & Features

### Currently Installed Extensions:
1. **uuid-ossp** (v1.1) - UUID generation
2. **pg_stat_statements** (v1.11) - Query performance tracking
3. **pgcrypto** (v1.3) - Cryptographic functions
4. **supabase_vault** (v0.3.1) - Supabase Vault Extension
5. **pg_graphql** (v1.5.11) - GraphQL support
6. **plpgsql** (v1.0) - PL/pgSQL procedural language

### Available Extensions (70+ available):
- **vector** (v0.8.0) - Vector data type for AI/ML
- **pg_cron** (v1.6) - Job scheduler
- **postgis** (v3.3.7) - Spatial data support
- **http** (v1.6) - HTTP client
- **pg_net** (v0.14.0) - Async HTTP
- And many more...

## 📋 Current Status

### Development Status:
- **Migrations:** ✅ No custom migrations (using default setup)
- **Branches:** 📝 No development branches
- **Edge Functions:** 📝 No edge functions deployed
- **Data Population:** 📊 Customer data loaded (13K+ records), other tables empty

### Data Status:
- **customers:** 13,091 records (2.1 MB) ✅
- **emails:** 0 records (ready for Gmail integration) 📝
- **system_logs:** 8 records with some activity 📊
- **All other tables:** Empty and ready for use 📝

## ⚠️ Security Advisors

### 🔴 Critical Security Issues:
1. **RLS Disabled** - Row Level Security is disabled on ALL public tables:
   - customers, emails, email_attachments, email_categorizations
   - email_events, erp_payloads, order_line_items, system_logs
   - [Fix Guide](https://supabase.com/docs/guides/database/database-linter?lint=0013_rls_disabled_in_public)

2. **Security Definer View** - View `customer_email_summary` uses SECURITY DEFINER
   - [Fix Guide](https://supabase.com/docs/guides/database/database-linter?lint=0010_security_definer_view)

### 🟡 Security Warnings:
- **Function Search Path** - Function `update_updated_at_column` has mutable search_path
  - [Fix Guide](https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable)

## 📈 Performance Advisors

### 🔵 Optimization Opportunities:

#### Unindexed Foreign Keys (7 issues):
- `email_attachments.email_id`
- `email_categorizations.email_id`
- `erp_payloads.email_id`
- `order_line_items.email_id`
- `order_line_items.erp_payload_id`
- `system_logs.debtor_id`
- `system_logs.email_id`

#### Unused Indexes (9 indexes):
- `idx_emails_category`, `idx_emails_received_date`, `idx_emails_gmail_id`
- `idx_email_events_email_id`, `idx_email_events_debtor_id`, `idx_email_events_event_type`
- `idx_erp_payloads_status`
- `idx_system_logs_level`, `idx_system_logs_created_at`

## 🎯 Workflow Integration

### Email Processing Flow:
1. **Gmail Integration** → `emails` table
2. **AI Categorization** → `email_categorizations` table
3. **Attachment Processing** → `email_attachments` table
4. **Event Tracking** → `email_events` table
5. **Customer Linking** → `customers` table via `debtor_id`

### ERP Integration Flow:
1. **Order Creation** → `erp_payloads` table (draft status)
2. **Line Items** → `order_line_items` table
3. **Approval Workflow** → Status: draft → pending_approval → approved
4. **MYOB Submission** → `erp_response` field updated
5. **Audit Trail** → `system_logs` table

## 📝 Next Steps & Recommendations

### ✅ COMPLETED FIXES (July 9, 2025):

#### 🔧 Schema Alignment Fixes:
1. **Updated models.py** to match Supabase schema exactly
   - ✅ EmailData model now matches emails table
   - ✅ Added EmailAttachment, EmailCategorization, ERPPayload models
   - ✅ Added OrderLineItem model for order_line_items table
   - ✅ Fixed field mappings (timestamp → received_date, id → gmail_id)

2. **Created SupabaseService** (supabase_database_service.py)
   - ✅ Proper database operations for all tables
   - ✅ Complete workflow support (emails → categorizations → payloads → line items)
   - ✅ Error handling and logging

3. **Updated main_processor.py**
   - ✅ Integrated Supabase saving into email processing workflow
   - ✅ Now saves processed emails to database automatically
   - ✅ Proper error handling for database operations

4. **Created test script** (test_supabase_integration.py)
   - ✅ Tests complete workflow from emails to line items
   - ✅ Validates all table operations work correctly

### 🔄 Integration Status:
- **emails table:** ✅ Ready to receive data from Gmail integration
- **email_attachments table:** ✅ Integrated with email processing
- **email_categorizations table:** ✅ AI categorization will save here
- **erp_payloads table:** ✅ MYOB payloads will save with approval workflow
- **order_line_items table:** ✅ Order details will be properly stored
- **email_events table:** ✅ Processing events will be logged

### Immediate Actions Required:
1. **🔴 Enable RLS** on all public tables for security
2. **🔵 Add indexes** for foreign key columns to improve performance
3. **🔵 Review unused indexes** - remove if not needed

### Testing Your Fixes:
Run the test script to validate everything works:
```bash
python test_supabase_integration.py
```

### Development Recommendations:
1. **Set up development branch** for testing schema changes
2. **Implement proper RLS policies** for multi-tenant security
3. **Add performance indexes** based on query patterns
4. **Consider Edge Functions** for email processing workflows

### Integration Notes:
- Project is ready for Gmail API integration
- Customer data is populated and ready for email linking
- MYOB integration tables are prepared with approval workflow
- Comprehensive logging system is in place

---

**Last Updated:** July 9, 2025  
**Data Source:** Supabase MCP Integration  
**Status:** Active Development Environment
