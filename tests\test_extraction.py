"""
Test script for order extraction functionality.
"""
import asyncio
import logging
from llm_service.main import LLMService
from llm_service.processors.order_processor import OrderProcessor
from llm_service.services.mistral_service import MistralService
from llm_service.services.memory_service import MemoryService

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

async def test_extraction():
    """Test the order extraction functionality."""
    logger.info("Initializing services...")
    
    # Initialize services
    mistral_service = MistralService()
    memory_service = MemoryService()
    order_processor = OrderProcessor(mistral_service, memory_service, None)
    
    # Sample content from a purchase order
    sample_content = """
    PURCHASE ORDER
    
    Order Number: 4506049832
    Order Date: 2025-07-20
    Delivery Date: 2025-08-01
    
    Vendor: Team Systems
    Vendor Number: 12345
    
    Bill To:
    Woolworths Limited
    1 Woolworths Way
    Bella Vista NSW 2153
    
    Ship To:
    Woolworths Distribution Center
    120 Industrial Road
    Eastern Creek NSW 2766
    
    Line Items:
    1. Item: TS-WHEEL-100MM, Description: 100mm Castor Wheel, Quantity: 50
    2. Item: TS-HANDLE-SS, Description: Stainless Steel Handle, Quantity: 25
    3. Item: TS-BRACKET-L, Description: L-Bracket Support, Quantity: 100
    
    Shipping Instructions: Standard Delivery
    Special Instructions: Please include test certificates
    """
    
    logger.info("Testing order extraction...")
    
    # Extract order data
    extracted_data = await order_processor.extract_order_data(sample_content, "<EMAIL>")
    
    if extracted_data:
        logger.info("Extraction successful!")
        logger.info(f"Extracted data structure: {list(extracted_data.keys())}")
        
        if "customer_details" in extracted_data:
            logger.info(f"Customer: {extracted_data['customer_details'].get('customer_name', 'Unknown')}")
        
        if "order_lines" in extracted_data:
            logger.info(f"Order lines: {len(extracted_data['order_lines'])}")
            for line in extracted_data['order_lines']:
                logger.info(f"  - {line.get('stockcode', 'Unknown')}: {line.get('orderquantity', 0)}")
    else:
        logger.error("Extraction failed!")

async def main():
    """Main entry point."""
    try:
        await test_extraction()
    except Exception as e:
        logger.error(f"Test failed: {e}")

if __name__ == "__main__":
    asyncio.run(main())