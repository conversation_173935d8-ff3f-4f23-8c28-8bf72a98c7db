"""
Enhanced MYOB Manager - Advanced MYOB operations with CLI integration.
Provides formatted data for interactive displays and enhanced functionality.
"""
import logging
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import json

from myob_poster import MYOBPoster
from myob_service import MyobService

logger = logging.getLogger(__name__)


class EnhancedMYOBManager:
    """Enhanced MYOB management with CLI integration."""
    
    def __init__(self):
        """Initialize MYOB services."""
        self.poster = MYOBPoster()
        self.service = MyobService()
        logger.info("Enhanced MYOB Manager initialized")
    
    def get_order_summary_table(self) -> List[List[str]]:
        """
        Get orders formatted for DataTable display.
        
        Returns:
            List of rows for table display
        """
        try:
            orders = self.poster.list_pending_orders()
            table_data = []
            
            for order_id in orders:
                order_data = self.poster.load_order(order_id)
                if order_data:
                    # Calculate order summary
                    lines = order_data.get('lines', [])
                    total_qty = sum(line.get('orderquantity', 0) for line in lines)
                    
                    # Get delivery info
                    delivery_addr = order_data.get('deliveryaddress', {})
                    delivery_city = delivery_addr.get('line2', 'N/A') if delivery_addr else 'N/A'
                    
                    # Format row data
                    row = [
                        order_id,
                        str(order_data.get('debtorid', 'N/A')),
                        order_data.get('customerordernumber', 'N/A'),
                        str(len(lines)),
                        f"{total_qty:.0f}",
                        delivery_city,
                        'Ready'
                    ]
                    table_data.append(row)
            
            logger.info(f"Generated table data for {len(table_data)} orders")
            return table_data
            
        except Exception as e:
            logger.error(f"Failed to generate order table: {e}")
            return []
    
    def get_order_details(self, order_id: str) -> Optional[Dict[str, Any]]:
        """
        Get detailed order information for display.
        
        Args:
            order_id: Order ID to get details for
            
        Returns:
            Formatted order details or None
        """
        try:
            order_data = self.poster.load_order(order_id)
            if not order_data:
                return None
            
            # Format order details for display
            details = {
                "order_id": order_id,
                "basic_info": {
                    "Debtor ID": order_data.get('debtorid', 'N/A'),
                    "Status": order_data.get('status', 'N/A'),
                    "Customer PO": order_data.get('customerordernumber', 'N/A')
                },
                "delivery_address": self._format_delivery_address(order_data.get('deliveryaddress', {})),
                "order_lines": self._format_order_lines(order_data.get('lines', [])),
                "extra_fields": self._format_extra_fields(order_data.get('extrafields', [])),
                "summary": {
                    "Total Lines": len(order_data.get('lines', [])),
                    "Total Quantity": sum(line.get('orderquantity', 0) for line in order_data.get('lines', [])),
                    "Has Delivery Address": bool(order_data.get('deliveryaddress')),
                    "Has Extra Fields": bool(order_data.get('extrafields'))
                }
            }
            
            return details
            
        except Exception as e:
            logger.error(f"Failed to get order details for {order_id}: {e}")
            return None
    
    def _format_delivery_address(self, address: Dict[str, Any]) -> List[str]:
        """Format delivery address for display."""
        if not address:
            return ["No delivery address"]
        
        lines = []
        for i in range(1, 7):
            line = address.get(f'line{i}')
            if line:
                lines.append(line)
        
        return lines if lines else ["No delivery address"]
    
    def _format_order_lines(self, lines: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Format order lines for display."""
        formatted_lines = []
        
        for i, line in enumerate(lines, 1):
            formatted_line = {
                "Line": i,
                "Stock Code": line.get('stockcode', 'N/A'),
                "Quantity": line.get('orderquantity', 0),
                "Description": line.get('description', 'N/A')
            }
            formatted_lines.append(formatted_line)
        
        return formatted_lines
    
    def _format_extra_fields(self, extra_fields: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Format extra fields for display."""
        formatted = {}
        
        for field in extra_fields:
            key = field.get('key', 'Unknown')
            value = field.get('value', 'N/A')
            formatted[key] = value
        
        return formatted
    
    async def validate_order_interactive(self, order_id: str) -> Tuple[bool, str, Optional[Dict]]:
        """
        Validate order with detailed results.
        
        Args:
            order_id: Order ID to validate
            
        Returns:
            Tuple of (success, message, validation_details)
        """
        try:
            order_data = self.poster.load_order(order_id)
            if not order_data:
                return False, f"Could not load order {order_id}", None
            
            # Step 1: Structure validation
            is_valid, errors = self.poster.validate_order_data(order_data)
            
            validation_details = {
                "structure_validation": {
                    "passed": is_valid,
                    "errors": errors
                }
            }
            
            if not is_valid:
                return False, f"Structure validation failed: {'; '.join(errors)}", validation_details
            
            # Step 2: MYOB API validation
            try:
                success, validated_order, message = self.poster.validate_with_myob(order_data)
                
                validation_details["myob_validation"] = {
                    "passed": success,
                    "message": message,
                    "has_validated_order": validated_order is not None
                }
                
                if success and validated_order:
                    validation_details["validated_order_summary"] = {
                        "debtor_id": validated_order.get('debtorid'),
                        "line_count": len(validated_order.get('lines', [])),
                        "has_pricing": any('price' in line for line in validated_order.get('lines', []))
                    }
                
                return success, message, validation_details
                
            except Exception as api_error:
                validation_details["myob_validation"] = {
                    "passed": False,
                    "message": f"API error: {str(api_error)}",
                    "error_type": type(api_error).__name__
                }
                
                return False, f"MYOB API validation failed: {str(api_error)}", validation_details
            
        except Exception as e:
            logger.error(f"Order validation failed: {e}")
            return False, f"Validation error: {str(e)}", None
    
    async def post_order_interactive(self, order_id: str) -> Tuple[bool, str, Optional[Dict]]:
        """
        Post order with detailed results.
        
        Args:
            order_id: Order ID to post
            
        Returns:
            Tuple of (success, message, post_details)
        """
        try:
            success, message = self.poster.post_order_to_myob(order_id)
            
            post_details = {
                "order_id": order_id,
                "success": success,
                "message": message,
                "timestamp": datetime.now().isoformat()
            }
            
            if success:
                # Try to extract MYOB order ID from message
                if "MYOB ID:" in message:
                    myob_id = message.split("MYOB ID:")[-1].strip()
                    post_details["myob_order_id"] = myob_id
            
            return success, message, post_details
            
        except Exception as e:
            logger.error(f"Order posting failed: {e}")
            post_details = {
                "order_id": order_id,
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
            return False, f"Posting error: {str(e)}", post_details
    
    def get_myob_statistics(self) -> Dict[str, Any]:
        """Get MYOB processing statistics."""
        try:
            orders = self.poster.list_pending_orders()
            
            if not orders:
                return {
                    "total_orders": 0,
                    "total_lines": 0,
                    "total_quantity": 0,
                    "customers": [],
                    "status": "No pending orders"
                }
            
            # Analyze orders
            total_lines = 0
            total_quantity = 0
            customers = set()
            
            for order_id in orders:
                order_data = self.poster.load_order(order_id)
                if order_data:
                    lines = order_data.get('lines', [])
                    total_lines += len(lines)
                    total_quantity += sum(line.get('orderquantity', 0) for line in lines)
                    
                    debtor_id = order_data.get('debtorid')
                    if debtor_id:
                        customers.add(debtor_id)
            
            return {
                "total_orders": len(orders),
                "total_lines": total_lines,
                "total_quantity": total_quantity,
                "unique_customers": len(customers),
                "customers": list(customers),
                "average_lines_per_order": total_lines / len(orders) if orders else 0,
                "average_quantity_per_order": total_quantity / len(orders) if orders else 0,
                "status": "Ready for processing"
            }
            
        except Exception as e:
            logger.error(f"Failed to get MYOB statistics: {e}")
            return {"error": str(e)}
    
    def export_orders_summary(self, format_type: str = "json") -> str:
        """
        Export orders summary in specified format.
        
        Args:
            format_type: Export format ('json', 'csv', 'text')
            
        Returns:
            Formatted export string
        """
        try:
            orders = self.poster.list_pending_orders()
            export_data = []
            
            for order_id in orders:
                order_data = self.poster.load_order(order_id)
                if order_data:
                    summary = {
                        "order_id": order_id,
                        "debtor_id": order_data.get('debtorid'),
                        "customer_po": order_data.get('customerordernumber'),
                        "line_count": len(order_data.get('lines', [])),
                        "total_quantity": sum(line.get('orderquantity', 0) for line in order_data.get('lines', []))
                    }
                    export_data.append(summary)
            
            if format_type == "json":
                return json.dumps(export_data, indent=2)
            elif format_type == "csv":
                if not export_data:
                    return "No data to export"
                
                # CSV format
                headers = list(export_data[0].keys())
                csv_lines = [",".join(headers)]
                
                for row in export_data:
                    csv_lines.append(",".join(str(row.get(h, "")) for h in headers))
                
                return "\n".join(csv_lines)
            elif format_type == "text":
                if not export_data:
                    return "No pending orders"
                
                text_lines = ["MYOB Orders Summary", "=" * 20, ""]
                
                for order in export_data:
                    text_lines.extend([
                        f"Order ID: {order['order_id']}",
                        f"  Debtor ID: {order['debtor_id']}",
                        f"  Customer PO: {order['customer_po']}",
                        f"  Lines: {order['line_count']}",
                        f"  Quantity: {order['total_quantity']}",
                        ""
                    ])
                
                return "\n".join(text_lines)
            else:
                return f"Unsupported format: {format_type}"
                
        except Exception as e:
            logger.error(f"Failed to export orders summary: {e}")
            return f"Export failed: {str(e)}"