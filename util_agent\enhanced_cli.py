#!/usr/bin/env python3
"""
Enhanced CLI - Simple command-line interface for TeamsysV0.1.
Provides unified email processing, MYOB management, and natural language querying.
"""
import asyncio
import logging
import argparse
import sys
from typing import List, Dict, Any, Optional
from datetime import datetime

# Update import paths for new structure
sys.path.append('.')
sys.path.append('util_agent')
sys.path.append('email_agent')
sys.path.append('myob_agent')
sys.path.append('db_agent')

from util_agent.services.unified_service_manager import UnifiedServiceManager
from util_agent.services.enhanced_myob_manager import EnhancedMYOBManager
from util_agent.services.system_monitor import SystemMonitor
from util_agent.services.performance_optimizer import PerformanceOptimizer
from util_agent.agents.myob_query_agent import MYOBQueryAgent
from util_agent.agents.gmail_query_agent import GmailQueryAgent

logger = logging.getLogger(__name__)


class EnhancedCLI:
    """Simple command-line interface for TeamsysV0.1."""
    
    def __init__(self):
        """Initialize the CLI with all services."""
        print("🚀 Initializing TeamsysV0.1 Enhanced CLI...")
        
        try:
            self.service_manager = UnifiedServiceManager()
            self.myob_manager = EnhancedMYOBManager()
            self.system_monitor = SystemMonitor()
            self.performance_optimizer = PerformanceOptimizer()
            self.myob_query_agent = MYOBQueryAgent()
            self.gmail_query_agent = GmailQueryAgent()
            
            print("✅ All services initialized successfully")
        except Exception as e:
            print(f"❌ Failed to initialize services: {e}")
            sys.exit(1)
    
    def print_banner(self):
        """Print the application banner."""
        print("\n" + "="*60)
        print("    TEAMSYSV0.1 - EMAIL & MYOB MANAGEMENT SYSTEM")
        print("="*60)
        print("📧 Email Processing | 💼 MYOB Management | 🤖 AI Queries")
        print("="*60 + "\n")
    
    def print_menu(self):
        """Print the main menu."""
        print("📋 MAIN MENU:")
        print("1. 📧 Email Processing")
        print("2. 💼 MYOB Management") 
        print("3. 🔍 MYOB Query (Natural Language)")
        print("4. 📬 Gmail Query (Natural Language)")
        print("5. 📊 System Status")
        print("6. ⚙️  System Health Check")
        print("0. 🚪 Exit")
        print("-" * 40)
    
    async def email_processing_menu(self):
        """Handle email processing operations."""
        print("\n📧 EMAIL PROCESSING")
        print("-" * 30)
        print("1. Process emails from specific labels")
        print("2. Process all emails")
        print("3. View processing status")
        print("0. Back to main menu")
        
        choice = input("\nSelect option: ").strip()
        
        if choice == "1":
            await self.process_specific_labels()
        elif choice == "2":
            await self.process_all_emails()
        elif choice == "3":
            self.show_email_status()
        elif choice == "0":
            return
        else:
            print("❌ Invalid option")
    
    async def process_specific_labels(self):
        """Process emails from specific labels."""
        print("\n📋 Available Labels:")
        labels = ["Brady", "RSEA", "Woolworths", "Brierley", "Gateway", "Highgate", "Sitecraft"]
        for i, label in enumerate(labels, 1):
            print(f"{i}. {label}")
        
        print("\nEnter label numbers (comma-separated) or 'all' for all labels:")
        selection = input("Labels: ").strip()
        
        if selection.lower() == 'all':
            selected_labels = labels
        else:
            try:
                indices = [int(x.strip()) - 1 for x in selection.split(',')]
                selected_labels = [labels[i] for i in indices if 0 <= i < len(labels)]
            except (ValueError, IndexError):
                print("❌ Invalid selection")
                return
        
        max_emails = input("Max emails per label (default 5): ").strip()
        max_emails = int(max_emails) if max_emails.isdigit() else 5
        
        time_filter = input("Time filter (e.g., '7d', '2w', or leave empty): ").strip()
        time_filter = time_filter if time_filter else None
        
        print(f"\n🔄 Processing emails from: {', '.join(selected_labels)}")
        print(f"📊 Max emails: {max_emails}, Time filter: {time_filter or 'None'}")
        
        try:
            def progress_callback(message: str, current: int, total: int):
                print(f"[{current}/{total}] {message}")
            
            results = await self.service_manager.process_emails_interactive(
                labels=selected_labels,
                max_emails=max_emails,
                time_filter=time_filter,
                callback=progress_callback
            )
            
            print(f"\n✅ Processing completed: {len(results)} orders processed")
            
            if results:
                print("\n📋 PROCESSED ORDERS:")
                print("-" * 50)
                for i, result in enumerate(results[:10], 1):  # Show first 10
                    customer = result.get("customer_name", "Unknown")
                    po = result.get("customer_po", "N/A")
                    lines = len(result.get("order_lines", []))
                    print(f"{i:2d}. {customer:<20} | PO: {po:<15} | Lines: {lines}")
                
                if len(results) > 10:
                    print(f"... and {len(results) - 10} more orders")
        
        except Exception as e:
            print(f"❌ Error processing emails: {e}")
        
        input("\nPress Enter to continue...")
    
    async def process_all_emails(self):
        """Process all emails from all labels."""
        print("\n🔄 Processing ALL emails from ALL labels...")
        
        confirm = input("This may take a while. Continue? (y/N): ").strip().lower()
        if confirm != 'y':
            return
        
        try:
            def progress_callback(message: str, current: int, total: int):
                print(f"[{current}/{total}] {message}")
            
            results = await self.service_manager.process_emails_interactive(
                labels=["Brady", "RSEA", "Woolworths", "Brierley", "Gateway"],
                max_emails=20,
                callback=progress_callback
            )
            
            print(f"\n✅ Processing completed: {len(results)} orders processed")
        
        except Exception as e:
            print(f"❌ Error processing emails: {e}")
        
        input("\nPress Enter to continue...")
    
    def show_email_status(self):
        """Show email processing status."""
        print("\n📊 EMAIL PROCESSING STATUS")
        print("-" * 40)
        
        try:
            status = self.service_manager.get_email_processing_status()
            
            print(f"Recent orders: {status.get('recent_orders_count', 0)}")
            print(f"Last processed: {status.get('last_processed', 'Never')}")
            print(f"Gmail connected: {'✅' if status.get('gmail_connected') else '❌'}")
            print(f"LLM service: {'✅' if status.get('llm_service_healthy') else '❌'}")
        
        except Exception as e:
            print(f"❌ Error getting status: {e}")
        
        input("\nPress Enter to continue...")
    
    async def myob_management_menu(self):
        """Handle MYOB management operations."""
        print("\n💼 MYOB MANAGEMENT")
        print("-" * 30)
        print("1. List pending orders")
        print("2. Validate order")
        print("3. Post order to MYOB")
        print("4. Batch process all orders")
        print("5. View MYOB status")
        print("0. Back to main menu")
        
        choice = input("\nSelect option: ").strip()
        
        if choice == "1":
            self.list_pending_orders()
        elif choice == "2":
            await self.validate_order()
        elif choice == "3":
            await self.post_order()
        elif choice == "4":
            await self.batch_process_orders()
        elif choice == "5":
            self.show_myob_status()
        elif choice == "0":
            return
        else:
            print("❌ Invalid option")
    
    def list_pending_orders(self):
        """List all pending MYOB orders."""
        print("\n📋 PENDING MYOB ORDERS")
        print("-" * 50)
        
        try:
            orders = self.service_manager.get_pending_orders()
            
            if not orders:
                print("📭 No pending orders found")
                return
            
            print(f"{'#':<3} {'Order ID':<15} {'Debtor':<8} {'Customer PO':<15} {'Lines':<6} {'Qty':<6}")
            print("-" * 60)
            
            for i, order in enumerate(orders, 1):
                print(f"{i:<3} {order['order_id']:<15} {order['debtor_id']:<8} "
                      f"{order['customer_po']:<15} {order['line_count']:<6} {order['total_quantity']:<6}")
        
        except Exception as e:
            print(f"❌ Error listing orders: {e}")
        
        input("\nPress Enter to continue...")
    
    async def validate_order(self):
        """Validate a specific order."""
        order_id = input("\nEnter Order ID to validate: ").strip()
        if not order_id:
            return
        
        print(f"🔍 Validating order {order_id}...")
        
        try:
            def progress_callback(message: str, current: int, total: int):
                print(f"[{current}/{total}] {message}")
            
            success, message = await self.service_manager.validate_order_interactive(
                order_id, callback=progress_callback
            )
            
            if success:
                print(f"✅ Validation passed: {message}")
            else:
                print(f"❌ Validation failed: {message}")
        
        except Exception as e:
            print(f"❌ Error validating order: {e}")
        
        input("\nPress Enter to continue...")
    
    async def post_order(self):
        """Post a specific order to MYOB."""
        order_id = input("\nEnter Order ID to post: ").strip()
        if not order_id:
            return
        
        confirm = input(f"Post order {order_id} to MYOB? (y/N): ").strip().lower()
        if confirm != 'y':
            return
        
        print(f"📤 Posting order {order_id} to MYOB...")
        
        try:
            def progress_callback(message: str, current: int, total: int):
                print(f"[{current}/{total}] {message}")
            
            success, message = await self.service_manager.post_order_interactive(
                order_id, callback=progress_callback
            )
            
            if success:
                print(f"✅ Order posted successfully: {message}")
            else:
                print(f"❌ Order posting failed: {message}")
        
        except Exception as e:
            print(f"❌ Error posting order: {e}")
        
        input("\nPress Enter to continue...")
    
    async def batch_process_orders(self):
        """Process all pending orders in batch."""
        orders = self.service_manager.get_pending_orders()
        
        if not orders:
            print("📭 No pending orders to process")
            input("\nPress Enter to continue...")
            return
        
        print(f"\n🚀 Found {len(orders)} pending orders")
        confirm = input("Process all orders in batch? (y/N): ").strip().lower()
        if confirm != 'y':
            return
        
        print("🔄 Starting batch processing...")
        
        try:
            order_ids = [order['order_id'] for order in orders]
            
            def progress_callback(message: str, current: int, total: int):
                print(f"[{current}/{total}] {message}")
            
            results = await self.service_manager.process_order_batch(
                order_ids, callback=progress_callback
            )
            
            successful = sum(1 for _, success, _ in results if success)
            failed = len(results) - successful
            
            print(f"\n📊 Batch processing completed:")
            print(f"   ✅ Successful: {successful}")
            print(f"   ❌ Failed: {failed}")
        
        except Exception as e:
            print(f"❌ Error in batch processing: {e}")
        
        input("\nPress Enter to continue...")
    
    def show_myob_status(self):
        """Show MYOB service status."""
        print("\n💼 MYOB SERVICE STATUS")
        print("-" * 40)
        
        try:
            status = self.service_manager.get_myob_status()
            
            print(f"Pending orders: {status.get('pending_orders_count', 0)}")
            print(f"Service connected: {'✅' if status.get('myob_service_connected') else '❌'}")
            print(f"Last order date: {status.get('last_order_date', 'Never')}")
            print(f"Total quantity pending: {status.get('total_quantity_pending', 0)}")
        
        except Exception as e:
            print(f"❌ Error getting MYOB status: {e}")
        
        input("\nPress Enter to continue...")
    
    async def myob_query_menu(self):
        """Handle natural language MYOB queries."""
        print("\n🔍 MYOB NATURAL LANGUAGE QUERY")
        print("-" * 40)
        print("Ask questions about MYOB data in natural language")
        print("Examples:")
        print("- 'Show me all Woolworths orders from last week'")
        print("- 'What's the total value of pending orders?'")
        print("- 'List customers with overdue invoices'")
        print("\nType 'back' to return to main menu")
        print("-" * 40)
        
        while True:
            query = input("\n🤖 Your query: ").strip()
            
            if query.lower() in ['back', 'exit', 'quit']:
                break
            
            if not query:
                continue
            
            print(f"🔍 Processing query: {query}")
            print("⏳ Please wait...")
            
            try:
                response = await self.myob_query_agent.process_natural_query(query)
                print("\n📋 RESPONSE:")
                print("-" * 30)
                print(response)
            
            except Exception as e:
                print(f"❌ Error processing query: {e}")
    
    async def gmail_query_menu(self):
        """Handle natural language Gmail queries."""
        print("\n📬 GMAIL NATURAL LANGUAGE QUERY")
        print("-" * 40)
        print("Ask questions about Gmail data in natural language")
        print("Examples:")
        print("- 'Show me recent emails from Brady'")
        print("- 'Find purchase orders from last month'")
        print("- 'List unprocessed emails'")
        print("\nType 'back' to return to main menu")
        print("-" * 40)
        
        while True:
            query = input("\n📧 Your query: ").strip()
            
            if query.lower() in ['back', 'exit', 'quit']:
                break
            
            if not query:
                continue
            
            print(f"🔍 Processing query: {query}")
            print("⏳ Please wait...")
            
            try:
                response = await self.gmail_query_agent.process_natural_query(query)
                print("\n📋 RESPONSE:")
                print("-" * 30)
                print(response)
            
            except Exception as e:
                print(f"❌ Error processing query: {e}")
    
    def show_system_status(self):
        """Show system status."""
        print("\n📊 SYSTEM STATUS")
        print("-" * 40)
        
        try:
            status = self.service_manager.get_system_status()
            
            print(f"Timestamp: {status.get('timestamp', 'Unknown')}")
            print("\n📧 Email Processing:")
            email_status = status.get('email_processing', {})
            print(f"  Recent orders: {email_status.get('recent_orders_count', 0)}")
            print(f"  Gmail connected: {'✅' if email_status.get('gmail_connected') else '❌'}")
            print(f"  LLM service: {'✅' if email_status.get('llm_service_healthy') else '❌'}")
            
            print("\n💼 MYOB Management:")
            myob_status = status.get('myob_management', {})
            print(f"  Pending orders: {myob_status.get('pending_orders_count', 0)}")
            print(f"  Service connected: {'✅' if myob_status.get('myob_service_connected') else '❌'}")
            
            print("\n⚙️  Services:")
            services = status.get('services', {})
            for service, health in services.items():
                print(f"  {service}: {'✅' if health == 'healthy' else '❌'}")
        
        except Exception as e:
            print(f"❌ Error getting system status: {e}")
        
        input("\nPress Enter to continue...")
    
    async def system_health_check(self):
        """Perform comprehensive system health check."""
        print("\n🏥 COMPREHENSIVE SYSTEM HEALTH CHECK")
        print("-" * 50)
        print("⏳ Running health checks...")
        
        try:
            health_results = await self.service_manager.comprehensive_health_check()
            
            print("\n📊 HEALTH CHECK RESULTS:")
            print("-" * 30)
            
            for component, result in health_results.items():
                if isinstance(result, dict):
                    status = result.get('status', 'unknown')
                    print(f"{component}: {'✅' if status == 'healthy' else '❌'} ({status})")
                    if 'details' in result:
                        print(f"  Details: {result['details']}")
                else:
                    print(f"{component}: {'✅' if result else '❌'}")
        
        except Exception as e:
            print(f"❌ Error during health check: {e}")
        
        input("\nPress Enter to continue...")
    
    async def run(self):
        """Run the main CLI loop."""
        self.print_banner()
        
        while True:
            try:
                self.print_menu()
                choice = input("Select option: ").strip()
                
                if choice == "1":
                    await self.email_processing_menu()
                elif choice == "2":
                    await self.myob_management_menu()
                elif choice == "3":
                    await self.myob_query_menu()
                elif choice == "4":
                    await self.gmail_query_menu()
                elif choice == "5":
                    self.show_system_status()
                elif choice == "6":
                    await self.system_health_check()
                elif choice == "0":
                    print("\n👋 Goodbye!")
                    break
                else:
                    print("❌ Invalid option. Please try again.")
                
                print()  # Add spacing
            
            except KeyboardInterrupt:
                print("\n\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"\n❌ Unexpected error: {e}")
                input("Press Enter to continue...")


def main():
    """Main entry point."""
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Create and run CLI
    cli = EnhancedCLI()
    asyncio.run(cli.run())


if __name__ == "__main__":
    main()