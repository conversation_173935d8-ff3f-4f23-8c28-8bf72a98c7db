#!/usr/bin/env python3
"""
Demo script for Enhanced Gmail Service with AI-powered capabilities.

This script demonstrates the enhanced Gmail service that combines Gmail API operations
with AI-powered email analysis, automated processing, and intelligent workflows.
"""

import json
import sys
from datetime import datetime
from pathlib import Path

# Add current directory to path for imports
sys.path.insert(0, str(Path(__file__).parent))

from services.enhanced_gmail_service import EnhancedGmailService
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.tree import Tree

console = Console()

def demo_ai_powered_email_fetching():
    """Demonstrate AI-powered email fetching and filtering."""
    console.print(Panel.fit(
        "🤖 AI-Powered Email Fetching Demo",
        style="bold blue"
    ))
    
    try:
        enhanced_gmail = EnhancedGmailService()
        
        console.print("🔄 Fetching emails with AI filtering...")
        
        with Progress(SpinnerColumn(), TextColumn("[progress.description]{task.description}")) as progress:
            task = progress.add_task("Applying AI filters...", total=None)
            
            # Fetch emails with AI filtering
            filtered_emails = enhanced_gmail.fetch_emails_with_ai_filtering(
                labels=["Brady"],
                days_back=7,
                ai_filter=True,
                min_confidence=0.7
            )
            
            progress.update(task, description="✅ AI filtering complete")
        
        console.print(f"✅ Found {len(filtered_emails)} emails passing AI filter")
        
        if filtered_emails:
            # Display first few emails
            emails_table = Table(title="AI-Filtered Emails")
            emails_table.add_column("Subject", style="bold blue", width=40)
            emails_table.add_column("Sender", style="green", width=25)
            emails_table.add_column("Attachments", style="yellow", width=12)
            
            for email in filtered_emails[:5]:  # Show first 5
                emails_table.add_row(
                    email.subject[:40] + ("..." if len(email.subject) > 40 else ""),
                    email.sender[:25] + ("..." if len(email.sender) > 25 else ""),
                    str(len(email.attachments))
                )
            
            console.print(emails_table)
        
        # Fetch priority emails
        console.print("\n🔄 Fetching high-priority emails...")
        
        priority_emails = enhanced_gmail.fetch_priority_emails(
            priority_levels=["critical", "high"]
        )
        
        console.print(f"✅ Found {len(priority_emails)} high-priority emails")
        
    except Exception as e:
        console.print(f"❌ Error in AI email fetching demo: {e}", style="red")

def demo_single_email_processing():
    """Demonstrate processing a single email with AI analysis."""
    console.print(Panel.fit(
        "📧 Single Email AI Processing Demo",
        style="bold green"
    ))
    
    try:
        enhanced_gmail = EnhancedGmailService()
        
        # Fetch a sample email
        console.print("🔄 Fetching sample email for processing...")
        
        emails = enhanced_gmail.fetch_emails_from_labels()
        
        if not emails:
            console.print("⚠️  No emails found to process", style="yellow")
            return
        
        sample_email = emails[0]  # Process first email
        
        console.print(f"📧 Processing email: {sample_email.subject[:50]}...")
        
        with Progress(SpinnerColumn(), TextColumn("[progress.description]{task.description}")) as progress:
            task = progress.add_task("AI analysis in progress...", total=None)
            
            # Process with AI
            result = enhanced_gmail.process_email_with_ai(sample_email)
            
            progress.update(task, description="✅ AI processing complete")
        
        # Display results
        console.print(f"✅ Processing completed in {result.processing_time:.2f} seconds")
        
        # AI Analysis Results
        analysis_table = Table(title="AI Analysis Results")
        analysis_table.add_column("Aspect", style="bold")
        analysis_table.add_column("Result", style="cyan")
        
        if result.ai_analysis:
            analysis_table.add_row("Category", result.ai_analysis.get("category", "unknown"))
            analysis_table.add_row("Priority", result.ai_analysis.get("priority", "unknown"))
            analysis_table.add_row("Contains Order", str(result.ai_analysis.get("contains_order", False)))
            analysis_table.add_row("Action Required", str(result.ai_analysis.get("action_required", False)))
            analysis_table.add_row("Confidence", f"{result.ai_analysis.get('confidence_score', 0):.1%}")
            
            if result.ai_analysis.get("entities"):
                entities = ", ".join(result.ai_analysis["entities"][:3])  # First 3 entities
                analysis_table.add_row("Key Entities", entities)
        
        console.print(analysis_table)
        
        # Order Extraction Results
        if result.extracted_orders:
            console.print(f"\n📦 Found {len(result.extracted_orders)} orders:")
            
            for i, order in enumerate(result.extracted_orders):
                console.print(f"\n🔸 Order {i+1} from {order.get('source', 'email')}")
                order_data = order.get("order_data", {})
                
                if "customer_details" in order_data:
                    customer = order_data["customer_details"]
                    console.print(f"  Customer ID: {customer.get('debtor_id', 'N/A')}")
                    console.print(f"  PO Number: {customer.get('customer_order_number', 'N/A')}")
                
                if "order_lines" in order_data:
                    console.print(f"  Items: {len(order_data['order_lines'])} line items")
                
                if "X_SHIPVIA" in order_data:
                    console.print(f"  Shipping: {order_data['X_SHIPVIA']}")
        
        # PDF Analysis Results
        if result.pdf_analyses:
            console.print(f"\n📄 Analyzed {len(result.pdf_analyses)} PDF attachments:")
            
            for pdf in result.pdf_analyses:
                console.print(f"  • {pdf.get('filename', 'Unknown')}: {pdf.get('document_type', 'unknown')} "
                             f"(confidence: {pdf.get('confidence', 0):.1%})")
        
    except Exception as e:
        console.print(f"❌ Error in single email processing demo: {e}", style="red")

def demo_batch_processing():
    """Demonstrate batch processing of multiple emails."""
    console.print(Panel.fit(
        "📊 Batch Email Processing Demo",
        style="bold magenta"
    ))
    
    try:
        enhanced_gmail = EnhancedGmailService()
        
        console.print("🔄 Fetching emails for batch processing...")
        
        # Fetch emails for batch processing
        emails = enhanced_gmail.fetch_emails_from_labels()
        
        if not emails:
            console.print("⚠️  No emails found for batch processing", style="yellow")
            return
        
        # Limit to first 5 emails for demo
        batch_emails = emails[:5]
        
        console.print(f"📧 Processing batch of {len(batch_emails)} emails...")
        
        with Progress(SpinnerColumn(), TextColumn("[progress.description]{task.description}")) as progress:
            task = progress.add_task("Batch processing...", total=None)
            
            # Process batch with filters
            filter_criteria = {
                "has_attachments": True,  # Only emails with attachments
                "min_attachments": 1
            }
            
            batch_result = enhanced_gmail.batch_process_emails(
                batch_emails, 
                filter_criteria=filter_criteria
            )
            
            progress.update(task, description="✅ Batch processing complete")
        
        # Display batch results
        console.print(f"✅ Batch processing completed in {batch_result.processing_time:.2f} seconds")
        
        # Summary table
        summary_table = Table(title="Batch Processing Summary")
        summary_table.add_column("Metric", style="bold")
        summary_table.add_column("Value", style="cyan")
        
        summary_table.add_row("Total Emails", str(batch_result.total_emails))
        summary_table.add_row("Processed Emails", str(batch_result.processed_emails))
        summary_table.add_row("Successful Extractions", str(batch_result.successful_extractions))
        summary_table.add_row("Failed Extractions", str(batch_result.failed_extractions))
        summary_table.add_row("Processing Time", f"{batch_result.processing_time:.2f}s")
        summary_table.add_row("Average Time per Email", 
                             f"{batch_result.processing_time / max(batch_result.processed_emails, 1):.2f}s")
        
        console.print(summary_table)
        
        # Detailed results
        if batch_result.results:
            console.print("\n📋 Detailed Results:")
            
            results_table = Table()
            results_table.add_column("Email", style="bold", width=30)
            results_table.add_column("Status", style="green", width=10)
            results_table.add_column("Category", style="blue", width=15)
            results_table.add_column("Orders", style="yellow", width=8)
            results_table.add_column("Time", style="dim", width=8)
            
            for result in batch_result.results:
                subject = result.email_data.subject[:25] + ("..." if len(result.email_data.subject) > 25 else "")
                status = "✅" if result.processing_status == "success" else "❌"
                category = result.ai_analysis.get("category", "unknown")[:12]
                orders = str(len(result.extracted_orders))
                time_str = f"{result.processing_time:.1f}s" if result.processing_time else "N/A"
                
                results_table.add_row(subject, status, category, orders, time_str)
            
            console.print(results_table)
        
    except Exception as e:
        console.print(f"❌ Error in batch processing demo: {e}", style="red")

def demo_automated_workflows():
    """Demonstrate automated processing workflows."""
    console.print(Panel.fit(
        "🚀 Automated Workflow Demo",
        style="bold yellow"
    ))
    
    try:
        enhanced_gmail = EnhancedGmailService()
        
        console.print("🔄 Running automated order processing workflow...")
        
        with Progress(SpinnerColumn(), TextColumn("[progress.description]{task.description}")) as progress:
            task = progress.add_task("Automated processing...", total=None)
            
            # Run automated order processing
            auto_result = enhanced_gmail.auto_process_new_orders(
                auto_approve_threshold=0.8,
                create_myob_orders=False  # Set to False for demo
            )
            
            progress.update(task, description="✅ Automated processing complete")
        
        console.print(f"✅ Automated processing completed in {auto_result.processing_time:.2f} seconds")
        
        # Display automation results
        auto_table = Table(title="Automation Results")
        auto_table.add_column("Metric", style="bold")
        auto_table.add_column("Value", style="cyan")
        
        auto_table.add_row("Emails Evaluated", str(auto_result.total_emails))
        auto_table.add_row("Auto-Processed", str(auto_result.processed_emails))
        auto_table.add_row("Orders Extracted", str(auto_result.successful_extractions))
        auto_table.add_row("Processing Failures", str(auto_result.failed_extractions))
        
        if auto_result.processed_emails > 0:
            success_rate = (auto_result.successful_extractions / auto_result.processed_emails) * 100
            auto_table.add_row("Success Rate", f"{success_rate:.1f}%")
        
        console.print(auto_table)
        
        # Smart labeling demo
        console.print("\n🏷️  Demonstrating smart email labeling...")
        
        emails = enhanced_gmail.fetch_emails_from_labels()[:3]  # First 3 emails
        
        if emails:
            label_assignments = enhanced_gmail.smart_label_emails(emails)
            
            console.print("✅ Smart labeling completed")
            
            # Display label assignments
            labels_tree = Tree("📋 Label Assignments")
            
            for label, email_ids in label_assignments.items():
                if email_ids:
                    label_node = labels_tree.add(f"🏷️  {label} ({len(email_ids)} emails)")
                    for email_id in email_ids[:3]:  # Show first 3
                        label_node.add(f"📧 {email_id[:12]}...")
            
            console.print(labels_tree)
        
    except Exception as e:
        console.print(f"❌ Error in automated workflow demo: {e}", style="red")

def demo_analytics_and_reporting():
    """Demonstrate analytics and reporting capabilities."""
    console.print(Panel.fit(
        "📈 Analytics & Reporting Demo",
        style="bold cyan"
    ))
    
    try:
        enhanced_gmail = EnhancedGmailService()
        
        console.print("🔄 Generating processing analytics...")
        
        # Get analytics
        analytics = enhanced_gmail.get_processing_analytics(days_back=30)
        
        # Display current session stats
        session_table = Table(title="Current Session Statistics")
        session_table.add_column("Metric", style="bold")
        session_table.add_column("Value", style="cyan")
        
        session_stats = analytics["current_session"]
        session_table.add_row("Emails Processed", str(session_stats["emails_processed"]))
        session_table.add_row("Orders Extracted", str(session_stats["orders_extracted"]))
        session_table.add_row("Processing Errors", str(session_stats["processing_errors"]))
        session_table.add_row("Last Processing", session_stats["last_processing_time"] or "Never")
        
        console.print(session_table)
        
        # Display performance metrics
        perf_table = Table(title="Performance Metrics")
        perf_table.add_column("Metric", style="bold")
        perf_table.add_column("Value", style="green")
        
        perf_metrics = analytics["performance_metrics"]
        perf_table.add_row("Emails per Hour", f"{perf_metrics['emails_per_hour']:.1f}")
        perf_table.add_row("Orders per Hour", f"{perf_metrics['orders_per_hour']:.1f}")
        perf_table.add_row("Error Rate", f"{perf_metrics['error_rate']:.1%}")
        perf_table.add_row("AI Confidence Avg", f"{perf_metrics['ai_confidence_average']:.1%}")
        
        console.print(perf_table)
        
        # Display category breakdown
        console.print("\n📊 Email Categories (Historical):")
        categories = analytics["historical_summary"]["most_common_categories"]
        
        categories_table = Table()
        categories_table.add_column("Category", style="bold")
        categories_table.add_column("Count", style="yellow")
        categories_table.add_column("Percentage", style="green")
        
        total_categorized = sum(categories.values())
        for category, count in sorted(categories.items(), key=lambda x: x[1], reverse=True):
            percentage = (count / total_categorized) * 100 if total_categorized > 0 else 0
            categories_table.add_row(category, str(count), f"{percentage:.1f}%")
        
        console.print(categories_table)
        
        # Service status
        console.print("\n🔧 Service Status:")
        status = enhanced_gmail.get_service_status()
        
        status_table = Table()
        status_table.add_column("Component", style="bold")
        status_table.add_column("Status", style="green")
        
        status_table.add_row("Gmail Service", status["gmail_service"])
        status_table.add_row("LLM Service", status["llm_service"])
        
        console.print(status_table)
        
        # Capabilities
        console.print("\n🎯 Available Capabilities:")
        for capability in status["capabilities"]:
            console.print(f"  ✅ {capability.replace('_', ' ').title()}")
        
    except Exception as e:
        console.print(f"❌ Error in analytics demo: {e}", style="red")

def main():
    """Run all enhanced Gmail service demonstrations."""
    console.print(Panel.fit(
        "🚀 Enhanced Gmail Service Demonstration\n"
        "AI-Powered Email Processing, Automation, and Analytics",
        style="bold yellow"
    ))
    
    try:
        # Run demonstrations
        demo_ai_powered_email_fetching()
        console.print("\n" + "="*60 + "\n")
        
        demo_single_email_processing()
        console.print("\n" + "="*60 + "\n")
        
        demo_batch_processing()
        console.print("\n" + "="*60 + "\n")
        
        demo_automated_workflows()
        console.print("\n" + "="*60 + "\n")
        
        demo_analytics_and_reporting()
        
        console.print(Panel.fit(
            "✅ All demonstrations completed successfully!\n"
            "The Enhanced Gmail Service provides:\n"
            "• AI-powered email filtering and analysis\n"
            "• Automated order processing workflows\n"
            "• Batch processing with intelligent prioritization\n"
            "• Smart labeling and email management\n"
            "• Comprehensive analytics and reporting\n"
            "• Seamless integration with existing Gmail operations",
            style="bold green"
        ))
        
    except Exception as e:
        console.print(f"❌ Demo failed: {e}", style="red")

if __name__ == "__main__":
    main()