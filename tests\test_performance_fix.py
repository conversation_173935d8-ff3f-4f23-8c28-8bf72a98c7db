#!/usr/bin/env python3
"""
Test script to verify the ZeroDivisionError fix in performance optimizer.
"""
import logging
from services.performance_optimizer import PerformanceOptimizer

# Configure logging
logging.basicConfig(level=logging.INFO)

def test_performance_optimizer():
    """Test performance optimizer cache statistics."""
    print("Testing Performance Optimizer Cache Statistics...")
    
    # Create optimizer
    optimizer = PerformanceOptimizer()
    
    # Test cache stats with zero counters (this was causing the error)
    try:
        stats = optimizer.cache.get_stats()
        print(f"✅ Cache stats retrieved successfully:")
        print(f"   Size: {stats['size']}")
        print(f"   Max Size: {stats['max_size']}")
        print(f"   TTL: {stats['ttl_seconds']}s")
        print(f"   Hit Rate: {stats['hit_rate']:.2%}")
        
        # Test with some cache operations
        optimizer.cache.set("test_key", "test_value")
        optimizer.cache.get("test_key")  # Hit
        optimizer.cache.get("nonexistent")  # Miss
        
        stats_after = optimizer.cache.get_stats()
        print(f"\n✅ Cache stats after operations:")
        print(f"   Size: {stats_after['size']}")
        print(f"   Hit Rate: {stats_after['hit_rate']:.2%}")
        
        return True
        
    except ZeroDivisionError as e:
        print(f"❌ ZeroDivisionError still occurs: {e}")
        return False
    except Exception as e:
        print(f"❌ Other error: {e}")
        return False

if __name__ == "__main__":
    success = test_performance_optimizer()
    print(f"\n{'✅ Test PASSED' if success else '❌ Test FAILED'}")