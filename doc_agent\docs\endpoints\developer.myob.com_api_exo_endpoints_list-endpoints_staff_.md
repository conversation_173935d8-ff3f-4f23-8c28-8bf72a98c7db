---
url: "https://developer.myob.com/api/exo/endpoints/list-endpoints/staff/"
title: "Staff"
---

[![](https://developer.myob.com/media/cms_page_media/23/myob_logo_small.png)](http://www.myob.com/)

# Staff

Return staff information

**Date Released:** Oct 25th 2013 **Date Updated:** Oct 25th 2013

| URL | Supports |
| --- | --- |
| {URI}/staff/ | [GET](https://developer.myob.com/api/exo/endpoints/list-endpoints/staff/#GET) \| <br>[PUT](https://developer.myob.com/api/exo/endpoints/list-endpoints/staff/#PUT) \| <br>[POST](https://developer.myob.com/api/exo/endpoints/list-endpoints/staff/#POST) |
| {URI} is exo.api.myob.com when connecting to the cloud or the address of the machine hosting the API when connecting on premise. |

The elements list below details information for Staff. To view the descriptions for the elements you can either hover any attribute to reveal details [or click here to show all details inline.](https://developer.myob.com/api/exo/endpoints/list-endpoints/staff/#reveal)

#### Attribute Details

- Name string,null
- Type: string,null
- JobTitle string,null
- Type: string,null
- Rel string,null
- Type: string,null
- Title string,null
- Type: string,null
- Id string
- Type: string
- Href string,null
- Type: string,null

#### Example json GET response

- {
  - 0
    - {
      - name : BRIDGET FAIRWEATHER
      - jobtitle : SALES
      - id : 1
      - href : {URI}/staff/1
    - }
  - 1
    - {
      - name : DAVID CRANSTON
      - jobtitle : SALES
      - id : 2
      - href : {URI}/staff/2
    - }
  - 2
    - {
      - name : TIM MCINTOSH
      - jobtitle : SALES
      - id : 3
      - href : {URI}/staff/3
    - }
  - 3
    - {
      - name : GREG MANNING
      - jobtitle : SALES
      - id : 4
      - href : {URI}/staff/4
    - }
  - 4
    - {
      - name : MALCOLM BREEN
      - jobtitle : SALES
      - id : 5
      - href : {URI}/staff/5
    - }
  - 5
    - {
      - name : INTERNET SALES
      - jobtitle
      - id : 6
      - href : {URI}/staff/6
    - }
  - 6
    - {
      - name : SYSDBA
      - jobtitle
      - id : 7
      - href : {URI}/staff/7
    - }
  - 7
    - {
      - name : EXO BUSINESS ADMIN ACCOUNT
      - jobtitle
      - id : 8
      - href : {URI}/staff/8
    - }
  - 8
    - {
      - name : DEMO
      - jobtitle
      - id : 9
      - href : {URI}/staff/9
    - }
- }

{URI} is defined as: http://exo.api.myob.com/