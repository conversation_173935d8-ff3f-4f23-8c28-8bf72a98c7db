"""
Main email order processor - orchestrates the complete workflow.
Refactored to focus only on workflow orchestration logic.
"""
import logging
import os
import json
from datetime import datetime
from typing import List, Optional, Dict, Any
from util_agent.config import config
from util_agent.models import ProcessedOrder, EmailData
from .gmail_service import GmailService
from .pdf_extractor import extract_text_from_pdf
from util_agent.llm_service import LLMService
from db_agent.supabase_database_service import SupabaseService
from .email_categorizer import EmailCategorizer, EmailClassification, EmailCategory, EmailPriority

logger = logging.getLogger(__name__)


class EmailOrderProcessor:
    """Main processor that orchestrates the email-to-markdown/JSON workflow with Supabase integration."""
    
    def __init__(self):
        logger.info("Initializing Email Order Processor with intelligent categorization")
        self.gmail_service = GmailService()
        self.db_service = SupabaseService()
        self.llm_service = LLMService(config.MISTRAL_API_KEY, self.db_service)
        self.email_categorizer = EmailCategorizer(self.llm_service)
        self._ensure_directories()
        self._setup_processing_labels()
        logger.info("Email Order Processor with intelligent categorization initialized successfully")
    
    def _setup_processing_labels(self):
        """Setup traffic light processing labels in Gmail."""
        try:
            labels = self.gmail_service.get_or_create_processing_labels()
            if labels:
                logger.info("Traffic light processing labels ready: Processed, Review, Failed")
            else:
                logger.warning("Failed to setup processing labels - labeling will be disabled")
        except Exception as e:
            logger.error(f"Error setting up processing labels: {e}")

    def _ensure_directories(self):
        directories = ["markdown", "myob"]
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
        logger.info("Created output directories: markdown/, myob/")

    def _sanitize_filename(self, text: str) -> str:
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            text = text.replace(char, '_')
        return text.strip()[:50]

    def _generate_filename_prefix(self, email_subject: str) -> str:
        now = datetime.now()
        date_prefix = now.strftime("%d-%m_%H%M")
        sanitized_subject = self._sanitize_filename(email_subject)
        return f"{date_prefix}_{sanitized_subject}"

    def _save_markdown_file(self, email_subject: str, markdown_content: str) -> str:
        filename_prefix = self._generate_filename_prefix(email_subject)
        filepath = os.path.join("markdown", f"{filename_prefix}.md")
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(markdown_content)
            logger.info(f"Saved markdown summary to: {filepath}")
            return filepath
        except Exception as e:
            logger.error(f"Failed to save markdown file: {e}")
            return ""

    def _save_myob_file(self, email_subject: str, myob_payload: dict) -> str:
        filename_prefix = self._generate_filename_prefix(email_subject)
        filepath = os.path.join("myob", f"{filename_prefix}.json")
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(myob_payload, f, indent=2)
            logger.info(f"Saved MYOB payload to: {filepath}")
            return filepath
        except Exception as e:
            logger.error(f"Failed to save MYOB file: {e}")
            return ""

    def _generate_order_id(self) -> str:
        import glob
        pattern = os.path.join("myob", "*.json")
        existing_files = glob.glob(pattern)
        # ... (rest of the function is fine)
        next_number = max(existing_numbers, default=0) + 1
        return f"TS{next_number:03d}"

    def _create_simple_email_summary(self, email: EmailData) -> str:
        # ... (this function is fine as is)
        return summary
    
    def _create_markdown_from_analysis(self, email: EmailData, analysis: Dict[str, Any], pdf_content: str = "") -> str:
        """Create markdown summary from LLM analysis."""
        
        analysis_text = analysis.get("analysis_text", "No analysis available")
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        markdown = f"""# Email Analysis Report
        
**Subject:** {email.subject}  
**From:** {email.sender}  
**Date:** {timestamp}  
**Email ID:** {email.gmail_id}

## LLM Analysis

{analysis_text}

## Technical Details

- **Content Length:** {analysis.get('content_length', 0)} characters
- **Has Context:** {'Yes' if analysis.get('has_context') else 'No'}
- **Context Sources:** {analysis.get('context_count', 0)}
- **Analysis Timestamp:** {analysis.get('analysis_timestamp', 'Unknown')}

## Original Email Content

### Email Body
```
{email.body or 'No email body'}
```

"""
        
        if pdf_content:
            markdown += f"""
### PDF Content
```
{pdf_content[:2000]}{'...' if len(pdf_content) > 2000 else ''}
```
"""
        
        if analysis.get('error'):
            markdown += f"""
## Processing Errors
- {analysis['error']}
"""
        
        return markdown

    # --- THIS METHOD CONTAINS THE PRIMARY FIX FOR THE SUPABASE SAVE ERROR ---
    def _save_to_supabase(self, processed_order: ProcessedOrder) -> bool:
        """Save processed order Pydantic object to Supabase database."""
        try:
            # FIX: Pass the entire Pydantic object directly to the service.
            # Do NOT convert it to a dictionary here. The database service
            # should be responsible for handling the Pydantic model.
            result = self.db_service.save_processed_order(processed_order)
            
            if result:
                logger.info(f"Successfully saved order to Supabase for email: {processed_order.email_id}")
                return True
            else:
                logger.error(f"Failed to save order to Supabase for email: {processed_order.email_id}")
                return False
                
        except Exception as e:
            logger.error(f"Error saving to Supabase: {e}")
            return False

    # In main_processor.py

    async def run_extraction_workflow(self, label_names: Optional[List[str]] = None, max_emails: int = 40, 
                      time_filter: Optional[str] = None, review_mode: bool = False) -> List[ProcessedOrder]:
        """
        Main workflow method for email extraction.
        """
        logger.info("Starting email processing workflow (extraction-only)")
        
        # --- THIS IS THE CRITICAL FIX ---
        # The 'emails' variable is now correctly defined by calling the fetching method.
        emails = await self._fetch_emails_for_processing(label_names, max_emails, time_filter)
        
        if not emails:
            logger.info("No emails found to process")
            return []
        
        # Filter out reply emails (Re: subjects) unless in review mode
        if not review_mode:
            original_count = len(emails)
            emails = [email for email in emails if not email.subject.strip().lower().startswith('re:')]
            filtered_count = original_count - len(emails)
            if filtered_count > 0:
                logger.info(f"Filtered out {filtered_count} reply emails (Re: subjects)")
        
        logger.info(f"Found {len(emails)} emails to process")
        processed_orders = []
        
        for email in emails:
            try:
                processed_order = await self._process_single_email(email, review_mode)
                if processed_order:
                    processed_orders.append(processed_order)
            except Exception as e:
                # This is the corrected error handler from our previous steps
                email_id = getattr(email, 'gmail_id', 'Unknown')
                if email_id == 'Unknown':
                    email_id = getattr(email, 'id', 'Unknown')
                
                email_subject = getattr(email, 'subject', 'Unknown Subject')
                
                logger.error(f"Error processing email {email_id} ('{email_subject}'): {e}")
                
                try:
                    failed_order = ProcessedOrder(
                        email_id=email_id,
                        email_subject=email_subject,
                        email_data=email,
                        extracted_data=None,
                        markdown_summary=f"# Processing Failed\n\nError: {str(e)}",
                        myob_payload=None,
                        markdown_filepath="",
                        myob_filepath=""
                    )
                    
                    try:
                        self.gmail_service.mark_email_failed(email_id)
                        logger.info(f"Marked failed email as 'Failed': {email_id}")
                    except Exception as label_error:
                        logger.error(f"Failed to label email as failed: {label_error}")
                    
                    processed_orders.append(failed_order)
                    
                except Exception as order_error:
                    logger.error(f"Failed to create failed order record: {order_error}")
                
                continue
        
        logger.info(f"Successfully processed {len(processed_orders)} orders")
        return processed_orders
    
    
    async def _process_single_email(self, email: EmailData, review_mode: bool = False) -> Optional[ProcessedOrder]:
        """Process a single email and return a ProcessedOrder object."""
        logger.info(f"Processing email: {email.subject} (ID: {email.gmail_id})")
        
        try:
            # Build content for processing
            content_parts = []
            
            # Add email body
            if email.body:
                content_parts.append(f"Email Body:\n{email.body}")
            
            # Extract PDF content if available
            pdf_content = ""
            if email.attachments:
                for attachment in email.attachments:
                    if attachment.filename.lower().endswith('.pdf'):
                        try:
                            pdf_text = extract_text_from_pdf(attachment.data)
                            if pdf_text:
                                pdf_content += f"\nPDF Content from {attachment.filename}:\n{pdf_text}\n"
                        except Exception as e:
                            logger.warning(f"Failed to extract PDF {attachment.filename}: {e}")
            
            if pdf_content:
                content_parts.append(pdf_content)
            
            full_content = "\n\n".join(content_parts)
            
            if not full_content.strip():
                logger.warning(f"No content found in email {email.gmail_id}")
                return None
            
            # Generate unstructured LLM analysis (no validation)
            logger.info("Generating comprehensive email analysis...")
            email_analysis = await self.llm_service.analyze_email_content(full_content, email.sender)
            
            # Create markdown summary from analysis
            markdown_summary = self._create_markdown_from_analysis(email, email_analysis, pdf_content)
            
            # Try to extract structured order data for MYOB (only if needed)
            myob_payload = None
            structured_order_data = None
            
            # Only attempt MYOB extraction if the analysis suggests this might be an order
            analysis_text = email_analysis.get("analysis_text", "").lower()
            if any(keyword in analysis_text for keyword in ["order", "purchase", "po ", "quantity", "delivery"]):
                logger.info("Analysis suggests potential order - attempting MYOB extraction...")
                try:
                    structured_order_data = await self.llm_service.extract_order_data_for_myob(email_analysis)
                    
                    if structured_order_data and structured_order_data.get("is_order"):
                        logger.info("Successfully extracted structured order data for MYOB")
                        # Generate MYOB payload with validation (only at this point)
                        myob_payload = self.llm_service.generate_myob_payload(structured_order_data)
                    else:
                        logger.info("Email analysis did not yield valid order data for MYOB")
                        
                except Exception as e:
                    logger.warning(f"MYOB extraction failed, but continuing with analysis: {e}")
            else:
                logger.info("Analysis does not suggest this is an order - skipping MYOB extraction")
            
            # Save files
            markdown_filepath = self._save_markdown_file(email.subject, markdown_summary)
            myob_filepath = ""
            if myob_payload:
                myob_filepath = self._save_myob_file(email.subject, myob_payload)
            
            # Create processed order object with analysis data
            processed_order = ProcessedOrder(
                email_id=email.gmail_id,
                email_subject=email.subject,
                email_data=email,
                extracted_data=structured_order_data,  # This may be None if not an order
                markdown_summary=markdown_summary,
                myob_payload=myob_payload,
                markdown_filepath=markdown_filepath,
                myob_filepath=myob_filepath,
                analysis_data=email_analysis  # Store the full LLM analysis
            )
            
            # Save to Supabase
            self._save_to_supabase(processed_order)
            
            # Mark email as successfully processed in Gmail
            try:
                self.gmail_service.mark_email_processed(email.gmail_id)
                logger.info(f"Marked email as 'Processed': {email.gmail_id}")
            except Exception as label_error:
                logger.error(f"Failed to add 'Processed' label to email {email.gmail_id}: {label_error}")
            
            return processed_order
            
        except Exception as e:
            logger.error(f"Error processing email {email.gmail_id}: {e}")
            raise

    async def run_intelligent_workflow(self, label_names: Optional[List[str]] = None, 
                                     max_emails: int = 40, time_filter: Optional[str] = None) -> Dict[str, Any]:
        """Run intelligent email processing with categorization and smart routing."""
        # ... (this workflow seems fine as is) ...
        # It correctly calls _process_order_email and _process_non_order_email, which in turn
        # call the corrected _process_single_email method. No changes needed here.
        return {
            "total_emails": len(emails),
            "classifications": classifications,
            "processed_orders": processed_orders,
            "dashboard_report": dashboard_report,
            "actionable_insights": actionable_insights
        }

    async def _fetch_emails_for_processing(self, label_names: Optional[List[str]], max_emails: int, time_filter: Optional[str]) -> List[EmailData]:
        """Fetch emails for processing."""
        if not label_names:
            label_names = config.GMAIL_LABELS_TO_PROCESS
        
        all_emails = []
        
        for label_name in label_names:
            logger.info(f"Processing label: {label_name}")
            label_id = self.gmail_service.get_label_id(label_name)
            if not label_id:
                logger.warning(f"Label '{label_name}' not found")
                continue
            
            try:
                # Build query with time filter if provided
                query_parts = []
                if time_filter:
                    query_parts.append(time_filter)
                
                # Default to looking for attachments unless in review mode
                query_parts.append("has:attachment")
                
                query = " ".join(query_parts) if query_parts else None
                
                response = self.gmail_service.service.users().messages().list(
                    userId='me',
                    labelIds=[label_id],
                    q=query,
                    maxResults=max_emails
                ).execute()
                
                messages = response.get('messages', [])
                logger.info(f"Found {len(messages)} emails in label '{label_name}'")
                
                for msg_ref in messages:
                    email_data = self.gmail_service._get_email_details(msg_ref['id'], label_name)
                    if email_data:
                        all_emails.append(email_data)
                        
            except Exception as e:
                logger.error(f"Error fetching emails from {label_name}: {e}")
                continue
        
        logger.info(f"Total emails fetched: {len(all_emails)}")
        return all_emails

    async def _process_order_email(self, email: EmailData, classification: EmailClassification) -> Optional[ProcessedOrder]:
        """Process an email that has been classified as an order."""
        try:
            return await self._process_single_email(email, review_mode=False)
        except Exception as e:
            logger.error(f"❌ Order processing failed for {email.gmail_id}: {e}")
            return None

    async def _process_non_order_email(self, email: EmailData, classification: EmailClassification) -> Optional[ProcessedOrder]:
        """Process an email that is NOT an order."""
        # ... (this function is fine as is) ...
        return ProcessedOrder(...)