"""
Email Categorization System
Intelligently categorizes emails and provides actionable next steps.
"""

import logging
from typing import Dict, Any, Optional, List
from enum import Enum
from pydantic import BaseModel
from datetime import datetime

from models import EmailData
from llm_service import LLMService

logger = logging.getLogger(__name__)

class EmailCategory(Enum):
    """Email categories for classification."""
    ORDER = "order"
    INVOICE = "invoice"
    INQUIRY = "inquiry"
    COMPLAINT = "complaint"
    INFORMATION = "information"
    SPAM = "spam"
    INTERNAL = "internal"
    OTHER = "other"

class EmailPriority(Enum):
    """Email priority levels."""
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"

class EmailClassification(BaseModel):
    """Result of email classification."""
    category: EmailCategory
    priority: EmailPriority
    confidence: float
    is_order: bool
    summary: str
    next_steps: List[str]
    key_entities: Dict[str, Any]
    requires_response: bool
    estimated_response_time: str

class EmailCategorizer:
    """Intelligent email categorization system."""
    
    def __init__(self, llm_service: LLMService):
        self.llm_service = llm_service
        logger.info("Email Categorizer initialized")
    
    async def categorize_email(self, email_data: EmailData) -> EmailClassification:
        """Categorize an email and provide actionable insights."""
        logger.info(f"Categorizing email: {email_data.subject}")
        
        try:
            # Use LLM to analyze and categorize the email
            classification_result = await self._analyze_with_llm(email_data)
            
            # Create structured classification
            classification = EmailClassification(
                category=EmailCategory(classification_result.get('category', 'other')),
                priority=EmailPriority(classification_result.get('priority', 'medium')),
                confidence=classification_result.get('confidence', 0.5),
                is_order=classification_result.get('is_order', False),
                summary=classification_result.get('summary', ''),
                next_steps=classification_result.get('next_steps', []),
                key_entities=classification_result.get('key_entities', {}),
                requires_response=classification_result.get('requires_response', False),
                estimated_response_time=classification_result.get('estimated_response_time', '24 hours')
            )
            
            logger.info(f"Email categorized as: {classification.category.value} (confidence: {classification.confidence})")
            return classification
            
        except Exception as e:
            logger.error(f"Error categorizing email: {e}")
            # Return default classification
            return EmailClassification(
                category=EmailCategory.OTHER,
                priority=EmailPriority.MEDIUM,
                confidence=0.0,
                is_order=False,
                summary="Email could not be categorized due to technical issues.",
                next_steps=["Manual review required"],
                key_entities={},
                requires_response=True,
                estimated_response_time="Manual review"
            )
    
    async def _analyze_with_llm(self, email_data: EmailData) -> Dict[str, Any]:
        """Use LLM to analyze email content and extract classification data."""
        
        # Prepare email content for analysis
        email_content = f"""
        Subject: {email_data.subject}
        From: {email_data.sender}
        Body: {email_data.body}
        
        Attachments: {len(email_data.attachments)} files
        """
        
        # Create comprehensive analysis prompt
        analysis_prompt = f"""
        Analyze this email and provide a comprehensive classification in JSON format.
        
        Email Content:
        {email_content}
        
        Provide analysis in this exact JSON structure:
        {{
            "category": "order|invoice|inquiry|complaint|information|spam|internal|other",
            "priority": "high|medium|low",
            "confidence": 0.0-1.0,
            "is_order": true/false,
            "summary": "Brief 1-2 sentence summary of the email",
            "next_steps": ["actionable step 1", "actionable step 2"],
            "key_entities": {{
                "customer_name": "extracted customer name or null",
                "company": "extracted company name or null",
                "order_number": "extracted order/reference number or null",
                "amount": "extracted monetary amount or null",
                "deadline": "extracted deadline/date or null"
            }},
            "requires_response": true/false,
            "estimated_response_time": "immediate|2 hours|24 hours|3 days|manual review"
        }}
        
        Classification Guidelines:
        - "order": Purchase orders, order confirmations, order requests
        - "invoice": Invoices, payment requests, billing inquiries
        - "inquiry": Questions, requests for information, quotes
        - "complaint": Issues, problems, dissatisfaction
        - "information": Updates, notifications, announcements
        - "spam": Promotional, irrelevant, or suspicious content
        - "internal": Communications from colleagues (@teamsystems.net.au)
        - "other": Anything that doesn't fit above categories
        
        Priority Guidelines:
        - "high": Orders, urgent complaints, payment issues
        - "medium": General inquiries, information requests
        - "low": Informational updates, non-urgent communications
        
        Return only valid JSON.
        """
        
        try:
            # Use the LLM service to analyze the email
            response = await self.llm_service.mistral_service.generate_content(analysis_prompt)
            
            # Parse the JSON response
            import json
            classification_data = json.loads(response.text)
            
            # Validate and clean the response
            return self._validate_classification_data(classification_data)
            
        except Exception as e:
            logger.error(f"LLM analysis failed: {e}")
            raise
    
    def _validate_classification_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and clean classification data from LLM."""
        
        # Ensure required fields exist with defaults
        validated_data = {
            'category': data.get('category', 'other'),
            'priority': data.get('priority', 'medium'),
            'confidence': float(data.get('confidence', 0.5)),
            'is_order': bool(data.get('is_order', False)),
            'summary': str(data.get('summary', 'Email analysis unavailable')),
            'next_steps': data.get('next_steps', ['Manual review required']),
            'key_entities': data.get('key_entities', {}),
            'requires_response': bool(data.get('requires_response', True)),
            'estimated_response_time': data.get('estimated_response_time', '24 hours')
        }
        
        # Validate enum values
        valid_categories = [cat.value for cat in EmailCategory]
        if validated_data['category'] not in valid_categories:
            validated_data['category'] = 'other'
        
        valid_priorities = [pri.value for pri in EmailPriority]
        if validated_data['priority'] not in valid_priorities:
            validated_data['priority'] = 'medium'
        
        # Ensure confidence is between 0 and 1
        validated_data['confidence'] = max(0.0, min(1.0, validated_data['confidence']))
        
        # Ensure next_steps is a list
        if not isinstance(validated_data['next_steps'], list):
            validated_data['next_steps'] = ['Manual review required']
        
        return validated_data
    
    def generate_dashboard_report(self, classifications: List[EmailClassification]) -> Dict[str, Any]:
        """Generate dashboard report from email classifications."""
        
        if not classifications:
            return {
                'total_emails': 0,
                'categories': {},
                'priorities': {},
                'orders_found': 0,
                'response_required': 0,
                'summary': 'No emails processed'
            }
        
        # Count categories
        category_counts = {}
        for classification in classifications:
            cat = classification.category.value
            category_counts[cat] = category_counts.get(cat, 0) + 1
        
        # Count priorities
        priority_counts = {}
        for classification in classifications:
            pri = classification.priority.value
            priority_counts[pri] = priority_counts.get(pri, 0) + 1
        
        # Count orders and responses needed
        orders_found = sum(1 for c in classifications if c.is_order)
        response_required = sum(1 for c in classifications if c.requires_response)
        
        # Generate summary
        total_emails = len(classifications)
        summary = f"Processed {total_emails} emails: {orders_found} orders, {response_required} require response"
        
        return {
            'total_emails': total_emails,
            'categories': category_counts,
            'priorities': priority_counts,
            'orders_found': orders_found,
            'response_required': response_required,
            'high_priority': priority_counts.get('high', 0),
            'summary': summary,
            'timestamp': datetime.now().isoformat()
        }
    
    def get_actionable_insights(self, classifications: List[EmailClassification]) -> List[Dict[str, Any]]:
        """Get actionable insights from email classifications."""
        
        insights = []
        
        # High priority emails needing immediate attention
        high_priority = [c for c in classifications if c.priority == EmailPriority.HIGH]
        if high_priority:
            insights.append({
                'type': 'urgent',
                'title': f'{len(high_priority)} High Priority Emails',
                'description': 'These emails require immediate attention',
                'action': 'Review and respond to high priority emails first',
                'emails': [{'subject': c.summary, 'next_steps': c.next_steps} for c in high_priority[:3]]
            })
        
        # Orders ready for processing
        orders = [c for c in classifications if c.is_order]
        if orders:
            insights.append({
                'type': 'orders',
                'title': f'{len(orders)} Orders Found',
                'description': 'Purchase orders ready for processing',
                'action': 'Process orders through MYOB integration',
                'emails': [{'subject': c.summary, 'entities': c.key_entities} for c in orders[:3]]
            })
        
        # Complaints needing response
        complaints = [c for c in classifications if c.category == EmailCategory.COMPLAINT]
        if complaints:
            insights.append({
                'type': 'complaints',
                'title': f'{len(complaints)} Complaints',
                'description': 'Customer complaints requiring response',
                'action': 'Prioritize complaint resolution',
                'emails': [{'subject': c.summary, 'next_steps': c.next_steps} for c in complaints[:3]]
            })
        
        return insights