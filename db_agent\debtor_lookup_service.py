#!/usr/bin/env python3
"""
Debtor Lookup Service - Fuzzy matching for customer identification
Matches email senders to debtor IDs from active customers CSV using fuzzy string matching.
"""

import csv
import logging
import re
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from pathlib import Path
import pandas as pd
from fuzzywuzzy import fuzz, process
from email.utils import parseaddr

logger = logging.getLogger(__name__)

@dataclass
class CustomerRecord:
    """Represents a customer record from the active debtors CSV."""
    customer_name: str
    debtor_id: int
    instructions_po_path: Optional[str] = None
    
    @property
    def domain_keywords(self) -> List[str]:
        """Extract domain-relevant keywords from company name."""
        # Remove common business suffixes and clean up
        name = self.customer_name.upper()
        # Remove common suffixes
        suffixes = ['PTY LTD', 'PTY. LTD', 'P/L', 'LIMITED', 'LTD', 'INC', 'INCORPORATED', 
                   'COMPANY', 'CO', 'CORP', 'CORPORATION', 'GROUP', 'ENTERPRISES', 'SERVICES']
        
        for suffix in suffixes:
            name = name.replace(suffix, '').strip()
        
        # Remove special characters and split into words
        name = re.sub(r'[^\w\s]', ' ', name)
        words = [word.strip() for word in name.split() if len(word.strip()) > 2]
        
        return words
    
    @property
    def search_variants(self) -> List[str]:
        """Generate search variants for better matching."""
        variants = [self.customer_name.upper()]
        
        # Add version without business suffixes
        clean_name = self.customer_name.upper()
        suffixes = ['PTY LTD', 'PTY. LTD', 'P/L', 'LIMITED', 'LTD', 'INC', 'INCORPORATED']
        for suffix in suffixes:
            clean_name = clean_name.replace(suffix, '').strip()
        variants.append(clean_name)
        
        # Add acronym if company has multiple words
        words = clean_name.split()
        if len(words) > 1:
            acronym = ''.join(word[0] for word in words if word)
            variants.append(acronym)
        
        return list(set(variants))  # Remove duplicates


class DebtorLookupService:
    """Service for fuzzy matching email senders to debtor IDs."""
    
    def __init__(self, csv_path: str = "docs/active_debtors.csv", threshold: int = 80):
        """
        Initialize the debtor lookup service.
        
        Args:
            csv_path: Path to the active debtors CSV file
            threshold: Minimum fuzzy match score (0-100)
        """
        self.csv_path = Path(csv_path)
        self.threshold = threshold
        self.customers: List[CustomerRecord] = []
        self.domain_to_customer: Dict[str, CustomerRecord] = {}
        self.name_variants: Dict[str, CustomerRecord] = {}
        
        self._load_customers()
        logger.info(f"Loaded {len(self.customers)} customers for fuzzy matching")
    
    def _load_customers(self):
        """Load customer data from CSV file."""
        if not self.csv_path.exists():
            raise FileNotFoundError(f"Active debtors CSV not found: {self.csv_path}")
        
        try:
            with open(self.csv_path, 'r', encoding='utf-8-sig') as file:
                reader = csv.DictReader(file)
                for row in reader:
                    # Handle potential BOM in column names
                    customer_name_key = next((k for k in row.keys() if 'customer_name' in k), None)
                    if customer_name_key and row[customer_name_key] and row['debtor_id']:
                        customer = CustomerRecord(
                            customer_name=row[customer_name_key].strip(),
                            debtor_id=int(row['debtor_id']),
                            instructions_po_path=row.get('instructions_po_path', '').strip() or None
                        )
                        self.customers.append(customer)
                        
                        # Build search indices
                        for variant in customer.search_variants:
                            self.name_variants[variant] = customer
            
            logger.info(f"Successfully loaded {len(self.customers)} customer records")
            
        except Exception as e:
            logger.error(f"Error loading customers from CSV: {e}")
            raise
    
    def find_debtor_by_customer_name(self, customer_name: str) -> Optional[Dict[str, Any]]:
        """
        Find debtor ID by customer name using fuzzy matching.
        
        Args:
            customer_name: Customer name to match against
            
        Returns:
            Dictionary with match details or None if no match found
        """
        if not customer_name or len(customer_name.strip()) < 3:
            return None
        
        # Clean and normalize the input customer name
        query_clean = customer_name.upper().strip()
        
        # Remove common business suffixes for better matching
        suffixes = ['PTY LTD', 'PTY. LTD', 'P/L', 'LIMITED', 'LTD', 'INC', 'INCORPORATED', 
                   'COMPANY', 'CO', 'CORP', 'CORPORATION', 'GROUP', 'ENTERPRISES', 'SERVICES']
        
        query_variants = [query_clean]
        for suffix in suffixes:
            if suffix in query_clean:
                variant = query_clean.replace(suffix, '').strip()
                if variant:
                    query_variants.append(variant)
        
        best_match = None
        best_score = 0
        
        for customer in self.customers:
            # Test against all customer variants
            for customer_variant in customer.search_variants:
                for query_variant in query_variants:
                    # Skip if the strings are too different in length (prevents false positives)
                    length_ratio = min(len(query_variant), len(customer_variant)) / max(len(query_variant), len(customer_variant))
                    if length_ratio < 0.3:  # Skip if one string is less than 30% the length of the other
                        continue
                    
                    # Try different fuzzy matching algorithms
                    scores = [
                        fuzz.ratio(query_variant, customer_variant),
                        fuzz.partial_ratio(query_variant, customer_variant),
                        fuzz.token_sort_ratio(query_variant, customer_variant),
                        fuzz.token_set_ratio(query_variant, customer_variant)
                    ]
                    
                    max_score = max(scores)
                    
                    # Additional validation: ensure there's meaningful overlap
                    if max_score >= self.threshold:
                        # Check for meaningful word overlap
                        query_words = set(query_variant.split())
                        customer_words = set(customer_variant.split())
                        
                        # Remove very short words that might cause false matches
                        query_words = {w for w in query_words if len(w) > 2}
                        customer_words = {w for w in customer_words if len(w) > 2}
                        
                        if query_words and customer_words:
                            word_overlap = len(query_words.intersection(customer_words)) / len(query_words.union(customer_words))
                            
                            # Require at least some word overlap for high scores
                            if max_score >= 95 and word_overlap < 0.3:
                                continue  # Skip this match as it's likely a false positive
                    
                    if max_score > best_score and max_score >= self.threshold:
                        best_score = max_score
                        best_match = {
                            'debtor_id': customer.debtor_id,
                            'customer_name': customer.customer_name,
                            'confidence_score': max_score,
                            'match_method': 'customer_name_fuzzy',
                            'instructions_path': customer.instructions_po_path,
                            'matched_text': f"'{query_variant}' -> '{customer_variant}'"
                        }
        
        if best_match:
            logger.info(f"Customer name match: {best_match['customer_name']} (ID: {best_match['debtor_id']}, Score: {best_score})")
            return best_match
        
        logger.info(f"No customer name match found for '{customer_name}' (best score: {best_score})")
        return None

    def find_debtor_by_email(self, email_address: str, sender_name: str = None) -> Optional[Dict[str, Any]]:
        """
        Find debtor ID by email address using fuzzy matching focused on customer names.
        
        Args:
            email_address: Full email address (e.g., "<EMAIL>")
            sender_name: Optional sender display name
            
        Returns:
            Dictionary with match details or None if no match found
        """
        if not email_address:
            return None
        
        # Parse email to get domain and sender name
        parsed_name, parsed_email = parseaddr(email_address)
        if not parsed_email or '@' not in parsed_email:
            logger.warning(f"Invalid email format: {email_address}")
            return None
        
        domain = parsed_email.split('@')[1].lower()
        
        # Try multiple matching strategies prioritizing customer name matching
        strategies = [
            self._match_by_sender_name,           # Priority 1: Direct sender name matching
            self._match_by_domain_exact,          # Priority 2: Known domain mappings
            self._match_by_company_keywords,      # Priority 3: Keyword extraction from sender/domain
            self._match_by_domain_fuzzy,          # Priority 4: Domain-based fuzzy matching
        ]
        
        best_match = None
        best_score = 0
        
        for strategy in strategies:
            try:
                match = strategy(domain, sender_name or parsed_name, email_address)
                if match and match['confidence_score'] > best_score:
                    best_match = match
                    best_score = match['confidence_score']
                    
                    # If we have a high-confidence match, return it
                    if best_score >= 90:
                        break
                        
            except Exception as e:
                logger.warning(f"Error in matching strategy {strategy.__name__}: {e}")
        
        if best_match and best_score >= self.threshold:
            logger.info(f"Found debtor match: {best_match['customer_name']} (ID: {best_match['debtor_id']}, Score: {best_score})")
            return best_match
        
        logger.info(f"No debtor match found for {email_address} (best score: {best_score})")
        return None
    
    def _match_by_domain_exact(self, domain: str, sender_name: str, email: str) -> Optional[Dict[str, Any]]:
        """Try exact domain matching with known domain patterns."""
        domain_mappings = {
            'woolworths.com.au': 'WOOLWORTHS LIMITED',
            'woolworthsgroup.com.au': 'WOOLWORTHS LIMITED',
            'endeavourgroup.com.au': 'ENDEAVOUR GROUP LIMITED',
            'coles.com.au': 'COLES',
            'colesgroup.com.au': 'COLES',
            'bunnings.com.au': 'BUNNINGS - HEAD OFFICE',
            'myer.com.au': 'MYER LIMITED',
            'target.com.au': 'TARGET',
            'kmart.com.au': 'KMART'
        }
        
        if domain in domain_mappings:
            expected_name = domain_mappings[domain]
            for customer in self.customers:
                if expected_name.upper() in customer.customer_name.upper():
                    return {
                        'debtor_id': customer.debtor_id,
                        'customer_name': customer.customer_name,
                        'confidence_score': 100,
                        'match_method': 'domain_exact',
                        'instructions_path': customer.instructions_po_path,
                        'matched_text': domain
                    }
        
        return None
    
    def _match_by_domain_fuzzy(self, domain: str, sender_name: str, email: str) -> Optional[Dict[str, Any]]:
        """Match by fuzzy comparison of domain to company names."""
        # Extract company name from domain
        domain_parts = domain.replace('.com', '').replace('.au', '').replace('.net', '').replace('.org', '')
        domain_parts = domain_parts.replace('.', ' ').replace('-', ' ')
        
        best_match = None
        best_score = 0
        
        for customer in self.customers:
            # Try matching domain parts against company name variants
            for variant in customer.search_variants:
                score = fuzz.partial_ratio(domain_parts.upper(), variant)
                if score > best_score and score >= self.threshold:
                    best_score = score
                    best_match = {
                        'debtor_id': customer.debtor_id,
                        'customer_name': customer.customer_name,
                        'confidence_score': score,
                        'match_method': 'domain_fuzzy',
                        'instructions_path': customer.instructions_po_path,
                        'matched_text': f"{domain_parts} -> {variant}"
                    }
        
        return best_match
    
    def _match_by_sender_name(self, domain: str, sender_name: str, email: str) -> Optional[Dict[str, Any]]:
        """Match by sender name if it contains company information."""
        if not sender_name or len(sender_name) < 3:
            return None
        
        # Clean sender name
        sender_clean = re.sub(r'[^\w\s]', ' ', sender_name.upper()).strip()
        
        best_match = None
        best_score = 0
        
        for customer in self.customers:
            for variant in customer.search_variants:
                score = fuzz.partial_ratio(sender_clean, variant)
                if score > best_score and score >= self.threshold:
                    best_score = score
                    best_match = {
                        'debtor_id': customer.debtor_id,
                        'customer_name': customer.customer_name,
                        'confidence_score': score,
                        'match_method': 'sender_name',
                        'instructions_path': customer.instructions_po_path,
                        'matched_text': f"{sender_clean} -> {variant}"
                    }
        
        return best_match
    
    def _match_by_company_keywords(self, domain: str, sender_name: str, email: str) -> Optional[Dict[str, Any]]:
        """Match by extracting keywords from domain and sender name."""
        # Extract keywords from domain
        domain_keywords = set()
        domain_clean = domain.replace('.com', '').replace('.au', '').replace('.net', '').replace('.org', '')
        domain_parts = re.sub(r'[^\w]', ' ', domain_clean).split()
        domain_keywords.update(word.upper() for word in domain_parts if len(word) > 2)
        
        # Extract keywords from sender name
        if sender_name:
            sender_parts = re.sub(r'[^\w\s]', ' ', sender_name).split()
            domain_keywords.update(word.upper() for word in sender_parts if len(word) > 2)
        
        if not domain_keywords:
            return None
        
        best_match = None
        best_score = 0
        
        for customer in self.customers:
            customer_keywords = set(customer.domain_keywords)
            
            # Calculate keyword overlap
            common_keywords = domain_keywords.intersection(customer_keywords)
            if common_keywords:
                # Score based on keyword overlap and fuzzy matching
                overlap_score = len(common_keywords) / max(len(domain_keywords), len(customer_keywords)) * 100
                
                # Also do fuzzy matching on the full names
                fuzzy_score = max(
                    fuzz.partial_ratio(' '.join(domain_keywords), variant)
                    for variant in customer.search_variants
                )
                
                # Combined score
                combined_score = (overlap_score * 0.6) + (fuzzy_score * 0.4)
                
                if combined_score > best_score and combined_score >= self.threshold:
                    best_score = combined_score
                    best_match = {
                        'debtor_id': customer.debtor_id,
                        'customer_name': customer.customer_name,
                        'confidence_score': int(combined_score),
                        'match_method': 'keyword_overlap',
                        'instructions_path': customer.instructions_po_path,
                        'matched_text': f"Keywords: {common_keywords}"
                    }
        
        return best_match
    
    def search_customers(self, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Search customers by name with fuzzy matching.
        
        Args:
            query: Search query
            limit: Maximum number of results
            
        Returns:
            List of matching customers with scores
        """
        if not query or len(query) < 2:
            return []
        
        query_clean = query.upper().strip()
        results = []
        
        for customer in self.customers:
            best_score = 0
            best_variant = customer.customer_name
            
            # Check all variants for best match
            for variant in customer.search_variants:
                score = fuzz.partial_ratio(query_clean, variant)
                if score > best_score:
                    best_score = score
                    best_variant = variant
            
            if best_score >= 50:  # Lower threshold for search
                results.append({
                    'debtor_id': customer.debtor_id,
                    'customer_name': customer.customer_name,
                    'confidence_score': best_score,
                    'instructions_path': customer.instructions_po_path,
                    'matched_variant': best_variant
                })
        
        # Sort by score and return top results
        results.sort(key=lambda x: x['confidence_score'], reverse=True)
        return results[:limit]
    
    def get_customer_by_debtor_id(self, debtor_id: int) -> Optional[CustomerRecord]:
        """Get customer record by debtor ID."""
        for customer in self.customers:
            if customer.debtor_id == debtor_id:
                return customer
        return None
    
    def get_instructions_path(self, debtor_id: int) -> Optional[str]:
        """Get PDF parsing instructions file path for a debtor ID."""
        customer = self.get_customer_by_debtor_id(debtor_id)
        return customer.instructions_po_path if customer else None
    
    def reload_customers(self):
        """Reload customer data from CSV file."""
        self.customers.clear()
        self.domain_to_customer.clear()
        self.name_variants.clear()
        self._load_customers()
        logger.info("Customer data reloaded")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get statistics about loaded customers."""
        customers_with_instructions = sum(1 for c in self.customers if c.instructions_po_path)
        
        return {
            'total_customers': len(self.customers),
            'customers_with_instructions': customers_with_instructions,
            'customers_without_instructions': len(self.customers) - customers_with_instructions,
            'csv_path': str(self.csv_path),
            'threshold': self.threshold
        }


def main():
    """CLI interface for testing the debtor lookup service."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Test debtor lookup service')
    parser.add_argument('--email', help='Test email address')
    parser.add_argument('--sender', help='Test sender name')
    parser.add_argument('--customer', help='Test customer name matching')
    parser.add_argument('--search', help='Search customers by name')
    parser.add_argument('--stats', action='store_true', help='Show service statistics')
    parser.add_argument('--threshold', type=int, default=80, help='Fuzzy match threshold (default: 80)')
    parser.add_argument('--csv', default='docs/active_debtors.csv', help='Path to CSV file')
    
    args = parser.parse_args()
    
    # Setup logging
    logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
    
    try:
        service = DebtorLookupService(csv_path=args.csv, threshold=args.threshold)
        
        if args.stats:
            stats = service.get_stats()
            print("=== Debtor Lookup Service Statistics ===")
            for key, value in stats.items():
                print(f"{key}: {value}")
        
        if args.email:
            print(f"\n=== Testing Email: {args.email} ===")
            result = service.find_debtor_by_email(args.email, args.sender)
            if result:
                print("Match found:")
                for key, value in result.items():
                    print(f"  {key}: {value}")
            else:
                print("No match found")
        
        if args.customer:
            print(f"\n=== Testing Customer Name: {args.customer} ===")
            result = service.find_debtor_by_customer_name(args.customer)
            if result:
                print("Match found:")
                for key, value in result.items():
                    print(f"  {key}: {value}")
            else:
                print("No match found")
        
        if args.search:
            print(f"\n=== Searching for: {args.search} ===")
            results = service.search_customers(args.search)
            if results:
                for i, result in enumerate(results, 1):
                    print(f"{i}. {result['customer_name']} (ID: {result['debtor_id']}, Score: {result['confidence_score']})")
            else:
                print("No matches found")
    
    except Exception as e:
        print(f"Error: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())