{"name": "Session Handover Generator", "description": "Generates a handover temp file at the end of a session to pass context to the next agent session", "trigger": {"type": "manual", "label": "Generate Session Handover"}, "action": {"type": "agent", "request": "Create a comprehensive handover file named 'session_handover_temp.md' that summarizes:\n\n1. Current session context and work completed\n2. Key files that were modified or created\n3. Outstanding tasks or issues that need attention\n4. Important decisions made during this session\n5. Next steps or recommendations for the following session\n\nThe file should be structured and detailed enough for the next agent to understand the current state and continue work seamlessly. Once this handover file has been read and processed by the next agent session, it should be deleted to keep the workspace clean."}}