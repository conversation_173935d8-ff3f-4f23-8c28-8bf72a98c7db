#!/usr/bin/env python3
"""
Demo script for the Enhanced Gmail CLI Tool

Shows various usage examples and capabilities of the gmail_cli.py tool.
"""

import subprocess
import sys
from rich.console import Console
from rich.panel import Panel
from rich.text import Text

console = Console()

def run_cli_command(command: str, description: str):
    """Run a CLI command and display it with description."""
    console.print(Panel(
        f"[bold blue]Command:[/bold blue] python gmail_cli.py {command}\n"
        f"[bold green]Description:[/bold green] {description}",
        title="📧 Gmail CLI Demo",
        border_style="cyan"
    ))
    
    print(f"\n🚀 Running: python gmail_cli.py {command}")
    print("=" * 60)
    
    try:
        result = subprocess.run(
            [sys.executable, "gmail_cli.py"] + command.split(),
            capture_output=False,
            text=True
        )
        return result.returncode == 0
    except Exception as e:
        console.print(f"❌ Error running command: {e}", style="red")
        return False

def main():
    """Run Gmail CLI demonstrations."""
    console.print(Panel.fit(
        "📧 Gmail CLI Tool Demonstration\n"
        "Showcasing various query options and rich interface features",
        style="bold magenta"
    ))
    
    demos = [
        {
            "command": "query --days 7 --has-attachment --format table",
            "description": "Search emails from last 7 days with attachments in table format"
        },
        {
            "command": "query --from-sender 'orders@' --unread-only --detailed",
            "description": "Find unread emails from senders containing 'orders@' with detailed info"
        },
        {
            "command": "query --attachment-type pdf --format tree",
            "description": "Search emails with PDF attachments displayed in tree format"
        },
        {
            "command": "query --subject 'Purchase Order' --days 30",
            "description": "Find emails with 'Purchase Order' in subject from last 30 days"
        },
        {
            "command": "query --label 'Brady' --label 'RSEA' --format table",
            "description": "Search emails with Brady or RSEA labels"
        },
        {
            "command": "query --larger-than 1M --has-attachment",
            "description": "Find large emails (>1MB) with attachments"
        },
        {
            "command": "labels",
            "description": "List all Gmail labels with statistics"
        },
        {
            "command": "query --custom-query 'has:attachment filename:pdf after:2024/01/01'",
            "description": "Custom query for PDF attachments after Jan 1, 2024"
        }
    ]
    
    console.print("\n[bold yellow]Available Demonstrations:[/bold yellow]")
    for i, demo in enumerate(demos, 1):
        console.print(f"  {i}. {demo['description']}")
    
    console.print("\n[dim]Note: These are example commands. The actual CLI will authenticate with Gmail and show real results.[/dim]")
    
    # Show help
    console.print("\n" + "="*60)
    console.print("[bold cyan]📖 For full help and options:[/bold cyan]")
    console.print("python gmail_cli.py --help")
    console.print("python gmail_cli.py query --help")
    console.print("python gmail_cli.py labels --help")
    
    # Interactive mode example
    console.print("\n" + "="*60)
    console.print("[bold green]🎮 Interactive Mode Example:[/bold green]")
    console.print("python gmail_cli.py query --days 7 --format interactive")
    console.print("[dim]This opens an interactive browser to navigate through emails[/dim]")

if __name__ == "__main__":
    main()