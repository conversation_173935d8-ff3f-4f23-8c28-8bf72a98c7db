# Enhanced Gmail CLI Tool

A powerful command-line interface for Gmail operations with rich console output, multiple query options, and interactive features.

## 🌟 Features

- **Rich Console Interface**: Beautiful, colorful output with progress indicators
- **Multiple Query Options**: Comprehensive search filters for emails
- **Interactive Email Browser**: Navigate through emails with keyboard shortcuts
- **Label Management**: Create and manage Gmail labels
- **Multiple Display Formats**: Table, tree, and interactive views
- **Advanced Filtering**: Date ranges, attachments, senders, content, and more
- **Real-time Progress**: Visual feedback during operations

## 🚀 Quick Start

### Installation

```bash
# Install dependencies (includes rich library)
pip install -r requirements.txt

# Make CLI executable
chmod +x gmail_cli.py
```

### Basic Usage

```bash
# Search emails from last 7 days with attachments
python gmail_cli.py query --days 7 --has-attachment

# Interactive email browser
python gmail_cli.py query --days 30 --format interactive

# List all Gmail labels
python gmail_cli.py labels
```

## 📋 Command Reference

### Query Command

Search and display emails with various filters:

```bash
python gmail_cli.py query [OPTIONS]
```

#### Date Filters
- `--days N` - Search emails from last N days
- `--after YYYY/MM/DD` - Search emails after specific date
- `--before YYYY/MM/DD` - Search emails before specific date

#### People Filters
- `--from-sender EMAIL` - Filter by sender email/domain
- `--to-recipient EMAIL` - Filter by recipient email/domain

#### Content Filters
- `--subject TEXT` - Filter by subject line
- `--contains TEXT` - Filter by email content

#### Attachment Filters
- `--has-attachment` - Only emails with attachments
- `--attachment-type TYPE` - Filter by attachment type (pdf, doc, xlsx, etc.)

#### Status Filters
- `--unread-only` - Only unread emails
- `--read-only` - Only read emails

#### Label Filters
- `--label LABEL` - Filter by label (can be used multiple times)

#### Size Filters
- `--larger-than SIZE` - Emails larger than size (e.g., 1M, 500K)
- `--smaller-than SIZE` - Emails smaller than size

#### Advanced Options
- `--custom-query QUERY` - Custom Gmail search query
- `--max-results N` - Maximum number of results (default: 50)

#### Display Options
- `--format FORMAT` - Output format: table, tree, interactive (default: table)
- `--detailed` - Show detailed information in table format

### Labels Command

Manage Gmail labels:

```bash
python gmail_cli.py labels [OPTIONS]
```

- `--create LABEL_NAME` - Create a new label

## 🎨 Display Formats

### Table Format (Default)
Clean tabular display with columns for ID, Subject, From, Date, and optional details.

```bash
python gmail_cli.py query --days 7 --format table --detailed
```

### Tree Format
Hierarchical display grouped by sender, showing email structure.

```bash
python gmail_cli.py query --from-sender "orders@" --format tree
```

### Interactive Format
Full-featured email browser with navigation and actions.

```bash
python gmail_cli.py query --days 30 --format interactive
```

#### Interactive Mode Controls
- `n` - Next email
- `p` - Previous email
- `d` - Show detailed email information
- `l` - List all email labels
- `m` - Mark as read/unread
- `q` - Quit browser

## 📚 Usage Examples

### Basic Searches

```bash
# Recent emails with attachments
python gmail_cli.py query --days 7 --has-attachment

# Unread emails from specific sender
python gmail_cli.py query --from-sender "<EMAIL>" --unread-only

# Emails with PDF attachments
python gmail_cli.py query --attachment-type pdf --days 30
```

### Advanced Searches

```bash
# Large emails with attachments from last month
python gmail_cli.py query --days 30 --larger-than 1M --has-attachment

# Purchase orders from multiple labels
python gmail_cli.py query --label "Brady" --label "RSEA" --subject "Purchase Order"

# Custom Gmail query
python gmail_cli.py query --custom-query "has:attachment filename:pdf after:2024/01/01"
```

### Label Management

```bash
# List all labels
python gmail_cli.py labels

# Create processing labels
python gmail_cli.py labels --create "Processed"
python gmail_cli.py labels --create "Review"
python gmail_cli.py labels --create "Failed"
```

### Interactive Browsing

```bash
# Browse recent emails interactively
python gmail_cli.py query --days 7 --format interactive

# Browse emails from specific sender
python gmail_cli.py query --from-sender "supplier@" --format interactive
```

## 🔧 Configuration

The CLI uses the same configuration as the main TeamsysV0.1 system:

### Environment Variables (.env)
```bash
# Gmail API Configuration
GMAIL_CREDENTIALS_FILE=credentials.json
GMAIL_TOKEN_FILE=token.pickle
GMAIL_LABELS_TO_PROCESS=Brady,RSEA,Woolworths

# Optional: Logging level
LOG_LEVEL=INFO
```

### Gmail API Setup
1. Create a project in Google Cloud Console
2. Enable Gmail API
3. Download OAuth 2.0 credentials as `credentials.json`
4. Place in project root directory

## 🎯 Rich Console Features

### Visual Elements
- **Progress Indicators**: Spinners and progress bars during operations
- **Color Coding**: Different colors for various email attributes
- **Tables**: Clean, formatted tables with proper alignment
- **Panels**: Bordered information panels for better readability
- **Trees**: Hierarchical display for grouped information

### Logging
- **Rich Logging**: Enhanced log messages with colors and formatting
- **Error Handling**: Detailed error messages with context
- **Debug Information**: Comprehensive debugging output when needed

## 🔍 Gmail Query Syntax

The CLI supports Gmail's native search syntax through the `--custom-query` option:

### Common Gmail Operators
- `from:<EMAIL>` - Emails from specific sender
- `to:<EMAIL>` - Emails to specific recipient
- `subject:"exact phrase"` - Exact subject match
- `has:attachment` - Emails with attachments
- `filename:pdf` - Emails with PDF attachments
- `after:2024/01/01` - Emails after date
- `before:2024/12/31` - Emails before date
- `larger:1M` - Emails larger than 1MB
- `smaller:500K` - Emails smaller than 500KB
- `is:unread` - Unread emails
- `label:labelname` - Emails with specific label

### Complex Queries
```bash
# Multiple conditions
python gmail_cli.py query --custom-query "from:orders@ has:attachment filename:pdf after:2024/01/01"

# OR conditions
python gmail_cli.py query --custom-query "(from:brady.com OR from:rsea.com.au) has:attachment"

# Exclude conditions
python gmail_cli.py query --custom-query "has:attachment -label:processed"
```

## 🛠️ Development

### Adding New Features

The CLI is designed to be extensible:

1. **New Query Options**: Add to `create_parser()` function
2. **New Display Formats**: Implement in `GmailCLI` class
3. **New Commands**: Add to subparsers and implement handler methods

### Code Structure

```
gmail_cli.py
├── GmailCLI class
│   ├── initialize_service()     # Gmail service setup
│   ├── build_query()           # Query construction
│   ├── search_emails()         # Email search
│   ├── display_*()             # Display methods
│   └── run_*_command()         # Command handlers
├── create_parser()             # CLI argument parsing
└── main()                      # Entry point
```

## 🐛 Troubleshooting

### Common Issues

**Gmail Authentication Errors**
```bash
# Delete token and re-authenticate
rm token.pickle
python gmail_cli.py query --days 1
```

**Rich Library Not Found**
```bash
# Install rich library
pip install rich>=13.0.0
```

**No Emails Found**
```bash
# Check query syntax
python gmail_cli.py query --custom-query "in:inbox" --max-results 10
```

### Debug Mode
```bash
# Enable debug logging
LOG_LEVEL=DEBUG python gmail_cli.py query --days 1
```

## 📄 License

This tool is part of the TeamsysV0.1 project and follows the same licensing terms.

## 🤝 Contributing

1. Follow the existing code style and patterns
2. Add comprehensive error handling
3. Include rich console formatting for new features
4. Update documentation for new options
5. Test with various Gmail configurations

## 📞 Support

For issues and questions:
- Check the troubleshooting section
- Review Gmail API documentation
- Ensure proper authentication setup
- Check environment variable configuration