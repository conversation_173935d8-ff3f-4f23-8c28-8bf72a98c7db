#!/usr/bin/env python3
"""
Test script to verify the Team Systems workflow prompt system is working correctly.
"""
import asyncio
import json
import logging
from llm_service.prompts.prompt_manager import prompt_manager

# Configure logging
logging.basicConfig(level=logging.INFO)

def test_customer_identification_scenarios():
    """Test various customer identification scenarios."""
    
    print("=" * 80)
    print("TEAM SYSTEMS WORKFLOW PROMPT TESTING")
    print("=" * 80)
    
    # Test scenarios that should correctly identify customers
    test_scenarios = [
        {
            "name": "Internal Staff Email - David about Sutton Tools",
            "sender_email": "<EMAIL>",
            "content": """Hi guys, please send me a tax invoice for Sutton Tools for PO#44422.
            
            They need:
            - 2x TRDP6/3 Drill Press
            - 1x MILL4 Milling Machine
            
            Ship to:
            Sutton Tools Pty Ltd
            123 Industrial Ave
            Dandenong VIC 3175""",
            "expected_customer": "Sutton Tools"
        },
        {
            "name": "External Customer Email - Direct from Woolworths",
            "sender_email": "<EMAIL>",
            "content": """PURCHASE ORDER WOL-12345
            
            Please supply:
            - 10x Safety Helmets
            - 5x High Vis Vests
            
            Deliver to:
            Woolworths Distribution Centre
            456 Logistics Way
            Sydney NSW 2000""",
            "expected_customer": "Woolworths"
        },
        {
            "name": "Internal Staff Email - Mitch forwarding RSEA order",
            "sender_email": "<EMAIL>",
            "content": """FW: Order from RSEA
            
            Forwarding this order from RSEA for processing:
            
            RSEA Purchase Order: RSE-789
            Account: RSEA Pty Ltd
            
            Items required:
            - 20x Safety Boots Size 10
            - 15x Work Gloves
            
            Ship to RSEA warehouse in Melbourne.""",
            "expected_customer": "RSEA"
        }
    ]
    
    print("\n🧪 TESTING CUSTOMER IDENTIFICATION SCENARIOS")
    print("-" * 60)
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\n📧 Test {i}: {scenario['name']}")
        print(f"   Sender: {scenario['sender_email']}")
        print(f"   Expected Customer: {scenario['expected_customer']}")
        
        # Generate the enhanced prompt
        try:
            enhanced_prompt = prompt_manager.get_enhanced_order_extraction_prompt(
                content=scenario['content'],
                sender_email=scenario['sender_email'],
                context=""
            )
            
            print(f"   ✅ Enhanced prompt generated successfully")
            
            # Check if the prompt contains the correct customer identification logic
            if "@teamsystems.net.au" in scenario['sender_email']:
                if "TEAM SYSTEMS EMPLOYEE, NOT a customer" in enhanced_prompt:
                    print(f"   ✅ Correctly identifies internal staff email")
                else:
                    print(f"   ❌ Failed to identify internal staff email")
            
            if "Look for the ACTUAL CUSTOMER" in enhanced_prompt:
                print(f"   ✅ Prompt includes customer identification instructions")
            else:
                print(f"   ❌ Missing customer identification instructions")
                
        except Exception as e:
            print(f"   ❌ Error generating prompt: {e}")
    
    return True

def test_workflow_system_prompt():
    """Test the workflow system prompt components."""
    
    print("\n🔧 TESTING WORKFLOW SYSTEM COMPONENTS")
    print("-" * 60)
    
    try:
        # Test workflow system prompt
        workflow_prompt = prompt_manager.get_workflow_system_prompt()
        print("✅ Workflow system prompt retrieved")
        
        # Check for key components
        key_components = [
            "Team Systems is ALWAYS the SUPPLIER",
            "MULTI-AGENT WORKFLOW RESPONSIBILITIES",
            "EMAIL PROCESSING AGENT",
            "ORDER EXTRACTION AGENT",
            "CUSTOMER MATCHING AGENT",
            "WORKFLOW DECISION TREE"
        ]
        
        for component in key_components:
            if component in workflow_prompt:
                print(f"   ✅ Contains: {component}")
            else:
                print(f"   ❌ Missing: {component}")
        
        # Test customer identification prompt
        customer_prompt = prompt_manager.get_customer_identification_prompt()
        print("\n✅ Customer identification prompt retrieved")
        
        # Check for key customer identification rules
        customer_rules = [
            "Team Systems is ALWAYS the supplier",
            "@teamsystems.net.au = Team Systems staff member",
            "Staff member is NOT the customer",
            "Customer = Sutton Tools (NOT David)"
        ]
        
        for rule in customer_rules:
            if rule in customer_prompt:
                print(f"   ✅ Contains rule: {rule}")
            else:
                print(f"   ❌ Missing rule: {rule}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing workflow components: {e}")
        return False

def test_prompt_manager_integration():
    """Test that the prompt manager properly integrates all components."""
    
    print("\n🔗 TESTING PROMPT MANAGER INTEGRATION")
    print("-" * 60)
    
    try:
        # Test available prompts
        available_prompts = prompt_manager.list_available_prompts()
        print(f"✅ Available prompts: {len(available_prompts)}")
        
        # Check for new workflow prompts
        workflow_prompts = [
            'enhanced_order_extraction',
            'workflow_system',
            'customer_identification'
        ]
        
        for prompt_name in workflow_prompts:
            if prompt_name in available_prompts:
                print(f"   ✅ {prompt_name} available")
                
                # Test prompt info
                info = prompt_manager.get_prompt_info(prompt_name)
                print(f"      Category: {info['category']}")
                print(f"      Parameters: {info['parameters']}")
            else:
                print(f"   ❌ {prompt_name} not available")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing prompt manager integration: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 Starting Team Systems Workflow Prompt Tests")
    
    results = []
    
    # Run tests
    results.append(("Customer Identification", test_customer_identification_scenarios()))
    results.append(("Workflow System", test_workflow_system_prompt()))
    results.append(("Prompt Manager Integration", test_prompt_manager_integration()))
    
    # Summary
    print("\n" + "=" * 80)
    print("TEST RESULTS SUMMARY")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Team Systems workflow prompts are working correctly.")
        print("\nKey improvements:")
        print("- ✅ Proper customer identification (internal staff vs external customers)")
        print("- ✅ Enhanced workflow context for all agents")
        print("- ✅ Clear business rules and decision trees")
        print("- ✅ Integration with existing prompt system")
    else:
        print("⚠️ Some tests failed. Please review the issues above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)