# Enhanced LLM Service

A comprehensive AI-powered service for email processing, PDF parsing, and order extraction with CRUD operations and intelligent analysis capabilities.

## 🌟 Features

### Email CRUD Operations
- **Create**: Store emails with AI-powered analysis and categorization
- **Read**: Retrieve email records with metadata and analysis
- **Update**: Modify email records with new information
- **Delete**: Mark emails as deleted (soft delete)
- **Search**: Semantic search across emails with filtering

### PDF Processing
- **Text Extraction**: Extract text content from PDF files using PyMuPDF
- **Structure Analysis**: AI-powered analysis of PDF document structure
- **Table Detection**: Identify and extract tabular data
- **Order Parsing**: Extract structured order information from PDFs

### AI-Powered Analysis
- **Email Categorization**: Classify emails by type (purchase_order, invoice, etc.)
- **Priority Assessment**: Determine urgency and importance
- **Entity Extraction**: Identify key entities (companies, PO numbers, products)
- **Content Summarization**: Generate concise summaries
- **Confidence Scoring**: Provide confidence levels for analysis

### Memory & Context
- **Persistent Storage**: ChromaDB-based vector storage for context
- **Semantic Search**: Find relevant historical data
- **Context Retrieval**: Enhance processing with similar examples

## 🚀 Quick Start

### Installation

```bash
# Install dependencies
pip install -r requirements.txt

# Ensure you have the required environment variables
# GEMINI_API_KEY=your_gemini_api_key
```

### Basic Usage

```python
from services.llm_service import LLMService

# Initialize service
llm_service = LLMService()

# Create email record with AI analysis
email_data = {
    "id": "email_001",
    "subject": "Purchase Order #PO12345",
    "sender": "<EMAIL>",
    "body": "Please find attached our purchase order...",
    "attachments": [{"filename": "PO12345.pdf", "data": pdf_bytes}]
}

email_id = llm_service.create_email_record(email_data)

# Search emails
results = llm_service.search_emails(
    query="purchase order safety equipment",
    filters={"category": "purchase_order"},
    limit=10
)

# Parse PDF for orders
order_data = llm_service.parse_pdf_order(pdf_bytes, "order.pdf")
```

## 📧 Email CRUD Operations

### Create Email Record

```python
email_data = {
    "id": "unique_email_id",
    "subject": "Email subject",
    "sender": "<EMAIL>",
    "timestamp": "2024-01-15T10:30:00Z",
    "body": "Email content...",
    "attachments": [...]
}

email_id = llm_service.create_email_record(email_data)
```

**AI Analysis Includes:**
- Category classification (purchase_order, invoice, quote, etc.)
- Priority assessment (critical, high, medium, low)
- Entity extraction (companies, PO numbers, products)
- Content summarization
- Action requirement detection

### Read Email Record

```python
email_record = llm_service.read_email_record(email_id)

if email_record:
    document = email_record["document"]
    metadata = email_record["metadata"]
    
    print(f"Category: {metadata['category']}")
    print(f"Priority: {metadata['priority']}")
    print(f"Contains Order: {metadata['contains_order']}")
```

### Update Email Record

```python
updates = {
    "metadata": {
        "status": "processed",
        "notes": "Order extracted successfully"
    },
    "analysis": "Additional processing notes"
}

success = llm_service.update_email_record(email_id, updates)
```

### Search Emails

```python
# Semantic search with filters
results = llm_service.search_emails(
    query="Brady safety equipment purchase order",
    filters={
        "category": "purchase_order",
        "sender": "<EMAIL>",
        "contains_order": True
    },
    limit=20
)

for result in results:
    print(f"Relevance: {result['relevance_score']:.2f}")
    print(f"Subject: {result['metadata']['subject']}")
    print(f"Category: {result['metadata']['category']}")
```

## 📄 PDF Processing

### Extract Text from PDF

```python
# From PDF bytes
pdf_text = llm_service.extract_text_from_pdf(pdf_bytes)
print(f"Extracted {len(pdf_text)} characters")
```

### Analyze PDF Structure

```python
analysis = llm_service.analyze_pdf_structure(pdf_bytes)

print(f"Document Type: {analysis['document_type']}")
print(f"Has Tables: {analysis['has_tables']}")
print(f"Has Order Lines: {analysis['has_order_lines']}")
print(f"Complexity: {analysis['complexity']}")
print(f"Confidence: {analysis['confidence']:.1%}")
```

### Parse PDF for Orders

```python
order_data = llm_service.parse_pdf_order(pdf_bytes, "order.pdf")

if order_data:
    customer = order_data["customer_details"]
    print(f"Customer ID: {customer['debtor_id']}")
    print(f"PO Number: {customer['customer_order_number']}")
    
    for line in order_data["order_lines"]:
        print(f"- {line['stockcode']}: {line['orderquantity']} units")
```

### Extract Tables from PDF

```python
tables = llm_service.extract_pdf_tables(pdf_bytes)

for i, table in enumerate(tables):
    print(f"Table {i+1} on page {table['page']}")
    print(f"Dimensions: {table['rows']}x{table['columns']}")
    
    for row in table['data']:
        print(" | ".join(row))
```

## 🔄 Comprehensive Processing

Process emails with PDF attachments in one operation:

```python
email_with_pdfs = {
    "id": "comprehensive_001",
    "subject": "New Purchase Order",
    "sender": "<EMAIL>",
    "body": "Please find attached our purchase order.",
    "attachments": [
        {
            "filename": "PO12345.pdf",
            "data": pdf_bytes,
            "mime_type": "application/pdf"
        }
    ]
}

results = llm_service.process_email_with_pdfs(email_with_pdfs)

print(f"Email Analysis: {results['email_analysis']['category']}")
print(f"PDFs Processed: {results['processing_summary']['analyzed_pdfs']}")
print(f"Orders Found: {results['processing_summary']['orders_found']}")

for order in results['extracted_orders']:
    print(f"Order from {order['source']}: {order['order_data']}")
```

## 🎯 AI Analysis Capabilities

### Email Analysis

The service automatically analyzes emails for:

- **Category Classification**:
  - `purchase_order` - Business purchase orders
  - `invoice` - Payment invoices
  - `packing_slip` - Shipping notifications
  - `quote` - Price quotations
  - `general_business` - Other business emails
  - `spam` - Unwanted emails
  - `personal` - Personal correspondence

- **Priority Assessment**:
  - `critical` - Urgent, time-sensitive
  - `high` - Important, requires attention
  - `medium` - Standard priority
  - `low` - Informational, low priority

- **Entity Extraction**:
  - Company names
  - Purchase order numbers
  - Product codes
  - Contact information
  - Dates and deadlines

### PDF Analysis

- **Document Type Detection**: Identifies purchase orders, invoices, catalogs, etc.
- **Structure Analysis**: Finds headers, sections, tables, and key areas
- **Content Complexity**: Assesses document complexity for processing
- **Table Detection**: Identifies tabular data for extraction

## 🔧 Configuration

### Environment Variables

```bash
# Required
GEMINI_API_KEY=your_gemini_api_key

# Optional
GEMINI_MODEL=gemini-2.5-flash  # Default model
LOG_LEVEL=INFO                 # Logging level
```

### Memory Storage

The service uses ChromaDB for persistent memory storage:

```python
# Default storage location
persist_directory = "./chroma_db"

# Memory is automatically managed
# - Email records stored with metadata
# - Order history for context
# - Semantic search capabilities
```

## 📊 Performance & Monitoring

### Logging

The service provides comprehensive logging:

```python
import logging
logging.basicConfig(level=logging.INFO)

# Logs include:
# - Processing times
# - Analysis results
# - Error handling
# - Performance metrics
```

### Metrics

Track processing performance:

```python
# Email processing metrics
- Analysis accuracy
- Processing time
- Memory usage
- Search relevance

# PDF processing metrics
- Text extraction success rate
- Table detection accuracy
- Order extraction rate
- Processing speed
```

## 🛠️ Advanced Usage

### Custom Analysis

Extend the analysis capabilities:

```python
# Custom email analysis
def custom_email_analysis(email_data):
    # Add custom business logic
    analysis = llm_service.analyze_email_content(email_data)
    
    # Enhance with custom rules
    if "urgent" in email_data["subject"].lower():
        analysis["priority"] = "critical"
    
    return analysis
```

### Batch Processing

Process multiple emails efficiently:

```python
def batch_process_emails(email_list):
    results = []
    
    for email in email_list:
        try:
            result = llm_service.process_email_with_pdfs(email)
            results.append(result)
        except Exception as e:
            logger.error(f"Failed to process email {email['id']}: {e}")
    
    return results
```

### Integration with Existing Systems

```python
# Integration with Gmail service
from services.gmail_service import GmailService

gmail = GmailService()
llm = LLMService()

# Process new emails
emails = gmail.fetch_emails_from_labels()
for email in emails:
    # Convert to LLM format
    email_data = {
        "id": email.id,
        "subject": email.subject,
        "sender": email.sender,
        "body": email.body,
        "attachments": email.attachments
    }
    
    # Process with AI
    results = llm.process_email_with_pdfs(email_data)
    
    # Handle results
    if results["processing_summary"]["orders_found"] > 0:
        # Process orders
        for order in results["extracted_orders"]:
            # Send to MYOB, etc.
            pass
```

## 🧪 Testing

Run the demonstration script:

```bash
# Run comprehensive demo
python demo_enhanced_llm.py

# Test individual components
python -c "
from services.llm_service import LLMService
llm = LLMService()
print('LLM Service initialized successfully')
"
```

## 🔍 Troubleshooting

### Common Issues

**ChromaDB Connection Issues**:
```bash
# Ensure ChromaDB directory exists and is writable
mkdir -p ./chroma_db
chmod 755 ./chroma_db
```

**PDF Processing Errors**:
```python
# Check PDF file integrity
try:
    text = llm_service.extract_text_from_pdf(pdf_bytes)
    if not text:
        print("PDF contains no extractable text")
except Exception as e:
    print(f"PDF processing error: {e}")
```

**Memory Issues with Large Files**:
```python
# Process large PDFs in chunks
def process_large_pdf(pdf_bytes):
    if len(pdf_bytes) > 10_000_000:  # 10MB
        logger.warning("Large PDF detected, processing may be slow")
    
    return llm_service.parse_pdf_order(pdf_bytes)
```

### Debug Mode

Enable detailed logging:

```python
import logging
logging.getLogger('services.llm_service').setLevel(logging.DEBUG)

# This will show:
# - Detailed AI prompts and responses
# - Processing steps
# - Performance metrics
# - Error details
```

## 📈 Performance Optimization

### Caching

Implement caching for repeated operations:

```python
from functools import lru_cache

@lru_cache(maxsize=100)
def cached_pdf_analysis(pdf_hash):
    return llm_service.analyze_pdf_structure(pdf_bytes)
```

### Batch Operations

Process multiple items together:

```python
# Batch email analysis
def batch_analyze_emails(emails):
    # Process multiple emails in single API call
    # Reduces API overhead
    pass
```

## 🔐 Security Considerations

- **API Keys**: Store securely in environment variables
- **PDF Content**: Validate PDF files before processing
- **Memory Storage**: Ensure ChromaDB directory has proper permissions
- **Data Privacy**: Consider data retention policies for stored emails

## 📚 API Reference

### LLMService Class

#### Email CRUD Methods
- `create_email_record(email_data: Dict) -> str`
- `read_email_record(email_id: str) -> Optional[Dict]`
- `update_email_record(email_id: str, updates: Dict) -> bool`
- `delete_email_record(email_id: str) -> bool`
- `search_emails(query: str, filters: Dict, limit: int) -> List[Dict]`

#### Analysis Methods
- `analyze_email_content(email_data: Dict) -> Dict`
- `analyze_pdf_structure(pdf_data: bytes) -> Dict`

#### PDF Processing Methods
- `extract_text_from_pdf(pdf_data: bytes) -> str`
- `parse_pdf_order(pdf_data: bytes, filename: str) -> Optional[Dict]`
- `extract_pdf_tables(pdf_data: bytes) -> List[Dict]`

#### Comprehensive Processing
- `process_email_with_pdfs(email_data: Dict) -> Dict`

## 🤝 Contributing

1. Follow existing code patterns and documentation
2. Add comprehensive error handling
3. Include logging for debugging
4. Write tests for new functionality
5. Update documentation for changes

## 📄 License

This enhanced LLM service is part of the TeamsysV0.1 project and follows the same licensing terms.