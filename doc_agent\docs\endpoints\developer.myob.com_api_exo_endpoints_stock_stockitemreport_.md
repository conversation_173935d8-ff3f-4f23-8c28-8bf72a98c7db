---
url: "https://developer.myob.com/api/exo/endpoints/stock/stockitemreport/"
title: "Stock Item Report"
---

[![](https://developer.myob.com/media/cms_page_media/23/myob_logo_small.png)](http://www.myob.com/)

# Stock Item Report

Return a stock item report as a PDF document.

**Date Released:** July 17th 2015 **Date Updated:** July 17th 2015

| URL | Supports |
| --- | --- |
| {URI}/stock/{id}/report | [GET](https://developer.myob.com/api/exo/endpoints/stock/stockitemreport/#GET) \| <br>[PUT](https://developer.myob.com/api/exo/endpoints/stock/stockitemreport/#PUT) \| <br>[POST](https://developer.myob.com/api/exo/endpoints/stock/stockitemreport/#POST) |
| {URI} is exo.api.myob.com when connecting to the cloud or the address of the machine hosting the API when connecting on premise. |

The elements list below details information for Stock Item Report. To view the descriptions for the elements you can either hover any attribute to reveal details [or click here to show all details inline.](https://developer.myob.com/api/exo/endpoints/stock/stockitemreport/#reveal)

#### Attribute Details

This endpoint has no attributes - it returns a Stock Item Details report for the specified stock item. Reports are generated using the **StockItem.clf** Clarity form.

#### Example json GET response

|     |     |
| --- | --- |
|  |  |