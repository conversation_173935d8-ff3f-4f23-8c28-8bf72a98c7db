#!/usr/bin/env python3
"""
Test script for the refactored customer name fuzzy matching.
"""

import logging
from debtor_lookup_service import DebtorLookupService

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

def test_customer_name_matching():
    """Test the new customer name matching functionality."""
    print("=== Testing Customer Name Fuzzy Matching ===\n")
    
    try:
        # Initialize the service
        service = DebtorLookupService(csv_path="docs/active_debtors.csv", threshold=80)
        
        # Test cases for customer name matching
        test_cases = [
            # Exact matches
            "A & L WINDOWS PTY LTD",
            "ADAIRS RETAIL GROUP",
            "CITY OF CASEY",
            
            # Partial matches (without suffixes)
            "A & L WINDOWS",
            "ADAIRS RETAIL",
            "ADAIRS",
            
            # Fuzzy matches (slight variations)
            "A&L WINDOWS",
            "ADAIRS GROUP",
            "CITY CASEY",
            
            # Case variations
            "a & l windows pty ltd",
            "Adairs Retail Group",
            "city of casey",
            
            # Common business name variations
            "ABSOLUTE STORAGE SYSTEMS",
            "ABSOLUTE STORAGE",
            "ALL STORAGE SYSTEMS",
            "ALL STORAGE",
            
            # Should not match
            "RANDOM COMPANY NAME",
            "XYZ CORPORATION",
        ]
        
        print("Testing direct customer name matching:\n")
        
        for test_name in test_cases:
            print(f"Testing: '{test_name}'")
            result = service.find_debtor_by_customer_name(test_name)
            
            if result:
                print(f"  ✅ Match found: {result['customer_name']}")
                print(f"     Debtor ID: {result['debtor_id']}")
                print(f"     Confidence: {result['confidence_score']}%")
                print(f"     Method: {result['match_method']}")
                print(f"     Match details: {result['matched_text']}")
            else:
                print(f"  ❌ No match found")
            print()
        
        # Test the search functionality as well
        print("\n=== Testing Search Functionality ===\n")
        
        search_queries = [
            "WINDOWS",
            "STORAGE",
            "ADAIRS",
            "CASEY",
        ]
        
        for query in search_queries:
            print(f"Searching for: '{query}'")
            results = service.search_customers(query, limit=3)
            
            if results:
                for i, result in enumerate(results, 1):
                    print(f"  {i}. {result['customer_name']} (ID: {result['debtor_id']}, Score: {result['confidence_score']}%)")
            else:
                print(f"  No results found")
            print()
        
        # Test email matching with customer name priority
        print("\n=== Testing Email Matching (with customer name priority) ===\n")
        
        email_tests = [
            ("<EMAIL>", "John Smith from Adairs"),
            ("<EMAIL>", "Casey Council"),
            ("<EMAIL>", "A&L Windows"),
            ("<EMAIL>", "ADAIRS RETAIL GROUP"),  # Should match by sender name
        ]
        
        for email, sender_name in email_tests:
            print(f"Testing email: {email} with sender: '{sender_name}'")
            result = service.find_debtor_by_email(email, sender_name)
            
            if result:
                print(f"  ✅ Match found: {result['customer_name']}")
                print(f"     Debtor ID: {result['debtor_id']}")
                print(f"     Confidence: {result['confidence_score']}%")
                print(f"     Method: {result['match_method']}")
                print(f"     Match details: {result['matched_text']}")
            else:
                print(f"  ❌ No match found")
            print()
        
    except Exception as e:
        print(f"Error during testing: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = test_customer_name_matching()
    if success:
        print("✅ All tests completed successfully!")
    else:
        print("❌ Tests failed!")