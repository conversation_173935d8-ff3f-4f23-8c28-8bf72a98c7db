"""
Enhanced email summary and analysis prompts.
Updated for v0.3.0 - Modular Architecture Edition with deterministic output
"""

from typing import Dict, Any
from .base_prompts import BasePrompts, PromptComponent


class SummaryPrompts(BasePrompts):
    """Enhanced email summary and analysis prompts."""
    
    # Email analysis system prompt
    EMAIL_ANALYSIS_SYSTEM = PromptComponent(
        name="email_analysis_system",
        content="""You are an email analysis specialist for Team Systems, expert at categorizing emails and determining required actions.""",
        category="summary_system",
        priority=10
    )
    
    # Email intent recognition - Enhanced with business rules
    INTENT_RECOGNITION = PromptComponent(
        name="intent_recognition",
        content="""EMAIL INTENT RECOGNITION:
- Prioritize accurate email_intent recognition using keywords:
  * "quote" or "quotation" → Quote Request
  * "price" or "pricing" → Price Inquiry  
  * "stock" or "availability" → Stock Check
  * "purchase order" or "PO" → Purchase Order
  * "general" or no clear intent → General Inquiry
- Look for urgency indicators: "urgent", "ASAP", "immediate"
- Identify if response is required and timeline
- Consider Team Systems e-commerce confirmations (Equip2go, Castor Solutions) as non-actionable""",
        category="summary_rules",
        priority=9
    )
    
    # Summary structure rules
    SUMMARY_STRUCTURE = PromptComponent(
        name="summary_structure",
        content="""SUMMARY STRUCTURE REQUIREMENTS:
- Provide concise, actionable summary (2-3 sentences max)
- Identify key stakeholders (sender, recipients, mentioned parties)
- Extract critical dates, deadlines, or timelines
- Note any attachments and their relevance
- Flag any special instructions or requirements
- Maintain consistent JSON structure for deterministic output""",
        category="summary_rules",
        priority=8
    )
    
    # Enhanced markdown formatting rules for JSON consistency
    MARKDOWN_RULES = PromptComponent(
        name="markdown_rules",
        content="""JSON STRUCTURE RULES:
- ALWAYS use the exact same JSON structure for consistency
- Use standardized field names: email_metadata, order_information, important_details, action_items
- Maintain consistent data types: strings for text, arrays for lists, objects for nested data
- Use null for missing values, not empty strings or undefined
- Follow ISO date format: YYYY-MM-DD for all dates
- Ensure all JSON is valid and properly escaped""",
        category="format_rules",
        priority=7
    )
    
    # Email summary JSON template
    EMAIL_SUMMARY_TEMPLATE = PromptComponent(
        name="email_summary_template",
        content="""Required JSON format for email summary:
{
    "summary": "Brief 2-3 sentence summary of email content",
    "intent": "Quote Request|Price Inquiry|Stock Check|Purchase Order|General Inquiry",
    "urgency": "Low|Medium|High|Critical",
    "action_required": "Response needed|Information only|Order processing|Follow-up required",
    "key_details": {
        "sender_type": "Customer|Colleague|Supplier|Unknown",
        "contains_order": true/false,
        "has_attachments": true/false,
        "deadline": "YYYY-MM-DD or null",
        "po_number": "PO number if found or null"
    },
    "next_steps": ["List of recommended actions"]
}""",
        category="template",
        priority=10
    )
    
    # Fixed markdown summary JSON template for consistency
    MARKDOWN_SUMMARY_TEMPLATE = PromptComponent(
        name="markdown_summary_template",
        content="""REQUIRED JSON FORMAT (use this EXACT structure every time):
{
  "email_metadata": {
    "subject": "email_subject_here",
    "from": "sender_email_here",
    "date": "YYYY-MM-DD",
    "body": "brief_email_body_summary"
  },
  "order_information": {
    "purchase_order_number": "PO_number_or_null",
    "supplier": "Team Systems",
    "customer": "customer_name_here",
    "order_date": "YYYY-MM-DD_or_null",
    "shipping_address": "full_shipping_address",
    "shipping_method": "shipping_method_or_pickup",
    "payment_terms": "payment_terms_or_null",
    "total_amount": "total_amount_or_null",
    "items": [
      {
        "quantity": 1,
        "item_number": "item_code",
        "description": "item_description",
        "price": "unit_price_or_null"
      }
    ]
  },
  "important_details": {
    "special_instructions": [
      "list_of_special_instructions"
    ],
    "attachments": [
      {
        "name": "attachment_filename",
        "relevance": "description_of_relevance"
      }
    ]
  },
  "action_items": [
    "list_of_required_actions"
  ]
}""",
        category="template",
        priority=10
    )
    
    @classmethod
    def build_email_summary_prompt(cls, email_body: str, subject: str, sender: str = "") -> str:
        """Build complete email summary prompt."""
        
        system_section = cls.EMAIL_ANALYSIS_SYSTEM.content
        json_enforcement = cls.JSON_ENFORCEMENT.content
        company_context = cls.COMPANY_CONTEXT.content
        email_rules = cls.EMAIL_RULES.content
        intent_recognition = cls.INTENT_RECOGNITION.content
        summary_structure = cls.SUMMARY_STRUCTURE.content
        json_template = cls.EMAIL_SUMMARY_TEMPLATE.content
        
        prompt = f"""{system_section}

{json_enforcement}

{company_context}

{email_rules}

{intent_recognition}

{summary_structure}

Email to analyze:
Subject: {subject}
From: {sender}
Content: {email_body}

{json_template}"""
        
        return prompt
    
    @classmethod
    def build_markdown_summary_prompt(cls, email_body: str, subject: str, sender: str, pdf_content: str = "") -> str:
        """Build deterministic markdown summary generation prompt with fixed JSON structure."""
        
        # Enhanced system section for consistency
        system_section = """You are a JSON document generator that creates structured summaries of email and document content.
You MUST respond with ONLY a valid JSON object in the EXACT format specified below.
CRITICAL: Use the exact same JSON structure every time for consistency."""
        
        # Add JSON enforcement for consistency
        json_enforcement = cls.JSON_ENFORCEMENT.content
        company_context = cls.COMPANY_CONTEXT.content
        markdown_rules = cls.MARKDOWN_RULES.content
        
        content_section = f"""Email Content:
Subject: {subject}
From: {sender}
Body: {email_body}"""
        
        if pdf_content:
            content_section += f"\n\nPDF Content:\n{pdf_content}"
        
        # Use the fixed template
        json_template = cls.MARKDOWN_SUMMARY_TEMPLATE.content
        
        prompt = f"""{system_section}

{json_enforcement}

{company_context}

{markdown_rules}

Analyze the following email and document content:

{content_section}

{json_template}

CRITICAL: You MUST use the exact JSON structure shown above. Do not deviate from this format.
DETERMINISTIC OUTPUT: Always use the same field names and structure for consistency."""
        
        return prompt
    
    @classmethod
    def build_intent_classification_prompt(cls, email_content: str) -> str:
        """Build prompt for email intent classification."""
        
        return f"""{cls.EMAIL_ANALYSIS_SYSTEM.content}

{cls.JSON_ENFORCEMENT.content}

{cls.INTENT_RECOGNITION.content}

Classify the intent of this email:

{email_content}

Return classification as JSON:
{{
    "primary_intent": "Quote Request|Price Inquiry|Stock Check|Purchase Order|General Inquiry|Order Confirmation",
    "confidence": 0.0-1.0,
    "secondary_intents": ["list of other possible intents"],
    "keywords_found": ["list of keywords that influenced classification"],
    "requires_action": true/false
}}"""