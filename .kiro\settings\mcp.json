{"mcpServers": {"fetch": {"command": "uvx", "args": ["mcp-server-fetch"], "env": {}, "disabled": false, "autoApprove": ["fetch"]}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp"], "disabled": false, "autoApprove": ["resolve-library-id", "get-library-docs"]}, "playwright": {"command": "npx", "args": ["@playwright/mcp@latest"], "disabled": false, "autoApprove": ["browser_navigate", "browser_close"]}}}