#!/usr/bin/env python3
"""
Test script to run comprehensive system health check.
"""
import asyncio
import json
import logging
from datetime import datetime
from services.system_monitor import SystemMonitor

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def main():
    """Run comprehensive system health check."""
    print("=" * 60)
    print("TeamsysV0.1 - Comprehensive System Health Check")
    print("=" * 60)
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Initialize system monitor
    monitor = SystemMonitor()
    
    try:
        # Run comprehensive health check
        print("🔍 Running comprehensive health check...")
        health_results = await monitor.comprehensive_health_check()
        
        print("\n📊 Health Check Results:")
        print("-" * 40)
        
        healthy_count = 0
        total_count = len(health_results)
        
        for service_name, result in health_results.items():
            status_icon = "✅" if result.healthy else "❌"
            status_text = "HEALTHY" if result.healthy else "UNHEALTHY"
            
            print(f"{status_icon} {service_name.upper()}: {status_text}")
            print(f"   Response Time: {result.response_time_ms:.0f}ms")
            
            if result.error_message:
                print(f"   Error: {result.error_message}")
            
            if result.details:
                for key, value in result.details.items():
                    print(f"   {key}: {value}")
            
            if result.healthy:
                healthy_count += 1
            
            print()
        
        # Overall system status
        overall_healthy = healthy_count == total_count
        overall_icon = "✅" if overall_healthy else "⚠️"
        overall_status = "HEALTHY" if overall_healthy else "DEGRADED"
        
        print("=" * 40)
        print(f"{overall_icon} OVERALL SYSTEM STATUS: {overall_status}")
        print(f"Services Healthy: {healthy_count}/{total_count}")
        
        # Get system metrics
        print("\n🖥️ Current System Metrics:")
        print("-" * 40)
        metrics = monitor.get_system_metrics()
        
        print(f"CPU Usage: {metrics.cpu_percent:.1f}%")
        print(f"Memory Usage: {metrics.memory_percent:.1f}%")
        print(f"Disk Usage: {metrics.disk_percent:.1f}%")
        print(f"Process Count: {metrics.process_count}")
        print(f"System Uptime: {metrics.uptime_seconds:.0f} seconds")
        
        # Get health summary with alerts
        print("\n🚨 Active Alerts:")
        print("-" * 40)
        health_summary = monitor.get_health_summary()
        alerts = health_summary.get('alerts', [])
        
        if alerts:
            for alert in alerts:
                severity_icon = "🔴" if alert['severity'] == 'high' else "🟡"
                print(f"{severity_icon} {alert['type'].upper()}: {alert['message']}")
        else:
            print("✅ No active alerts")
        
        # Export comprehensive report
        print("\n📋 Generating comprehensive health report...")
        report = monitor.export_health_report()
        
        # Save report to file
        report_filename = f"health_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_filename, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        print(f"📄 Health report saved to: {report_filename}")
        
        print("\n" + "=" * 60)
        print("System Health Check Complete!")
        print("=" * 60)
        
        return overall_healthy
        
    except Exception as e:
        print(f"❌ Error during health check: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)