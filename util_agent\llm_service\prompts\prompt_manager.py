"""
Centralized prompt management system for all agents.
"""

import os
import json
from typing import Dict, Any, List, Optional
from pathlib import Path
from .base_prompts import BasePrompts, PromptComponent
from .order_prompts import OrderPrompts
from .summary_prompts import SummaryPrompts
from .system_prompts import SystemPrompts
from .teamsystems_workflow_prompt import TeamSystemsWorkflowPrompts


class PromptManager:
    """Centralized manager for all prompts across agents."""
    
    def __init__(self):
        """Initialize prompt manager."""
        
        # Initialize prompt classes
        self.base = BasePrompts()
        self.order = OrderPrompts()
        self.summary = SummaryPrompts()
        self.system = SystemPrompts()
        self.workflow = TeamSystemsWorkflowPrompts()
    
    # Order Processing Prompts
    def get_order_extraction_prompt(self, content: str, context: str = "") -> str:
        """Get complete order extraction prompt."""
        return self.order.build_order_extraction_prompt(content, context)
    
    def get_order_validation_prompt(self, data: Dict[str, Any]) -> str:
        """Get order validation prompt."""
        return self.order.build_validation_prompt(data)
    
    # Summary Processing Prompts
    def get_email_summary_prompt(self, email_body: str, subject: str, sender: str = "") -> str:
        """Get email summary prompt with business context."""
        return self.summary.build_email_summary_prompt(email_body, subject, sender)
    
    def get_markdown_summary_prompt(self, email_body: str, subject: str, sender: str, pdf_content: str = "") -> str:
        """Get markdown summary generation prompt."""
        return self.summary.build_markdown_summary_prompt(email_body, subject, sender, pdf_content)
    
    def get_intent_classification_prompt(self, email_content: str) -> str:
        """Get email intent classification prompt."""
        return self.summary.build_intent_classification_prompt(email_content)
    
    # System Prompts
    def get_mistral_system_prompt(self) -> str:
        """Get MistralAI system prompt."""
        return self.system.get_mistral_system_prompt()
    
    def get_json_enforcement_prompt(self) -> str:
        """Get JSON enforcement prompt."""
        return self.system.get_json_enforcement_prompt()
    
    def enhance_prompt_for_json_strict(self, base_prompt: str) -> str:
        """Enhance prompt with strict JSON enforcement."""
        return self.system.enhance_prompt_for_json_strict(base_prompt)
    
    def enhance_prompt_for_json_fallback(self, base_prompt: str) -> str:
        """Enhance prompt for fallback mode."""
        return self.system.enhance_prompt_for_json_fallback(base_prompt)
    
    def get_error_recovery_prompt(self, original_prompt: str, error_details: str = "") -> str:
        """Get error recovery prompt."""
        return self.system.get_error_recovery_prompt(original_prompt, error_details)
    
    # Team Systems Workflow Prompts
    def get_workflow_system_prompt(self) -> str:
        """Get Team Systems workflow system prompt."""
        return self.workflow.get_workflow_system_prompt()
    
    def get_customer_identification_prompt(self) -> str:
        """Get customer identification rules prompt."""
        return self.workflow.get_customer_identification_prompt()
    
    def get_enhanced_order_extraction_prompt(self, content: str, sender_email: str = "", context: str = "") -> str:
        """Get enhanced order extraction prompt with Team Systems workflow context."""
        return self.workflow.build_enhanced_order_prompt(content, sender_email, context)
    
    # Utility Methods
    def get_prompt_by_name(self, prompt_name: str, **kwargs) -> str:
        """Get any prompt by name with parameters."""
        method_map = {
            'order_extraction': self.get_order_extraction_prompt,
            'enhanced_order_extraction': self.get_enhanced_order_extraction_prompt,
            'order_validation': self.get_order_validation_prompt,
            'email_summary': self.get_email_summary_prompt,
            'markdown_summary': self.get_markdown_summary_prompt,
            'intent_classification': self.get_intent_classification_prompt,
            'mistral_system': self.get_mistral_system_prompt,
            'json_enforcement': self.get_json_enforcement_prompt,
            'workflow_system': self.get_workflow_system_prompt,
            'customer_identification': self.get_customer_identification_prompt,
        }
        
        if prompt_name in method_map:
            return method_map[prompt_name](**kwargs)
        else:
            raise ValueError(f"Unknown prompt name: {prompt_name}")
    
    def list_available_prompts(self) -> List[str]:
        """List all available prompt names."""
        return [
            'order_extraction',
            'enhanced_order_extraction',
            'order_validation', 
            'email_summary',
            'markdown_summary',
            'intent_classification',
            'mistral_system',
            'json_enforcement',
            'workflow_system',
            'customer_identification'
        ]
    
    def get_prompt_info(self, prompt_name: str) -> Dict[str, Any]:
        """Get information about a specific prompt."""
        info_map = {
            'order_extraction': {
                'description': 'Extract order data from email/PDF content',
                'parameters': ['content', 'context'],
                'category': 'order_processing'
            },
            'enhanced_order_extraction': {
                'description': 'Enhanced order extraction with Team Systems workflow context and customer identification',
                'parameters': ['content', 'sender_email', 'context'],
                'category': 'teamsystems_workflow'
            },
            'order_validation': {
                'description': 'Validate extracted order data',
                'parameters': ['data'],
                'category': 'order_processing'
            },
            'email_summary': {
                'description': 'Generate email summary and intent classification',
                'parameters': ['email_body', 'subject', 'sender'],
                'category': 'email_analysis'
            },
            'markdown_summary': {
                'description': 'Generate markdown summary of email and PDF content',
                'parameters': ['email_body', 'subject', 'sender', 'pdf_content'],
                'category': 'email_analysis'
            },
            'intent_classification': {
                'description': 'Classify email intent and requirements',
                'parameters': ['email_content'],
                'category': 'email_analysis'
            },
            'mistral_system': {
                'description': 'System prompt for MistralAI configuration',
                'parameters': [],
                'category': 'system'
            },
            'json_enforcement': {
                'description': 'JSON enforcement prompt for structured output',
                'parameters': [],
                'category': 'system'
            },
            'workflow_system': {
                'description': 'Team Systems multi-agent workflow system overview and responsibilities',
                'parameters': [],
                'category': 'teamsystems_workflow'
            },
            'customer_identification': {
                'description': 'Customer identification rules for Team Systems workflow',
                'parameters': [],
                'category': 'teamsystems_workflow'
            }
        }
        
        return info_map.get(prompt_name, {'description': 'Unknown prompt', 'parameters': [], 'category': 'unknown'})
    
    def export_prompts_to_json(self, output_file: str = "prompts_export.json") -> None:
        """Export all prompts to JSON file for backup/analysis."""
        export_data = {
            'prompts': {}
        }
        
        for prompt_name in self.list_available_prompts():
            try:
                # Get sample prompt (with dummy data for parameters)
                if prompt_name == 'order_extraction':
                    sample = self.get_order_extraction_prompt("sample content")
                elif prompt_name == 'email_summary':
                    sample = self.get_email_summary_prompt("sample body", "sample subject")
                elif prompt_name in ['mistral_system', 'json_enforcement']:
                    sample = getattr(self, f"get_{prompt_name}_prompt")()
                else:
                    sample = "Prompt requires specific parameters"
                
                export_data['prompts'][prompt_name] = {
                    'content': sample,
                    'info': self.get_prompt_info(prompt_name)
                }
            except Exception as e:
                export_data['prompts'][prompt_name] = {
                    'error': str(e),
                    'info': self.get_prompt_info(prompt_name)
                }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)
        
        print(f"Prompts exported to {output_file}")


# Global prompt manager instance
prompt_manager = PromptManager()