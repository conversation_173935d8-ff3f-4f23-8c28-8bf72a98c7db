---
url: "https://developer.myob.com/api/exo/endpoints/debtor/debtorreport/"
title: "Debtor Transaction Report"
---

[![](https://developer.myob.com/media/cms_page_media/23/myob_logo_small.png)](http://www.myob.com/)

# Debtor Transaction Report

Return a Debtor transaction report as a PDF document.

**Date Released:** November 14th 2014 **Date Updated:** November 14th

| URL | Supports |
| --- | --- |
| {URI}/debtor/{debtorid}/transaction/{id}/report | [GET](https://developer.myob.com/api/exo/endpoints/debtor/debtorreport/#GET) \| <br>[PUT](https://developer.myob.com/api/exo/endpoints/debtor/debtorreport/#PUT) \| <br>[POST](https://developer.myob.com/api/exo/endpoints/debtor/debtorreport/#POST) |
| {URI} is exo.api.myob.com when connecting to the cloud or the address of the machine hosting the API when connecting on premise. |

The elements list below details information for Debtor Transaction Report. To view the descriptions for the elements you can either hover any attribute to reveal details [or click here to show all details inline.](https://developer.myob.com/api/exo/endpoints/debtor/debtorreport/#reveal)

#### Attribute Details

This endpoint has no attributes - it returns a Debtor transaction report for the specified transaction. Reports are generated using the **Invoice.clf** Clarity form. The EXO API supports the use of multiple Debtor invoice forms ( **Invoice1.clf**, **Invoice2.clf**, etc.) as specified by each Debtor account’s **Invoice file** field on the Details 2 tab.

**Note:** The EXO API does not support form file lists.

#### Example json GET response

|     |     |
| --- | --- |
|  |  |