"""
MistralAI Service Module

Provides integration with MistralAI's API for order processing and analysis.
Enhanced with proper JSON mode, retry logic, and improved error handling.
Updated to use latest MistralAI API features and best practices.
"""

import os
import logging
import re
import json
from typing import Dict, Any, Optional, List
from pydantic import BaseModel, Field
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type
from mistralai import Mistral

from ..core.base import BaseService
from ..core.exceptions import LLMProcessingError, ConfigurationError
from ..core.constants import MISTRAL_MODEL_NAME, MAX_TOKENS, TEMPERATURE, MISTRAL_REQUEST_TIMEOUT
from ..core.retry_utils import network_retry, api_retry, check_network_connectivity
from ..prompts.prompt_manager import prompt_manager

logger = logging.getLogger(__name__)

class MistralResponse:
    """Response object for compatibility with existing processors."""
    def __init__(self, text: str):
        self.text = text

class MistralConfig(BaseModel):
    """Configuration for MistralAI service."""
    api_key: str = Field(..., description="MistralAI API key")
    model: str = Field(default=MISTRAL_MODEL_NAME, description="Mistral model to use")
    max_tokens: int = Field(default=MAX_TOKENS, description="Maximum tokens for response")
    temperature: float = Field(default=TEMPERATURE, description="Temperature for generation")
    timeout: int = Field(default=MISTRAL_REQUEST_TIMEOUT, description="Request timeout in seconds")

class MistralService(BaseService):
    """Service for interacting with MistralAI's API with enhanced error handling and JSON mode."""
    
    def __init__(self, config: Optional[MistralConfig] = None):
        """Initialize Mistral service with configuration."""
        super().__init__()
        self.config = config or self._load_config()
        self._client = None
        
    def _load_config(self) -> MistralConfig:
        """Load configuration from environment variables."""
        api_key = os.getenv("MISTRAL_API_KEY")
        if not api_key:
            raise ConfigurationError("MISTRAL_API_KEY environment variable not set")
            
        return MistralConfig(
            api_key=api_key,
            model=os.getenv("MISTRAL_MODEL", MISTRAL_MODEL_NAME),
            max_tokens=int(os.getenv("MISTRAL_MAX_TOKENS", str(MAX_TOKENS))),
            temperature=float(os.getenv("MISTRAL_TEMPERATURE", str(TEMPERATURE)))
        )
    
    async def __aenter__(self):
        """Async context manager entry."""
        await self.initialize()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close()
    
    async def initialize(self) -> None:
        """Initialize the Mistral client."""
        self._client = Mistral(api_key=self.config.api_key)
        logger.info(f"Initialized Mistral service with model: {self.config.model}")
    
    async def close(self) -> None:
        """Close the client."""
        self._client = None
    
    async def health_check(self) -> bool:
        """Check if Mistral service is healthy."""
        try:
            if not self._client:
                await self.initialize()
            
            # Simple test call to check connectivity
            test_response = self._client.chat.complete(
                model=self.config.model,
                messages=[{"role": "user", "content": "test"}],
                max_tokens=1
            )
            return bool(test_response.choices)
        except Exception as e:
            logger.error(f"Mistral health check failed: {e}")
            return False
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type((Exception,))
    )
    async def generate_content_with_json_mode(self, prompt: str, use_json_mode: bool = True) -> 'MistralResponse':
        """Generate content using Mistral API with JSON mode and retry logic."""
        try:
            if not self._client:
                await self.initialize()
            
            # Prepare the request parameters
            request_params = {
                "model": self.config.model,
                "messages": [{"role": "user", "content": prompt}],
                "max_tokens": self.config.max_tokens,
                "temperature": self.config.temperature
            }
            
            # Add JSON mode if supported and requested
            if use_json_mode:
                request_params["response_format"] = {"type": "json_object"}
            
            response = self._client.chat.complete(**request_params)
            
            if response.choices:
                content = response.choices[0].message.content
                return MistralResponse(text=content)
            else:
                raise LLMProcessingError("No response choices returned from Mistral API")
                
        except Exception as e:
            logger.error(f"Error generating content with Mistral: {e}")
            raise LLMProcessingError(f"Failed to generate content: {str(e)}")
    
    @network_retry(max_attempts=3, min_wait=2, max_wait=10)
    async def generate_content(self, prompt: str) -> 'MistralResponse':
        """Generate content using Mistral API with enhanced JSON mode and retry logic."""
        try:
            if not self._client:
                await self.initialize()
            
            # Enhanced prompt with stronger JSON enforcement
            enhanced_prompt = self._enhance_prompt_for_json_strict(prompt)
            
            # Primary attempt with JSON mode
            response = self._client.chat.complete(
                model=self.config.model,
                messages=[{"role": "user", "content": enhanced_prompt}],
                max_tokens=self.config.max_tokens,
                temperature=self.config.temperature,
                response_format={"type": "json_object"}  # Force JSON mode
            )
            
            if response.choices:
                content = response.choices[0].message.content
                # Validate and clean the JSON response
                cleaned_content = self._clean_and_validate_json(content)
                return MistralResponse(text=cleaned_content)
            else:
                raise LLMProcessingError("No response choices returned from Mistral API")
                
        except Exception as e:
            logger.error(f"Error generating content with Mistral (JSON mode): {e}")
            # Fallback to non-JSON mode with aggressive cleaning
            try:
                logger.info("Retrying without JSON mode with aggressive cleaning...")
                fallback_prompt = self._enhance_prompt_for_json_fallback(prompt)
                
                response = self._client.chat.complete(
                    model=self.config.model,
                    messages=[{"role": "user", "content": fallback_prompt}],
                    max_tokens=self.config.max_tokens,
                    temperature=0.0,  # Even lower temperature for maximum determinism
                    random_seed=42    # Add deterministic seed
                )
                
                if response.choices:
                    content = response.choices[0].message.content
                    cleaned_content = self._aggressive_json_extraction(content)
                    return MistralResponse(text=cleaned_content)
                    
            except Exception as fallback_error:
                logger.error(f"Fallback also failed: {fallback_error}")
            
            raise LLMProcessingError(f"Failed to generate content after all attempts: {str(e)}")
    
    @network_retry(max_attempts=3, min_wait=2, max_wait=10)
    async def generate_content_flexible(self, prompt: str) -> str:
        """Generate natural language content without JSON mode enforcement."""
        try:
            if not self._client:
                await self.initialize()
            
            # Use the prompt as-is for natural conversation
            response = self._client.chat.complete(
                model=self.config.model,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=self.config.max_tokens,
                temperature=self.config.temperature
                # No response_format constraint - allow natural language
            )
            
            if response.choices:
                content = response.choices[0].message.content
                return content.strip()
            else:
                raise LLMProcessingError("No response choices returned from Mistral API")
                
        except Exception as e:
            logger.error(f"Error generating flexible content with Mistral: {e}")
            raise LLMProcessingError(f"Failed to generate flexible content: {e}")
    
    def _enhance_prompt_for_json_strict(self, prompt: str) -> str:
        """Enhance prompt with strict JSON enforcement using centralized prompt manager."""
        return prompt_manager.enhance_prompt_for_json_strict(prompt)
    
    def _enhance_prompt_for_json_fallback(self, prompt: str) -> str:
        """Fallback prompt enhancement using centralized prompt manager."""
        return prompt_manager.enhance_prompt_for_json_fallback(prompt)
    
    def _clean_and_validate_json(self, content: str) -> str:
        """Clean and validate JSON response with enhanced error handling."""
        if not content:
            raise LLMProcessingError("Empty response from Mistral API")
        
        # First, try to parse as-is (for clean JSON responses)
        try:
            json.loads(content.strip())
            return content.strip()
        except json.JSONDecodeError:
            pass
        
        # Apply cleaning strategies
        cleaned = self._aggressive_json_extraction(content)
        
        # Validate the cleaned JSON
        try:
            json.loads(cleaned)
            return cleaned
        except json.JSONDecodeError as e:
            logger.error(f"Failed to extract valid JSON from response: {e}")
            logger.error(f"Original content: {content[:500]}...")
            raise LLMProcessingError(f"Invalid JSON response from Mistral API: {str(e)}")
    
    def _aggressive_json_extraction(self, content: str) -> str:
        """Aggressively extract JSON from potentially messy content."""
        if not content:
            return content
        
        # Remove <think> tags and their content (case insensitive)
        content = re.sub(r'<think>.*?</think>', '', content, flags=re.DOTALL | re.IGNORECASE)
        
        # Remove markdown code blocks
        content = re.sub(r'```json\s*', '', content, flags=re.IGNORECASE)
        content = re.sub(r'```\s*$', '', content, flags=re.MULTILINE)
        
        # Remove common LLM prefixes
        prefixes_to_remove = [
            r'Here\'s the JSON.*?:',
            r'Based on.*?:',
            r'I\'ll analyze.*?:',
            r'The extracted.*?:',
            r'JSON response:',
            r'Response:',
            r'Output:',
        ]
        
        for prefix in prefixes_to_remove:
            content = re.sub(prefix, '', content, flags=re.IGNORECASE | re.DOTALL)
        
        # Strategy 1: Look for complete JSON objects with proper nesting
        json_pattern = r'\{(?:[^{}]|(?:\{(?:[^{}]|(?:\{[^{}]*\}))*\}))*\}'
        json_matches = re.findall(json_pattern, content, re.DOTALL)
        
        # Sort matches by length (longer ones more likely to be complete)
        json_matches.sort(key=len, reverse=True)
        
        for match in json_matches:
            try:
                # Validate it's proper JSON
                parsed = json.loads(match.strip())
                # Additional validation: ensure it has expected structure
                if isinstance(parsed, dict) and len(parsed) > 0:
                    return match.strip()
            except json.JSONDecodeError:
                continue
        
        # Strategy 2: Find JSON between first { and last }
        first_brace = content.find('{')
        last_brace = content.rfind('}')
        
        if first_brace != -1 and last_brace != -1 and last_brace > first_brace:
            potential_json = content[first_brace:last_brace + 1]
            try:
                parsed = json.loads(potential_json)
                if isinstance(parsed, dict) and len(parsed) > 0:
                    return potential_json
            except json.JSONDecodeError:
                pass
        
        # Strategy 3: Try to fix common JSON issues
        if first_brace != -1 and last_brace != -1:
            potential_json = content[first_brace:last_brace + 1]
            
            # Fix common issues
            potential_json = potential_json.replace('\n', ' ')  # Remove newlines
            potential_json = re.sub(r',\s*}', '}', potential_json)  # Remove trailing commas
            potential_json = re.sub(r',\s*]', ']', potential_json)  # Remove trailing commas in arrays
            
            try:
                parsed = json.loads(potential_json)
                if isinstance(parsed, dict) and len(parsed) > 0:
                    return potential_json
            except json.JSONDecodeError:
                pass
        
        # If all else fails, return the content between braces or original content
        if first_brace != -1 and last_brace != -1:
            return content[first_brace:last_brace + 1]
        
        return content.strip()
    
    def _clean_json_response(self, content: str) -> str:
        """Clean response content to extract valid JSON."""
        if not content:
            return content
        
        # Remove <think> tags and their content
        content = re.sub(r'<think>.*?</think>', '', content, flags=re.DOTALL)
        
        # Remove markdown code blocks
        content = re.sub(r'```json\s*', '', content)
        content = re.sub(r'```\s*$', '', content)
        
        # Try to extract JSON object
        import json
        
        # Strategy 1: Look for complete JSON objects
        json_pattern = r'\{(?:[^{}]|(?:\{[^{}]*\}))*\}'
        json_matches = re.findall(json_pattern, content, re.DOTALL)
        
        # Sort matches by length (longer ones more likely to be complete)
        json_matches.sort(key=len, reverse=True)
        
        for match in json_matches:
            try:
                # Validate it's proper JSON
                json.loads(match.strip())
                return match.strip()
            except json.JSONDecodeError:
                continue
        
        # Strategy 2: Find JSON between first { and last }
        first_brace = content.find('{')
        last_brace = content.rfind('}')
        
        if first_brace != -1 and last_brace != -1 and last_brace > first_brace:
            potential_json = content[first_brace:last_brace + 1]
            try:
                json.loads(potential_json)
                return potential_json
            except json.JSONDecodeError:
                pass
        
        # Return original content if no valid JSON found
        return content.strip()
    
    def _get_system_prompt(self) -> str:
        """Get system prompt for MistralAI using centralized prompt manager."""
        return prompt_manager.get_mistral_system_prompt()
    
    def _build_analysis_prompt(self, email_content: str) -> str:
        """Build prompt for email analysis."""
        return f"""Analyze this email and determine if it contains a purchase order or order-related information.

Email content:
{email_content}

Return a JSON object with:
- is_order: boolean indicating if this is an order
- confidence: float between 0 and 1
- order_type: type of order (purchase_order, sales_order, etc.)
- summary: brief summary of the order content"""
    
    def _build_extraction_prompt(self, email_content: str) -> str:
        """Build prompt for order data extraction."""
        return f"""Extract structured order data from this email.

Email content:
{email_content}

Return a JSON object with:
- customer_details: object with debtor_id (use 0 if unknown), customer_name, email
- purchase_order_number: string
- line_items: array of objects with item_code, description, quantity, unit_price
- delivery_address: object with street, city, state, postal_code, country
- special_instructions: string
- total_amount: float
- currency: string (default to "AUD")"""