# Product Overview

TeamsysV0.1 is an AI-powered email processing system that automates business purchase order handling. The system monitors Gmail labels, uses Google Gemini AI to extract structured data from emails and PDFs, validates orders against business rules, and creates sales orders in MYOB EXO.

## Core Functionality

- **Automated Email Processing**: Monitors Gmail labels for purchase orders and invoices from multiple suppliers
- **AI-Powered Data Extraction**: Uses Google Gemini to parse unstructured email content and PDFs into structured business data
- **MYOB EXO Integration**: Creates validated sales orders directly in the MYOB accounting system
- **Web Dashboard**: Modern Flask-based interface for email management, processing statistics, and manual operations
- **Multi-Agent Architecture**: Enhanced Universal Agent for comprehensive processing and Continuous Polling Agent for real-time monitoring
- **Context Memory**: ChromaDB-powered memory system for improved AI processing accuracy

## Key Business Value

- Eliminates manual data entry from email-based purchase orders
- Reduces processing errors through AI validation and structured data models
- Provides real-time processing with intelligent prioritization
- Offers comprehensive audit trails and processing analytics
- Supports multiple suppliers and order formats automatically
- Generates markdown summaries and MYOB-ready JSON payloads
- Provides batch processing capabilities for high-volume scenarios

## Target Users

Business operations teams who process high volumes of email-based purchase orders and need automated integration with MYOB EXO accounting systems. Particularly suited for companies dealing with suppliers like Brady, RSEA Safe, Woolworths, and other major distributors.

## System Capabilities

- **Multi-format Processing**: Handles various email formats and PDF attachment types
- **Intelligent Validation**: Business rule validation before order creation
- **Real-time Monitoring**: Continuous polling for new emails
- **Batch Operations**: Process multiple emails simultaneously
- **Status Tracking**: Visual indicators for processing status
- **File Management**: Automatic generation and storage of processing artifacts
- **Analytics**: Processing statistics and performance metrics