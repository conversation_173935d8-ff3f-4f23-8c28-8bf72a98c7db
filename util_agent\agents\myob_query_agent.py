"""
MYOB Query Agent - LLM-powered natural language MYOB querying.
Converts natural language queries into MYOB API calls and formats results.
"""
import json
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta

from myob_service import MyobService
from llm_service.main import LLMService

logger = logging.getLogger(__name__)


class MYOBQueryAgent:
    """LLM-powered natural language MYOB querying."""
    
    def __init__(self):
        """Initialize the query agent."""
        self.myob_service = MyobService()
        self.llm_service = LLMService()
        self.endpoint_catalog = self._load_endpoint_catalog()
        logger.info("MYOB Query Agent initialized")
    
    def _load_endpoint_catalog(self) -> Dict:
        """Load comprehensive endpoint catalog."""
        try:
            with open('endpoints_comprehensive.json', 'r') as f:
                catalog = json.load(f)
                logger.info(f"Loaded {len(catalog.get('endpoints', []))} MYOB endpoints")
                return catalog
        except Exception as e:
            logger.error(f"Failed to load endpoint catalog: {e}")
            return {"endpoints": []}
    
    async def process_natural_query(self, user_query: str) -> str:
        """
        Process natural language query and return results.
        
        Args:
            user_query: Natural language query from user
            
        Returns:
            Formatted response string
        """
        try:
            logger.info(f"Processing natural query: {user_query}")
            
            # Step 1: Classify query intent
            intent = await self._classify_query_intent(user_query)
            logger.debug(f"Query intent: {intent}")
            
            # Step 2: Map to appropriate endpoint(s)
            endpoints = self._map_intent_to_endpoints(intent)
            logger.debug(f"Mapped to {len(endpoints)} endpoints")
            
            # Step 3: Generate API calls
            api_calls = await self._generate_api_calls(user_query, endpoints)
            logger.debug(f"Generated {len(api_calls)} API calls")
            
            # Step 4: Execute calls and format results
            results = await self._execute_and_format(api_calls, user_query)
            
            logger.info("Natural query processing completed")
            return results
            
        except Exception as e:
            logger.error(f"Natural query processing failed: {e}")
            return f"Sorry, I encountered an error processing your query: {str(e)}"
    
    async def _classify_query_intent(self, query: str) -> Dict:
        """Classify user query into MYOB operation categories."""
        classification_prompt = f"""
        You are a helpful MYOB assistant. Analyze this MYOB query and determine what the user wants to know:
        
        Query: "{query}"
        
        Determine the primary intent from these categories:
        - customer_lookup: Finding customer/debtor information
        - order_search: Looking for sales orders
        - stock_inquiry: Product/inventory questions  
        - financial_data: Reports, transactions, balances
        - system_info: System status, configuration
        - data_analysis: Complex analysis requiring multiple endpoints
        
        Also identify any specific entities mentioned like:
        - Time periods, customer names, product codes, quantities, locations
        
        Respond in a helpful, conversational way explaining what you understand the user wants to know about their MYOB data.
        """
        
        try:
            # Use generate_content_flexible for conversational responses
            response = await self.llm_service.mistral_service.generate_content_flexible(classification_prompt)
            
            # Simple intent detection based on keywords
            intent_mapping = {
                "customer": "customer_lookup",
                "debtor": "customer_lookup",
                "order": "order_search",
                "sale": "order_search",
                "stock": "stock_inquiry",
                "product": "stock_inquiry",
                "inventory": "stock_inquiry",
                "financial": "financial_data",
                "report": "financial_data",
                "balance": "financial_data",
                "transaction": "financial_data",
                "system": "system_info",
                "status": "system_info",
                "analysis": "data_analysis",
                "compare": "data_analysis"
            }
            
            # Simple intent detection
            primary_intent = "customer_lookup"  # default
            query_lower = query.lower()
            for keyword, intent in intent_mapping.items():
                if keyword in query_lower:
                    primary_intent = intent
                    break
            
            return {
                "primary_intent": primary_intent,
                "confidence": 0.8,
                "secondary_intents": [],
                "extracted_entities": {},
                "reasoning": response,
                "llm_response": response
            }
            
        except Exception as e:
            logger.error(f"Query classification failed: {e}")
            return {
                "primary_intent": "general_inquiry",
                "confidence": 0.5,
                "secondary_intents": [],
                "extracted_entities": {},
                "reasoning": f"Classification failed: {str(e)}",
                "llm_response": "I'll help you find information in your MYOB system."
            }
    
    def _map_intent_to_endpoints(self, intent: Dict) -> List[Dict]:
        """Map classified intent to relevant MYOB endpoints."""
        intent_mapping = {
            "customer_lookup": ["debtor", "debtordebtorreport", "debtordebtortrans"],
            "order_search": ["salesorder", "salesorderline"],
            "stock_inquiry": ["stock", "stocklocation", "stockmovement"],
            "financial_data": ["debtordebtorreport", "debtordebtortrans"],
            "system_info": ["discovery", "schema"],
            "data_analysis": ["debtor", "salesorder", "stock"]  # Multiple endpoints
        }
        
        primary = intent.get("primary_intent", "")
        relevant_endpoint_names = intent_mapping.get(primary, ["debtor"])  # Default fallback
        
        # Get full endpoint details
        endpoint_details = []
        for endpoint_name in relevant_endpoint_names:
            for endpoint in self.endpoint_catalog.get("endpoints", []):
                if endpoint_name in endpoint.get("href", "").lower():
                    endpoint_details.append(endpoint)
        
        # If no endpoints found, add some common ones
        if not endpoint_details:
            for endpoint in self.endpoint_catalog.get("endpoints", [])[:5]:  # First 5 as fallback
                endpoint_details.append(endpoint)
        
        return endpoint_details
    
    async def _generate_api_calls(self, query: str, endpoints: List[Dict]) -> List[Dict]:
        """Generate specific API calls based on query and endpoints."""
        api_generation_prompt = f"""
        Generate MYOB API calls for this query:
        
        Query: "{query}"
        
        Available endpoints:
        {json.dumps(endpoints[:3], indent=2)}  # Limit to first 3 to avoid token limits
        
        Generate 1-3 specific API calls with:
        - method: HTTP method (GET, POST, etc.)
        - endpoint: endpoint name (debtor, salesorder, etc.)
        - parameters: query parameters if needed
        - description: what this call will retrieve
        
        For time-based queries, use appropriate date filters.
        For customer queries, use customer name or ID filters.
        
        Return JSON array:
        [
            {{
                "method": "GET",
                "endpoint": "debtor",
                "parameters": {{"q": "Woolworths"}},
                "description": "Search for Woolworths customer"
            }}
        ]
        """
        
        try:
            response = await self.llm_service.mistral_service.generate_content(api_generation_prompt)
            api_calls = json.loads(response)
            
            # Ensure it's a list
            if not isinstance(api_calls, list):
                api_calls = [api_calls]
            
            return api_calls
            
        except Exception as e:
            logger.error(f"API call generation failed: {e}")
            # Return a safe default call
            return [{
                "method": "GET",
                "endpoint": "debtor",
                "parameters": {},
                "description": "Get debtor information"
            }]
    
    async def _execute_and_format(self, api_calls: List[Dict], original_query: str) -> str:
        """Execute API calls and format results for user."""
        results = []
        
        for call in api_calls:
            try:
                result = await self._execute_single_call(call)
                results.append({
                    "call": call,
                    "success": True,
                    "data": result,
                    "count": len(result) if isinstance(result, list) else 1
                })
            except Exception as e:
                logger.error(f"API call failed: {e}")
                results.append({
                    "call": call,
                    "success": False,
                    "error": str(e),
                    "count": 0
                })
        
        # Format results using LLM
        formatting_prompt = f"""
        Format these MYOB API results for the user query: "{original_query}"
        
        Results:
        {json.dumps(results, indent=2, default=str)[:3000]}  # Limit size
        
        Provide a clear, human-readable summary that directly answers the user's question.
        Include:
        - Direct answer to the question
        - Relevant data in tables or lists where appropriate
        - Key statistics or summaries
        - Any important notes or limitations
        
        Format as plain text with clear sections and bullet points where helpful.
        """
        
        try:
            formatted_response = await self.llm_service.mistral_service.generate_content_flexible(formatting_prompt)
            return formatted_response
        except Exception as e:
            logger.error(f"Result formatting failed: {e}")
            return self._create_fallback_response(results, original_query)
    
    async def _execute_single_call(self, call: Dict) -> Any:
        """Execute a single API call using MyobService methods."""
        method = call.get("method", "GET").upper()
        endpoint = call.get("endpoint", "").lower()
        parameters = call.get("parameters", {})
        
        try:
            if endpoint == "debtor":
                if "q" in parameters:
                    # Search for specific debtor
                    return self.myob_service.search_debtor(parameters["q"])
                else:
                    # Get all debtors (paginated)
                    return self.myob_service.get_all_debtors_paginated()
            
            elif endpoint == "salesorder":
                return self.myob_service.get_all_sales_orders_paginated()
            
            elif endpoint == "debtordebtorreport":
                months_back = parameters.get("months_back", 12)
                return self.myob_service.get_debtor_report(months_back)
            
            elif endpoint == "debtordebtortrans":
                months_back = parameters.get("months_back", 12)
                debtor_id = parameters.get("debtor_id")
                return self.myob_service.get_debtor_transactions(debtor_id, months_back)
            
            else:
                # Generic endpoint call - try to map to available methods
                logger.warning(f"Unknown endpoint: {endpoint}")
                return {"message": f"Endpoint {endpoint} not implemented yet"}
                
        except Exception as e:
            logger.error(f"API call execution failed for {endpoint}: {e}")
            raise
    
    def _create_fallback_response(self, results: List[Dict], query: str) -> str:
        """Create a fallback response when LLM formatting fails."""
        response_lines = [
            f"Results for query: '{query}'",
            "=" * 50,
            ""
        ]
        
        for i, result in enumerate(results, 1):
            call = result.get("call", {})
            response_lines.append(f"{i}. {call.get('description', 'API Call')}")
            
            if result.get("success"):
                data = result.get("data", [])
                count = result.get("count", 0)
                response_lines.append(f"   ✅ Success: Found {count} records")
                
                # Show first few records if available
                if isinstance(data, list) and data:
                    response_lines.append("   Sample data:")
                    for item in data[:3]:  # First 3 items
                        if isinstance(item, dict):
                            # Show key fields
                            key_fields = ["id", "name", "debtorid", "customerordernumber"]
                            item_info = []
                            for field in key_fields:
                                if field in item:
                                    item_info.append(f"{field}: {item[field]}")
                            if item_info:
                                response_lines.append(f"     - {', '.join(item_info)}")
            else:
                error = result.get("error", "Unknown error")
                response_lines.append(f"   ❌ Failed: {error}")
            
            response_lines.append("")
        
        return "\n".join(response_lines)
    
    def get_example_queries(self) -> List[str]:
        """Get list of example queries users can try."""
        return [
            "Show me all orders from Woolworths in the last 30 days",
            "What's the current stock level for product IH051R2?",
            "Find all customers with outstanding balances over $1000",
            "Show me the top 10 customers by order volume this year",
            "What orders are pending for customer ID 6207?",
            "List all products that are out of stock",
            "Show me sales orders created today",
            "Find all customers in Western Australia",
            "What's the total value of orders this month?",
            "Show me all customers with recent transactions"
        ]
    
    def get_supported_operations(self) -> Dict[str, List[str]]:
        """Get list of supported operations by category."""
        return {
            "Customer Operations": [
                "Search customers by name",
                "Get customer details",
                "Find customers by location",
                "Get customer transaction history"
            ],
            "Order Operations": [
                "Search orders by customer",
                "Find orders by date range",
                "Get order details",
                "List recent orders"
            ],
            "Financial Operations": [
                "Get customer balances",
                "Find outstanding invoices",
                "Get transaction reports",
                "Calculate totals by period"
            ],
            "System Operations": [
                "Check system status",
                "List available endpoints",
                "Get data summaries",
                "Export information"
            ]
        }