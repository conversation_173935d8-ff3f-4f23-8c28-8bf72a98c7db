#!/usr/bin/env python3
"""
Quick test script to verify email processing initialization.
"""
import asyncio
import logging
from datetime import datetime

async def test_email_processing_init():
    """Test email processing initialization."""
    
    print("🧪 Testing Email Processing Initialization")
    print("=" * 50)
    
    try:
        # Test 1: Import main processor
        print("1. Testing imports...")
        from main_processor import EmailOrderProcessor
        print("   ✅ EmailOrderProcessor imported successfully")
        
        # Test 2: Initialize processor
        print("2. Testing processor initialization...")
        processor = EmailOrderProcessor()
        print("   ✅ EmailOrderProcessor initialized successfully")
        
        # Test 3: Check Gmail service
        print("3. Testing Gmail service...")
        if hasattr(processor, 'gmail_service') and processor.gmail_service:
            print("   ✅ Gmail service is available")
        else:
            print("   ❌ Gmail service not available")
        
        # Test 4: Check LLM service
        print("4. Testing LLM service...")
        if hasattr(processor, 'llm_service') and processor.llm_service:
            print("   ✅ LLM service is available")
        else:
            print("   ❌ LLM service not available")
        
        # Test 5: Check database service
        print("5. Testing database service...")
        if hasattr(processor, 'database_service') and processor.database_service:
            print("   ✅ Database service is available")
        else:
            print("   ❌ Database service not available")
        
        print("\n" + "=" * 50)
        print("🎉 All initialization tests passed!")
        print("💡 You can now run email processing with:")
        print("   python run_email_processing.py")
        print("   python teamsys_cli.py email --once --max-emails 3")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Initialization test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main entry point."""
    # Setup basic logging
    logging.basicConfig(level=logging.WARNING)
    
    try:
        success = asyncio.run(test_email_processing_init())
        exit(0 if success else 1)
    except Exception as e:
        print(f"❌ Test failed: {e}")
        exit(1)

if __name__ == "__main__":
    main()