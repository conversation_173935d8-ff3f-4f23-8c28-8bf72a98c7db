# Project Structure

## Architecture Pattern

The project follows a **layered service architecture** with clear separation of concerns:

- **Orchestrators**: High-level workflow coordination and system management
- **Services**: External API integrations (Gmail, Gemini AI, MYOB)
- **Agents**: Autonomous processing components
- **Utils**: Shared utilities, configuration, and data models

## Directory Structure

```
TeamsysV0.1/
├── orchestrators/          # System orchestration and workflows
│   ├── comprehensive_email_system.py    # Main system coordinator
│   ├── main_processor.py               # Core processing logic
│   ├── email_order_processor.py        # Email processing pipeline
│   ├── enhanced_email_processor.py     # Enhanced processing
│   └── email_dashboard.py              # Web dashboard
├── services/               # External API integrations
│   ├── gmail_service.py               # Gmail API operations
│   ├── llm_service.py                 # Gemini AI processing
│   ├── myob_service.py                # MYOB EXO integration
│   └── memory_client.py               # ChromaDB memory service
├── agents/                 # Autonomous processing agents
│   ├── enhanced_universal_agent.py    # Universal email processing
│   └── continuous_polling_agent.py    # Real-time monitoring
├── utils/                  # Shared utilities and configuration
│   ├── config.py                      # Configuration management
│   ├── models.py                      # Pydantic data models
│   ├── pdf_extractor.py               # PDF processing
│   └── setup.py                       # System setup utilities
├── templates/              # HTML templates for dashboard
├── markdown/               # Generated markdown summaries
├── myob/                   # Generated MYOB JSON payloads
├── chroma_db/              # ChromaDB vector database
└── backups/                # System backups
```

## Key Entry Points

- `run_restructured_system.py`: Main entry point with component selection
- Legacy entry points: Individual component files in root directory
- `demo_dashboard.py`: Demo mode with sample data

## Data Flow Pattern

1. **Gmail Service** → Fetches emails from configured labels
2. **LLM Service** → Processes content with Gemini AI
3. **MYOB Service** → Validates and creates sales orders
4. **Memory Client** → Stores context for AI processing
5. **Dashboard** → Provides web interface for management

## Configuration Management

- Environment variables in `.env` file
- System config in `system_config.json`
- Component configs: `universal_agent_config.yaml`, `polling_config.json`
- Centralized config class in `config.py` (root level)
- Validation methods for required settings

## Data Models

All data structures use **Pydantic models** for validation in `models.py`:
- `EmailData`: Email content and metadata
- `ExtractedOrder`: Structured order information
- `CustomerDetails`: Customer and debtor data
- `OrderLine`: Individual line items
- `ProcessedOrder`: Complete processed order
- `DeliveryAddress`: Shipping address structure

## File Naming Conventions

- **Services**: `{service_name}_service.py`
- **Agents**: `{agent_type}_agent.py`
- **Tests**: `test_{component_name}.py`
- **Demos**: `demo_{feature_name}.py`
- **Config files**: `{component}_config.{json|yaml}`
- **Generated files**: Timestamped format `DD-MM_HHMM_{description}.{md|json}`

## Import Patterns

- Root-level imports for core modules (`config`, `models`)
- Package-relative imports within orchestrators/services/agents
- Path manipulation in entry points for import resolution
- Configuration imported from root `config` module

## Error Handling

- Comprehensive logging with `colorlog` configured in `config.py`
- Service-level error handling with retries
- Graceful degradation for API failures
- Structured error messages with context

## Code Organization Principles

- **Single Responsibility**: Each module has a clear, focused purpose
- **Dependency Injection**: Services passed as parameters rather than imported
- **Configuration Centralization**: All settings managed through config classes
- **Data Validation**: Pydantic models ensure type safety and validation
- **Separation of Concerns**: Clear boundaries between data, business logic, and presentation